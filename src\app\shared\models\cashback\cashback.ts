import { Currency } from "../../enums/Currency";
import { ReferentialData } from "../referentialData";
import { Company } from "../company/company";
import { PaymentDetails } from "../company/paymentDetails";
import { CashbackStatus } from "../../enums/cashbackStatus";

export interface Cashback extends ReferentialData {
    id?: string;
    companyId?: string;
    company?: Company;
    code?: string;
    walletBallance: number;
    dynoAmount: number;
    feeAmount: number;
    currency?: Currency;
    netAmount: number;
    vatAmount: number;
    totalAmount: number;
    paymentDetailsId: string;
    paymentDetails?: PaymentDetails;
    ValidationDate?: Date;
    status?: CashbackStatus;
}

