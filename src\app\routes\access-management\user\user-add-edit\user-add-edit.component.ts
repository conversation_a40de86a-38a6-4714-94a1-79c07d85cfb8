import { Component, EventEmitter, Input, OnChanges, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import jwtDecode from 'jwt-decode';
import { MessageService, SelectItem } from 'primeng/api';
import { MultiSelectChangeEvent } from 'primeng/multiselect';
import { UserService } from 'src/app/services/User.service';
import { AuthService } from 'src/app/services/auth.service';
import { CompanyService } from 'src/app/services/company.service';
import { LocalStoreService } from 'src/app/services/local-store.service';
import { RoleManagementService } from 'src/app/services/role-management.service';
import { Gender } from 'src/app/shared/enums/Gender';
import { StatusCode } from 'src/app/shared/enums/StatusCode';
import { ClientURI, getClientURI } from 'src/app/shared/enums/clientURI';
import { Status } from 'src/app/shared/enums/status';
import { ForgetPasswordDTO } from 'src/app/shared/models/authentification/password/ForgetPasswordDTO';
import { tokenDTO } from 'src/app/shared/models/authentification/token';
import { Company } from 'src/app/shared/models/company/company';
import { Role } from 'src/app/shared/models/role/role';
import { UserDTO } from 'src/app/shared/models/user/UserDTO';
import { __values } from 'tslib';
import { UserType } from 'src/app/shared/enums/UserType';
import { DropdownChangeEvent, DropdownFilterOptions } from 'primeng/dropdown';
import { CountryCode, getCountries, getCountryCallingCode } from 'libphonenumber-js';
import { PhoneNumberValidator } from 'src/app/shared/validators/phoneNumberValidator';
import { Countries } from 'src/app/shared/enums/Countries';

@Component({
    selector: 'app-user-add-edit',
    templateUrl: './user-add-edit.component.html',
    styleUrls: ['./user-add-edit.component.scss']
})

export class UserAddEditComponent implements OnChanges {
    @Output() closeAddDialogEvent = new EventEmitter();
    @Output() userAdded = new EventEmitter<void>();
    @Input() userId: number = 0;
    @Input() selectedUser: UserDTO | null = null;
    @Input() display: boolean = false;
    UserFormGroup: FormGroup;
    GenderTypeDropdown: SelectItem[] = [];
    UserTypeDropdown: SelectItem[] = [];
    statusDropdown: SelectItem[] = [];
    selectedStatus: Status = Status.Active;
    mode: string = "";
    selectedGender: Gender = Gender.Male;
    filteredRoles: any[] = [];
    roles: Role[] = [];
    rolesByUserType: Role[] = [];
    companySuggestions: Company[] = [];
    selectedCompanyName: string = '';
    company: Company | null = null;
    selectedRoles: string[] = [];
    minDate: Date = new Date(1900, 0, 1);
    maxDate: Date = (() => {
        const minAge = 18;
        const maxDateCalc = new Date();
        maxDateCalc.setFullYear(maxDateCalc.getFullYear() - minAge);
        return maxDateCalc;
    })();
    isUserSuperAdmin = false;
    userType?: UserType;
    selectedUserType : UserType = UserType.SuperAdmin;
    countryCodes: any[] = [];
    selectedCountryCode: CountryCode | null = null;
    countryCallingCodes: { country: string, callingCode: string }[] = [];
    flagPath = 'assets/demo/images/flag/';
    includedCountryCodes: any;
    loading = false;
    companyId: string = "";
    constructor(private formBuilder: FormBuilder, private roleManagement: RoleManagementService,
        private messageService: MessageService, private userService: UserService, private localStorageService: LocalStoreService, private companyService: CompanyService, private authService: AuthService) {
        this.GenderTypeDropdown = this.enumToArray(Gender);
        this.statusDropdown = this.enumToArray(Status);
        this.includedCountryCodes = ['TN', 'DZ', 'MA', 'LY', 'SS', 'FR', 'MR', 'MT', 'EG', 'BH', 'KE', 'DJ'];

        this.UserFormGroup = this.formBuilder.group({
            id: [""],
            FullName: ["", Validators.required],
            gendertype: ["", Validators.required],
            email: ["", Validators.required],
            phonenumber: ['', [Validators.required]],
            countryCode: [""],
            datebirth: ["", Validators.required],
            status: ["", Validators.required],
            userType: ["", this.isUserSuperAdmin ? Validators.required : null],
            roles: ["", Validators.required],
            companyName: [, this.isUserSuperAdmin ? Validators.required : null],
        })
        this.UserFormGroup.get('countryCode')?.valueChanges.subscribe((countryCode: string) => {
            const countryC = this.getCountryCode('+' + countryCode) as CountryCode;
            const phoneNumberControl = this.UserFormGroup.get('phonenumber');
            if (phoneNumberControl) {
                phoneNumberControl.clearValidators();
                phoneNumberControl.setValidators([Validators.required, PhoneNumberValidator.phoneValidator(countryC)]);
                phoneNumberControl.updateValueAndValidity();
            }
        });
        //    this.UserFormGroup.get('countryCode')?.setValue('');
        this.getCountryCodes();
    }
    // switchToMonthView(event: any) {
    //     event.view = 'month';
    //   }
    setDefaultDate(event: any) {
        // Set the default date to 18 years ago

        const minDate = new Date();
        minDate.setFullYear(minDate.getFullYear() - 18);
        this.UserFormGroup.get('datebirth')?.setValue(minDate);

    }
    isNotANumber(value: any): boolean {
        return isNaN(value);
    }
    isSaveDisabled(): boolean {
        return this.UserFormGroup.invalid;
    }
    //get TN from +216
    getCountryCode(countryValue: string): string | undefined {
        for (const countryCode of Object.values(Countries)) {
            if (countryCode === countryValue) {
                return Object.keys(Countries).find(key => Countries[key as keyof typeof Countries] === countryValue);
            }
        }
        return undefined;
    }

    getCountryValue(countryCode: string): string | undefined {
        const country = Object.entries(Countries).find(([key, value]) => value === countryCode);
        return country ? country[1] : undefined;
    }


    onCountryCodeChange(event: { originalEvent: Event, value: any }): void {

        this.UserFormGroup.controls['countryCode'].setValue(event.value);
    }

    getCountryCodes(): void {
        const countries = getCountries();
        this.countryCodes = countries
            .filter(country => this.includedCountryCodes.includes(country))
            .map(country => {
                const countryCode = getCountryCallingCode(country);
                const flagUrl = `${this.flagPath}${country.toUpperCase()}.webp`;
                return { label: `+${countryCode}`, value: countryCode, flagUrl: flagUrl };
            });
    }

    enumToArray(enumerable: any): SelectItem[] {
        return Object.keys(enumerable)
            .filter(key => !isNaN(Number(enumerable[key])))
            .map(key => ({ label: key, value: enumerable[key] }));
    }

    ngOnInit() {
        let token = this.localStorageService.getData("Token");
        if (token != undefined) {
           
            let decodedToken: tokenDTO = jwtDecode(token);
            let userType = decodedToken.UserType;
            this.companyId = decodedToken.Company;
            if (userType == UserType[UserType.SuperAdmin].toString()) {
                this.isUserSuperAdmin = true;
                this.userType = UserType.SuperAdmin
            }
            if (userType == UserType[UserType.Company].toString()) {
                this.isUserSuperAdmin = false;
                this.userType = UserType.Company
            }
            if (userType == UserType[UserType.ShopOwner].toString()) {
                this.isUserSuperAdmin = false;
                this.userType = UserType.ShopOwner
            }
            this.getAvailableUserTypes();
            this.getActiveCompany();
        }

        this.roleManagement.getAllRoles().subscribe({

            next: __values => {
                this.roles = __values.objectValue;
                this.rolesByUserType = this.roles.filter(x => x.userType == this.selectedUserType && x.status == Status.Active && x.companyId == this.companyId)

            },
            error: err => {
                if (err.status === 0) {
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Network Error', detail: 'Server not reachable or no internet connection.' });
                } else {
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Server Error', detail: err.exceptionMessage });
                }
            },
            complete: () => console.log("get all Roles donne ☺")
        });
        // this.UserFormGroup.get('countryCode')?.setValue('');
        this.selectedUserType = UserType.SuperAdmin;
    }

    getRoles() {
        this.roleManagement.getAllRoles().subscribe({

            next: __values => {
                this.roles = __values.objectValue;
                this.rolesByUserType = this.roles.filter(x => x.userType == this.selectedUserType && x.status == Status.Active && x.companyId == this.companyId)

            },
            error: err => {
                if (err.status === 0) {
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Network Error', detail: 'Server not reachable or no internet connection.' });
                } else {
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Server Error', detail: err.exceptionMessage });
                }
            },
            complete: () => console.log("get all Roles donne ☺")
        });
    }

    getAvailableUserTypes() {
        if (this.userType == UserType.SuperAdmin) {
            this.UserTypeDropdown = [
                {
                    label: UserType[UserType.SuperAdmin].toString(),
                    value: UserType.SuperAdmin
                },
                {
                    label: UserType[UserType.Company].toString(),
                    value: UserType.Company
                },              
                {
                    label: UserType[UserType.ShopOwner].toString(),
                    value: UserType.ShopOwner
                }
            ]
        }
        if (this.userType == UserType.Company) {
            this.UserTypeDropdown = [
                {
                    label: UserType[UserType.Company].toString(),
                    value: UserType.Company
                }
            ]
        }
        if (this.userType == UserType.ShopOwner) {
            this.UserTypeDropdown = [
                {
                    label: UserType[UserType.ShopOwner].toString(),
                    value: UserType.ShopOwner
                }
            ]
        }
    }

    ngOnChanges() {

        if (this.selectedUser) {
            this.mode = 'edit';
            this.UserFormGroup.reset();
            this.UserFormGroup.get("email")?.disable();
            this.UserFormGroup.get('email')?.clearValidators();
            this.populateFormFromUser(this.selectedUser);
            this.selectedUserType = this.selectedUser.userType;
        } else {

            this.UserFormGroup.reset();
            this.mode = 'add';
            this.UserFormGroup.get("countryCode")?.setValue('');
            this.UserFormGroup.get("email")?.enable();
            this.UserFormGroup.get('email')?.updateValueAndValidity();
            this.selectedUserType = UserType.SuperAdmin;

        }

        console.log(this.selectedUserType);
        this.getActiveCompany();
        this.getRoles();
    }

    populateFormFromUser(user: UserDTO) {
        if (user.id) {
            this.companyService.getCompanyById(user.companyId).subscribe(
                (response) => {
                    if (response.objectValue)
                        this.company = response.objectValue;
                    this.selectedRoles = []
                    this.selectedCompanyName = this.company?.id != null ?  this.company?.id : 'hiiii';
                    this.selectedRoles = this.selectedRoles.concat(user.roles.map(element => element.id));
                    this.UserFormGroup.setValue({
                        id: user.id,
                        FullName: user.fullName,
                        gendertype: user.gender,
                        email: user.email,
                        phonenumber: user.phoneNumber,
                        // countryCode: user.countryCode,
                        countryCode: user.countryCode ? user.countryCode.replace("+", "") : '', // Ensure countryCode is properly set
                        datebirth: new Date(user.dateOfBirth),
                        userType: user.userType,
                        companyName: this.selectedCompanyName,    
                        roles: this.selectedRoles,
                        status: user.status,                                         
                    });
                    this.rolesByUserType = this.roles.filter(x => x.userType == user.userType && x.status == Status.Active);              
                }
            )


        }
    }

    closeAddDialog() {
        this.UserFormGroup.reset();
        this.closeAddDialogEvent.emit(false);
        
    }

    onGenderTypeChange(event: any) {
        this.selectedGender = event.value;
    }

    saveUser() {
        // Mark all form controls as touched
        Object.values(this.UserFormGroup.controls).forEach(control => {
            control.markAsTouched();
        });
        Object.keys(this.UserFormGroup.controls).forEach(key => {
            const control = this.UserFormGroup.controls[key];
            if (control.invalid) {
                console.log(`Invalid attribute: ${key}`);
            }
        });
        if (this.isSaveDisabled()) {
            this.loading = false;
            return;
        }
        this.loading = true;
        const selectedRoleIds: string[] = this.UserFormGroup.get('roles')!.value;
        const selectedRoles: Role[] = selectedRoleIds.map(roleId => ({ id: roleId } as Role));
        const userDTO: UserDTO = {
            id: this.UserFormGroup.get('id')!.value,
            fullName: this.UserFormGroup.get('FullName')!.value,
            gender: this.UserFormGroup.get('gendertype')!.value,
            phoneNumber: this.UserFormGroup.get('phonenumber')!.value,
            countryCode: this.UserFormGroup.get('countryCode')!.value,
            email: this.UserFormGroup.get('email')!.value,
            dateOfBirth: this.UserFormGroup.get('datebirth')!.value.toISOString(),
            macAddress: '',
            userType: this.isUserSuperAdmin ? this.UserFormGroup.get('userType')!.value : this.userType,
            roles: selectedRoles,
            status: this.selectedStatus,
            companyId: this.selectedCompanyName,
        };

        userDTO.countryCode = '+' + userDTO.countryCode;

        if (this.mode === 'add') {
            this.userService.saveUser(userDTO).subscribe({
                error: err => {
                    this.loading = false;
                    if (err.status === 0) {
                        this.messageService.add({ key: 'toast', severity: 'error', summary: 'Network Error', detail: 'Server not reachable or no internet connection.' });
                    } else {
                        this.messageService.add({ key: 'toast', severity: 'error', summary: 'Server Error', detail: err.exceptionMessage });
                    }
                },
                next: (response) => {
                    var data: ForgetPasswordDTO = {
                        email: userDTO.email,
                        clientURI: getClientURI()

                    }
                    if (response.statusCode == 302) {
                        this.userAdded.emit();
                        this.loading = false;
                        this.closeAddDialogEvent.emit(false);
                        this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error', detail: response.exceptionMessage });
                    } else if(response.statusCode == 302) {
                        this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error', detail: response.exceptionMessage });
                        this.loading = false;
                    }
                    else {

                        this.authService.ForgetPassword(data).subscribe({
                            error: err => {
                                console.log('err', err);
                                this.loading = false;
                            },
                            complete: () => {
                            }

                        });
                        this.userAdded.emit();
                        this.closeAddDialogEvent.emit(false);
                        this.messageService.add({ key: 'toast', severity: 'success', summary: 'User added successfully', detail: '' });
                    }
                },
                complete: () => {
                    this.userAdded.emit;
                    this.closeAddDialogEvent.emit(false);
                    this.loading = false;
                }
            });
            
        }
        else if (this.mode === 'edit') {

            this.userService.updateUser(userDTO).subscribe({
                error: err => {
                    this.loading = false;
                    if (err.status === 0) {
                        this.messageService.add({ key: 'toast', severity: 'error', summary: 'Network Error', detail: 'Server not reachable or no internet connection.' });
                    } else {
                        this.messageService.add({ key: 'toast', severity: 'error', summary: 'Server Error', detail: err.exceptionMessage });
                    }
                },
                complete: () => {
                    this.messageService.add({ key: 'toast', severity: 'success', summary: 'User Updated successfully', detail: '' })
                    this.userAdded.emit();
                    this.closeAddDialogEvent.emit(false);
                    this.loading = false;
                }
            });

        }
        this.UserFormGroup.reset();

        // this.userService.getAllUsers;
    }

    filterRole(event: any) {
        const filtered: any[] = [];
        const query = event.query;
        for (let i = 0; i < this.roles.length; i++) {
            const Role = this.roles[i];
            console.log("==========", Role.name);

            if (Role.name.toLowerCase().indexOf(query.toLowerCase()) == 0) {
                filtered.push(Role.name);
            }
        }

        this.filteredRoles = filtered;

    }

    onCompanySelect(event: any) {
        this.selectedCompanyName = event.value;
    }



    searchCompanies(event: any) {
        this.companyService.getAllActiveCompanies(this.userType ?? UserType.SuperAdmin).subscribe((response) => {
            if (response.statusCode == StatusCode.Ok) {
                const query = event.query.toLowerCase();
                // this.companySuggestions = response.objectValue
                //     .filter(company => company.name && company.name.toLowerCase().includes(query))
                //     .map(company => ({
                //         name: company.name,
                //         id: company.id
                //     }));

                
            }
        });
    }

    getActiveCompany() {
        this.companyService.getAllActiveCompanies(this.selectedUserType ?? UserType.SuperAdmin).subscribe((response) => {
            if (response.statusCode == StatusCode.Ok) {
                // this.companySuggestions = response.objectValue
                //     .map(company => ({
                //         label: company.name,
                //         value: company
                //     }));

                this.companySuggestions = response.objectValue;
            }
        });
    }

    resetFunction(options: DropdownFilterOptions) {
        options.reset?.();
        this.selectedCompanyName = '';
    }

    customFilterFunction(event: KeyboardEvent, options: DropdownFilterOptions) {
        options.filter?.(event)
    }

    removeRole(roleToRemove: any): void {
        const currentRoles = this.UserFormGroup.get('roles')?.value as any[];
        const updatedRoles = currentRoles.filter(role => role.id !== roleToRemove.id);
        this.UserFormGroup.get('roles')?.setValue(updatedRoles);
    }

    onUserTypeChange(e: DropdownChangeEvent) {
        this.selectedUserType = e.value;
        this.UserFormGroup.get('roles')?.reset()
        this.rolesByUserType = this.roles.filter(x => x.userType == e.value && x.status == Status.Active)
        this.getActiveCompany();
    }

    onStatusChange(event: any) {
        this.selectedStatus = event.value;
    }
}
