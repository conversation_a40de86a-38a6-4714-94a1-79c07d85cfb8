import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { ResponseAPI } from "../shared/models/ResponseAPI";
import { environment } from "environment";

@Injectable({
    providedIn: 'root'
})
export class RequestService {

    constructor(private http: HttpClient) {

    }
    private transferRequests: any[] = [
        { id: 1, enterpriseName: 'Company A', email: '<EMAIL>', amountRequested: 1000 },
        { id: 2, enterpriseName: 'Company B', email: '<EMAIL>', amountRequested: 2000 },
        { id: 3, enterpriseName: 'Company C', email: '<EMAIL>', amountRequested:3000},
        { id: 4, enterpriseName: 'Company D', email: '<EMAIL>', amountRequested:4000},
        { id: 5, enterpriseName: 'Company E', email: '<EMAIL>', amountRequested:550}
    ];
    getTransferRequests() {
        return this.transferRequests;
    }
}
