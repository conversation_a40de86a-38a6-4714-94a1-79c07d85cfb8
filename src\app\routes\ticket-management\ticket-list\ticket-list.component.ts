import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { ConfirmationService, FilterMetadata, SelectItem } from 'primeng/api';
import { Status } from 'src/app/shared/enums/status';
import { MessageService } from 'primeng/api';
import { Ticket } from 'src/app/shared/models/ticket/ticket';
import { TicketService } from 'src/app/services/ticket.service';
import { WalletType } from 'src/app/shared/enums/wallet-type';
import { PeriodType } from 'src/app/shared/enums/PeriodType';

interface expandedRows {
    [key: string]: boolean;
}
@Component({
    selector: 'app-ticket-list',
    templateUrl: './ticket-list.component.html',
    styleUrls: ['./ticket-list.component.scss']
})
export class TicketListComponent {

    tickets: Ticket[] = [];
    Status = Status;
    displayAddEditDialog: boolean = false;
    displayDeleteDialog: boolean = false;
    selectedTicketId: string = "";
    rowGroupMetadata: any;
    selectedTicket: Ticket | null = null;
    expandedRows: expandedRows = {};
    activityValues: number[] = [0, 100];
    isExpanded: boolean = false;
    idFrozen: boolean = false;
    loading: boolean = true;
    StatusList = this.enumToArray(Status).map(item => ({ label: item.label, value: item.label }));
    pageSize: number = 5;
    pageNumber: number = 0;
    first!: number;
    totalRecords: number = 1;
    ticketTypeList = this.enumToArray(WalletType).map(item => ({ label: item.label, value: item.label }));
    PeriodTypeList = this.enumToArray(PeriodType).map(item => ({ label: item.label, value: item.label }));
    sortBy?: string | string[] | null | undefined;
    sortDirection: number = 1;
    @ViewChild('filter') filter!: ElementRef;

    constructor(private ticketService: TicketService,
        private confirmationService: ConfirmationService,
        private messageService: MessageService,) { }

    //   ngOnInit() {
    //     this.loadTickets();
    //   }
    enumToArray(enumerable: any): SelectItem[] {
        return Object.keys(enumerable)
            .filter(key => !isNaN(Number(enumerable[key])))
            .map(key => ({ label: key, value: enumerable[key] }));
    }
    getStatusString(statusValue: number): string {
        return Status[statusValue];
    }
    getTicketTypeString(ticketTypeValue: number): string {
        return WalletType[ticketTypeValue];
    }
    getPeriodTypeString(periodTypeValue: number): string {
        return PeriodType[periodTypeValue];
    }
    loadTickets(filters?: { [s: string]: FilterMetadata } | undefined) {
        // this.ticketService.getAllTickets().subscribe(response => {
        //   if (response.objectValue) {
        //     this.tickets = response.objectValue;
        //     console.log("reponse tickets", response.objectValue);
        //     this.loading = false;
        //   }
        // },
        //   (error) => {
        //   }
        // );
        this.pageNumber = (this.first / this.pageSize) + 1;
        const sortBy = this.sortBy as string;
        const sortDirection = this.sortDirection;
        filters = filters || {};
        this.ticketService.getAllPaginationTickets(this.pageSize, this.pageNumber,sortBy, sortDirection, filters).subscribe(response => {
            if (response.body && response.body.objectValue) {
                this.tickets = response.body.objectValue;
                const xPaginationHeader = response.headers.get('x-pagination');
                if (xPaginationHeader) {
                    const xPagination = JSON.parse(xPaginationHeader);
                    this.totalRecords = xPagination.TotalCount;
                } else {
                    console.error('x-pagination header not found in the response');
                }
                console.log("reponse tickets", response.body.objectValue);
                this.loading = false;
            }
        },
            (error) => {
            }
        );
    }

    onSort() {
        //this.updateRowGroupMetaData();
    }


    onGlobalFilter(table: Table, event: Event) {
        table.filterGlobal((event.target as HTMLInputElement).value, 'contains');
    }

    clear(table: Table) {
        table.clear();
        this.filter.nativeElement.value = '';
    }
    showDeleteConfirmation(ticket: Ticket) {
        if (ticket.ticketId !== undefined) {
            this.selectedTicketId = ticket.ticketId;
            this.displayDeleteDialog = true;
        }
        else {
            console.log('nope');

        }
    }
    confirmDelete() {
        if (this.selectedTicketId !== null) {
            this.ticketService.deleteTicket(this.selectedTicketId).subscribe(
                (response) => {

                    if (response.statusCode == 200) {
                        this.displayDeleteDialog = false;
                        this.messageService.add({ key: 'toast', severity: 'success', summary: 'Ticket deleted successfully', detail: response.exceptionMessage });
                        this.loadTickets();
                    }
                    else {
                        this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Delete Ticket', detail: response.exceptionMessage });

                    }
                },
                (error) => {
                    this.displayDeleteDialog = false;

                }
            );
        }
    }


    displayTicketDialog(ticket: Ticket | null) {
        console.log("tiiiicket", this.tickets)

        this.selectedTicket = ticket;
        this.displayAddEditDialog = true;
        return this.displayAddEditDialog;
    }
    closeAddEditDialogEvent(e: Event) {
        this.displayAddEditDialog = false;
    }
    onTicketAdded() {
        this.loadTickets();
    }
    onElementDeleted() {
        this.loadTickets();
    }
    onCancelDelete() {
        this.displayDeleteDialog = false;
    }

    lazyLoadtickets(event: TableLazyLoadEvent) {
        this.first = event.first || 0;
        this.sortBy = event.sortField ?? '';
        this.sortDirection = event.sortOrder as number;
        this.loadTickets(event.filters as { [s: string]: FilterMetadata } | undefined);

    }
}
