# Script PowerShell pour les tests manuels de l'application Angular Dyno
# Compatible Windows avec Docker Desktop

param(
    [switch]$SkipBuild,
    [switch]$RunTests,
    [switch]$Cleanup
)

# Configuration des couleurs
$Host.UI.RawUI.ForegroundColor = "White"

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Test-DockerAvailable {
    Write-Status "Vérification de Docker..."
    
    try {
        $dockerVersion = docker --version
        if ($LASTEXITCODE -ne 0) {
            throw "Docker command failed"
        }
        
        docker info | Out-Null
        if ($LASTEXITCODE -ne 0) {
            throw "Docker is not running"
        }
        
        Write-Success "Docker est disponible et en cours d'exécution"
        Write-Host "Version: $dockerVersion" -ForegroundColor Gray
        return $true
    }
    catch {
        Write-Error "Docker n'est pas disponible ou n'est pas en cours d'exécution"
        Write-Host "Veuillez installer Docker Desktop et vous assurer qu'il est démarré" -ForegroundColor Yellow
        return $false
    }
}

function Stop-ExistingServices {
    Write-Status "Arrêt des services existants..."
    
    try {
        docker-compose down -v --remove-orphans 2>$null
        Write-Success "Services arrêtés"
    }
    catch {
        Write-Warning "Aucun service à arrêter ou erreur lors de l'arrêt"
    }
}

function Build-DockerImages {
    if ($SkipBuild) {
        Write-Status "Construction des images ignorée (paramètre -SkipBuild)"
        return
    }
    
    Write-Status "Construction des images Docker..."
    Write-Host "Cela peut prendre plusieurs minutes..." -ForegroundColor Yellow
    
    docker-compose build --no-cache
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Images Docker construites avec succès"
    } else {
        Write-Error "Échec de la construction des images Docker"
        exit 1
    }
}

function Start-TestServices {
    Write-Status "Démarrage des services de test..."
    
    # Démarrer les services de base
    docker-compose --profile testing up -d postgres-test redis mock-api
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Échec du démarrage des services de test"
        exit 1
    }
    
    Write-Status "Attente que les services soient prêts..."
    Start-Sleep -Seconds 15
    
    # Vérifier l'état des services
    $services = docker-compose ps --services --filter "status=running"
    if ($services) {
        Write-Success "Services de test démarrés: $($services -join ', ')"
    } else {
        Write-Error "Aucun service de test en cours d'exécution"
        exit 1
    }
}

function Test-MockAPI {
    Write-Status "Test des endpoints de l'API mock..."
    
    try {
        # Test de l'endpoint de login
        $loginResponse = Invoke-RestMethod -Uri "http://localhost:1080/Api/Auth/login" `
            -Method POST `
            -ContentType "application/json" `
            -Body '{"email":"<EMAIL>","password":"password"}' `
            -TimeoutSec 10
        
        if ($loginResponse.success) {
            Write-Success "Endpoint de login fonctionne"
        } else {
            Write-Warning "Réponse inattendue de l'endpoint de login"
        }
    }
    catch {
        Write-Warning "Impossible de tester l'endpoint de login: $($_.Exception.Message)"
    }
    
    try {
        # Test de l'endpoint des entreprises
        $companyResponse = Invoke-RestMethod -Uri "http://localhost:1080/Api/Company/GetAllPaged" `
            -Method POST `
            -ContentType "application/json" `
            -Body '{"pageSize":10,"pageNumber":1}' `
            -TimeoutSec 10
        
        if ($companyResponse.success) {
            Write-Success "Endpoint des entreprises fonctionne"
        } else {
            Write-Warning "Réponse inattendue de l'endpoint des entreprises"
        }
    }
    catch {
        Write-Warning "Impossible de tester l'endpoint des entreprises: $($_.Exception.Message)"
    }
}

function Start-AngularApp {
    Write-Status "Démarrage de l'application Angular..."
    
    docker-compose up -d angular-dev
    
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Échec du démarrage de l'application Angular"
        exit 1
    }
    
    Write-Status "Attente que l'application soit prête..."
    Write-Host "Cela peut prendre plusieurs minutes pour la première fois..." -ForegroundColor Yellow
    
    $timeout = 300  # 5 minutes
    $counter = 0
    
    do {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:4200" -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                break
            }
        }
        catch {
            # Continuer à attendre
        }
        
        Start-Sleep -Seconds 5
        $counter += 5
        Write-Host "." -NoNewline
        
        if ($counter -ge $timeout) {
            Write-Host ""
            Write-Error "Timeout: L'application n'a pas démarré dans les temps"
            exit 1
        }
    } while ($true)
    
    Write-Host ""
    Write-Success "Application démarrée et accessible sur http://localhost:4200"
}

function Invoke-AutomatedTests {
    if (-not $RunTests) {
        return
    }
    
    Write-Status "Exécution des tests automatisés..."
    
    docker-compose --profile testing run --rm angular-test npm run test:ci
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Tests automatisés réussis"
    } else {
        Write-Warning "Certains tests automatisés ont échoué"
    }
}

function Show-ManualTestInstructions {
    Write-Host ""
    Write-Host "🧪 INSTRUCTIONS POUR LES TESTS MANUELS" -ForegroundColor Cyan
    Write-Host "======================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "L'application est maintenant accessible sur: " -NoNewline
    Write-Host "http://localhost:4200" -ForegroundColor Blue
    Write-Host "API Mock disponible sur: " -NoNewline
    Write-Host "http://localhost:1080" -ForegroundColor Blue
    Write-Host ""
    Write-Host "📋 CHECKLIST DES TESTS MANUELS:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "✅ Phase 1 - Corrections Prioritaires:" -ForegroundColor Green
    Write-Host "   1. Tester la connexion utilisateur"
    Write-Host "   2. Vérifier la gestion des erreurs (essayer une connexion invalide)"
    Write-Host "   3. Tester le refresh automatique du token"
    Write-Host "   4. Vérifier les messages d'erreur conviviaux"
    Write-Host ""
    Write-Host "✅ Phase 2 - Optimisations:" -ForegroundColor Green
    Write-Host "   5. Tester la pagination sur la liste des entreprises"
    Write-Host "   6. Vérifier le tri et les filtres"
    Write-Host "   7. Tester les actions CRUD (créer, modifier, supprimer)"
    Write-Host "   8. Vérifier la gestion d'état (navigation entre pages)"
    Write-Host ""
    Write-Host "✅ Phase 3 - Fonctionnalités Avancées:" -ForegroundColor Green
    Write-Host "   9. Accéder aux logs d'audit (/dashboard/audit-logs)"
    Write-Host "   10. Tester les filtres avancés des logs"
    Write-Host "   11. Vérifier l'export des logs"
    Write-Host "   12. Tester les notifications (si implémentées)"
    Write-Host "   13. Vérifier la gestion des rôles (si accessible)"
    Write-Host ""
    Write-Host "🔍 COMMANDES UTILES:" -ForegroundColor Magenta
    Write-Host "   - Voir les logs: docker-compose logs angular-dev"
    Write-Host "   - État des services: docker-compose ps"
    Write-Host "   - Arrêter les services: docker-compose down"
    Write-Host ""
    Write-Host "⏹️  ARRÊTER LES TESTS:" -ForegroundColor Red
    Write-Host "   Appuyez sur Ctrl+C ou exécutez: docker-compose down"
    Write-Host ""
}

function Wait-ForUserInput {
    Write-Host ""
    Write-Success "Environnement de test prêt!"
    Write-Host ""
    Write-Host "Appuyez sur une touche pour voir les logs en temps réel, ou Ctrl+C pour arrêter..." -ForegroundColor Yellow
    
    try {
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        Write-Host ""
        Write-Status "Affichage des logs en temps réel..."
        docker-compose logs -f angular-dev
    }
    catch {
        Write-Host ""
        Write-Status "Arrêt demandé par l'utilisateur"
    }
}

# Fonction principale
function Main {
    Write-Host "🚀 Démarrage des tests manuels pour l'application Angular Dyno" -ForegroundColor Cyan
    Write-Host "================================================================" -ForegroundColor Cyan
    Write-Host "Début des tests à $(Get-Date)" -ForegroundColor Gray
    Write-Host ""
    
    if ($Cleanup) {
        Stop-ExistingServices
        Write-Success "Nettoyage terminé"
        return
    }
    
    if (-not (Test-DockerAvailable)) {
        exit 1
    }
    
    Stop-ExistingServices
    Build-DockerImages
    Start-TestServices
    Test-MockAPI
    Start-AngularApp
    
    if ($RunTests) {
        Invoke-AutomatedTests
    }
    
    Show-ManualTestInstructions
    Wait-ForUserInput
    
    Write-Status "Arrêt des services..."
    docker-compose down
    Write-Success "Tests terminés"
}

# Point d'entrée
try {
    Main
}
catch {
    Write-Error "Erreur inattendue: $($_.Exception.Message)"
    Write-Host "Arrêt des services..." -ForegroundColor Yellow
    docker-compose down 2>$null
    exit 1
}
finally {
    $Host.UI.RawUI.ForegroundColor = "White"
}
