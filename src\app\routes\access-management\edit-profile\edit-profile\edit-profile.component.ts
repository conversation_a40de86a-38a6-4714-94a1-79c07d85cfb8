import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MessageService } from 'primeng/api';
import { AuthService } from 'src/app/services/auth.service';
import { StatusCode } from 'src/app/shared/enums/StatusCode';
import { UserProfileDTO } from 'src/app/shared/models/user/UserProfileDTO';

@Component({
    selector: 'app-edit-profile',
    templateUrl: './edit-profile.component.html',
    styleUrls: ['./edit-profile.component.scss']
})
export class EditProfileComponent implements OnInit {
    profile: UserProfileDTO = { fullName: '', email: '', phoneNumber: '', picture: '', dateOfBirth: undefined };
    errorMessage: string = '';
    profileFormGroup: FormGroup;
    fileName: string = '';
    @Output() closeAddDialogEvent = new EventEmitter();
    @Output() profileUpdated = new EventEmitter<void>();
    @Output() profilePictureChanged = new EventEmitter<string>();
    @Input() display: boolean = false;

    minDate: Date = new Date(1900, 0, 1);
    maxDate: Date = (() => {
        const minAge = 18;
        const maxDateCalc = new Date();
        maxDateCalc.setFullYear(maxDateCalc.getFullYear() - minAge);
        return maxDateCalc;
    })();

    constructor(private authService: AuthService, private formBuilder: FormBuilder, private messageService: MessageService) {
        this.profileFormGroup = this.formBuilder.group({
            fullName: ["", Validators.required],
            email: ["", Validators.required],
            phoneNumber: ["", Validators.required],
            dateOfBirth: [null, [Validators.required]],
            picture: [""]
        })
    }

    ngOnInit(): void {
        this.authService.getUserProfile().subscribe(
            response => {
                this.profile = response.objectValue;
                this.populateProfileForm();
                this.profileFormGroup.get("email")?.disable();
                this.profileFormGroup.get('email')?.clearValidators();
                this.profileFormGroup.get("phoneNumber")?.disable();
                this.profileFormGroup.get('phoneNumber')?.clearValidators();
            },
            error => {
                this.errorMessage = 'Failed to load user profile.';
                console.error('Error fetching user profile:', error);
            }
        );
    }

    populateProfileForm() {
        if (this.profile.dateOfBirth) {
            this.profileFormGroup.patchValue({
                fullName: this.profile.fullName,
                email: this.profile.email,
                phoneNumber: this.profile.phoneNumber,
                dateOfBirth: new Date(this.profile.dateOfBirth),
                picture: this.profile.picture
            });
        } else { }

    }

    setDefaultDate(event: any) {
        // Set the default date to 18 years ago

        const minDate = new Date();
        minDate.setFullYear(minDate.getFullYear() - 18);
        this.profileFormGroup.get('datebirth')?.setValue(minDate);

    }

    editProfile(): void {
        this.profile = { ...this.profile, ...this.profileFormGroup.value };
        this.authService.updateUserProfile(this.profile).subscribe(
            response => {
                if (response.statusCode == StatusCode.Ok) {
                    this.onProfilePictureChanged(this.profile.picture);
                    this.messageService.add({ key: 'toast', severity: 'success', summary: 'User Profile updated successfully', detail: response.exceptionMessage });
                    this.profileUpdated.emit();
                    this.closeAddDialogEvent.emit(false);
                } else {
                    this.messageService.add({ key: 'toast', severity: 'success', summary: 'Error Update User Profile', detail: response.exceptionMessage });
                }
            },
            error => {
                console.error('Error updating profile:', error);

            }
        );
    }

    closeProfileDialog() {
        this.closeAddDialogEvent.emit(false);
    }

    onFileSelected(event: any) {
        const file: File = event.target.files[0];
        if (file) {
            // Set the file name to be displayed
            this.fileName = file.name;

            // Convert the file to base64
            this.convertToBase64(file).then((result: string) => {
                // Update the picture field in the form group
                this.profileFormGroup.patchValue({
                    picture: result
                });
            }).catch(error => {
                console.error("Error converting file to base64:", error);
            });
        }
    }

    convertToBase64(file: File): Promise<string> {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = error => reject(error);
        });
    }

    onProfilePictureChanged(path: string): void {
        this.profilePictureChanged.emit(path);
    }
}
