<div class="grid">
    <div class="col-12">
        <div class="card">
            <p-table #dt1 [value]="tableData" selectionMode="single" dataKey="id" [resizableColumns]="true"
                [rows]="pageSize" [loading]="loading" [rowHover]="true" styleClass="p-datatable-gridlines"
                [paginator]="true" [first]="first" [lazy]="true" (onLazyLoad)="lazyLoadSales($event)"
                [totalRecords]="totalRecords"
                [globalFilterFields]="['code','companyName','productType','dynoAmount','netAmount','vatAmount','totalAmount','paymentMethod','date', 'status','creatorUserEmail']"
                responsiveLayout="scroll">
                <ng-template pTemplate="caption">
                    <div>

                        <p-button pRipple type="button" icon="pi pi-filter-slash" label="Clear" [outlined]="true"
                            [pTooltip]="'Clear Filters'" (click)="clear(dt1)" [style]="{'margin-right.px': '10'}"
                            styleClass="p-button-outlined ">
                            <span class="tooltip"></span></p-button>

                        <p-button *ngIf="requestType=='New'&&!isUserSuperAdmin" pRipple type="button" icon="pi pi-plus"
                            label="Add" [outlined]="true" [pTooltip]="'Add Transfer Request'"
                            (click)="displaySalesOrderDialog(null)" [style]="{'margin-right.px': '10'}"
                            styleClass="p-button-outlined ">
                            <span class="tooltip"></span></p-button>




                        <app-sales-order-add-edit [display]="displayAddEditDialog"
                            (salesOrderAdded)="onSalesOrderAdded()"
                            (closeAddDialogEvent)="closeAddEditDialogEvent($event)"></app-sales-order-add-edit>

                    </div>
                </ng-template>

                <ng-template pTemplate="header">
                    <tr>
                        <th style="min-width: 10rem" pSortableColumn="code">
                            <div class="flex justify-content-between align-items-center">
                                Id <!--<p-sortIcon field="code"></p-sortIcon>
                                <p-columnFilter type="text" field="code" display="menu"
                                    placeholder="Search by id"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 12rem" pSortableColumn="Company.Name">
                            <div class="flex justify-content-between align-items-center">
                                Company Name <!--<p-sortIcon field="Company.Name"></p-sortIcon>
                                <p-columnFilter type="text" field="Company.Name" display="menu" matchMode="contains"
                                    placeholder="Search by name"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="productType">
                            <div class="flex justify-content-between align-items-center">
                                Product <!--<p-sortIcon field="ProductType"></p-sortIcon>
                                <p-columnFilter field="ProductType" matchMode="equals" display="menu">
                                    <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                        <p-dropdown [ngModel]="value" [options]="productTypeList"
                                            (onChange)="filter($event.value)" placeholder="Any"
                                            [style]="{'min-width': '12rem'}">
                                            <ng-template let-option pTemplate="item">
                                                <span
                                                    [class]="'customer-badge status-' + option.value">{{option.label}}</span>
                                            </ng-template>
                                        </p-dropdown>
                                    </ng-template>
                                </p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="dynoAmount">
                            <div class="flex justify-content-between align-items-center">
                                Dyno Amount <!--<p-sortIcon field="dynoAmount"></p-sortIcon>
                                <p-columnFilter type="numeric" field="dynoAmount" display="menu" matchMode="equals"
                                    placeholder="Search by amount"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="netAmount">
                            <div class="flex justify-content-between align-items-center">
                                Net Amount<!-- <p-sortIcon field="netAmount"></p-sortIcon>
                                <p-columnFilter type="numeric" field="netAmount" display="menu" matchMode="equals"
                                    placeholder="Search by net Amount"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="vatAmount">
                            <div class="flex justify-content-between align-items-center">
                                VAT Amount <!--<p-sortIcon field="vatAmount"></p-sortIcon>
                                <p-columnFilter type="numeric" field="vatAmount" display="menu" matchMode="equals"
                                    placeholder="Search by vat Amount"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="totalAmount">
                            <div class="flex justify-content-between align-items-center">
                                Total Amount <!--<p-sortIcon field="totalAmount"></p-sortIcon>
                                <p-columnFilter type="numeric" field="totalAmount" display="menu" matchMode="equals"
                                    placeholder="Search by total Amount" currency="TND"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="paymentMethod">
                            <div class="flex justify-content-between align-items-center">
                                Payment <!--<p-sortIcon field="paymentMethod"></p-sortIcon>
                                <p-columnFilter field="paymentMethod" matchMode="equals" display="menu"
                                    matchMode="equals">
                                    <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                        <p-dropdown [ngModel]="value" [options]="PaymentMethodList"
                                            (onChange)="filter($event.value)" placeholder="Search by payment method"
                                            [style]="{'min-width': '12rem'}">
                                            <ng-template let-option pTemplate="item">
                                                <span
                                                    [class]="'customer-badge status-' + option.value">{{option.label}}</span>
                                            </ng-template>
                                        </p-dropdown>
                                    </ng-template>
                                </p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="date">
                            <div class="flex justify-content-between align-items-center">
                                Request Date<!-- <p-sortIcon field="date"></p-sortIcon>
                                <p-columnFilter type="date" field="date" display="menu"
                                    placeholder="mm/dd/yyyy"></p-columnFilter>-->
                            </div>
                        </th>

                        <th style="min-width: 10rem" pSortableColumn="date" *ngIf="requestType=='Valid'">
                            <div class="flex justify-content-between align-items-center">
                                Validation Date <!--<p-sortIcon field="date"></p-sortIcon>
                                <p-columnFilter type="date" field="date" display="menu"
                                    placeholder="mm/dd/yyyy"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="status">
                            <div class="flex justify-content-between align-items-center">
                                Document <!-- <p-sortIcon field="status"></p-sortIcon>-->
                            </div>
                        </th>

                        <th style="min-width: 10rem" pSortableColumn="status">
                            <div class="flex justify-content-between align-items-center">
                                Status <!--<p-sortIcon field="status"></p-sortIcon>
                                <p-columnFilter field="Status" matchMode="equals" display="menu">
                                    <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                        <p-dropdown [ngModel]="value" [options]="salesOrderStatusList"
                                            (onChange)="filter($event.value)" placeholder="Any"
                                            [style]="{'min-width': '12rem'}">
                                            <ng-template let-option pTemplate="item">
                                                <span
                                                    [class]="'customer-badge status-' + option.value">{{option.label}}</span>
                                            </ng-template>
                                        </p-dropdown>
                                    </ng-template>
                                </p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 12rem" pSortableColumn="creatorUserEmail">
                            <div class="flex justify-content-between align-items-center">
                                Created by <!--<p-sortIcon field="creatorUserEmail"></p-sortIcon>
                                <p-columnFilter type="text" field="creatorUserEmail" display="menu"
                                    placeholder="Search by user email"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 12rem" pSortableColumn="creatorUserEmail" *ngIf="requestType=='Failed'">
                            <div class="flex justify-content-between align-items-center">
                                Reason <!--<p-sortIcon field="Reason"></p-sortIcon>
                                <p-columnFilter type="text" field="Reason" display="menu"
                                    placeholder="Search by Reason"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem" *ngIf="requestType=='New'">
                            <div class="flex justify-content-between align-items-center">
                                Actions
                            </div>
                        </th>

                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-salesOrder *ngIf="requestType!='Failed'">
                    <tr [pSelectableRow]="salesOrder">
                        <td>
                            {{salesOrder.code}}
                        </td>
                        <td>
                            {{salesOrder.company.name}}
                        </td>
                        <td>
                            <span>{{ getProductTypeString(salesOrder.productType) }}</span>
                        </td>
                        <td class="text-right">
                            {{salesOrder.dynoAmount| number:'1.3-3'}}
                        </td>
                        <td class="text-right">
                            <div style="display: inline-block; padding-right: 5px;">
                                {{ 'TND' }}
                            </div>
                            <div style="display: inline-block;">
                                {{salesOrder.netAmount| number:'1.3-3'}}
                            </div>
                        </td>
                        <td class="text-right">
                            <div style="display: inline-block; padding-right: 5px;">
                                {{ 'TND' }}
                            </div>
                            <div style="display: inline-block;">
                                {{salesOrder.vatAmount| number:'1.3-3'}}
                            </div>
                        </td>
                        <td class="text-right">
                            <div style="display: inline-block; padding-right: 5px;">
                                {{ 'TND' }}
                            </div>
                            <div style="display: inline-block;">
                                {{ salesOrder.totalAmount | number:'1.3-3'}}
                            </div>
                        </td>
                        <td>
                            <span>{{ getPaymentMethodString(salesOrder.paymentMethod) }}</span>
                        </td>
                        <td>
                            <span>{{ salesOrder.creationTime| dateTimeToDate }}</span>
                        </td>
                        <td *ngIf="requestType=='Valid'">
                            <span>{{ salesOrder.date| dateTimeToDate }}</span>
                        </td>
                        <td>
                            <a [href]="S3URl+salesOrder.document?.path" target="_blank"><i class="pi pi-file-pdf" style="color: red; font-size: 2rem;display: flex;
                                justify-content: space-around;"></i></a>
                        </td>
                        <td>
                            <span [class]="'component-badge status-' + getStatusString(salesOrder.status)">
                                {{ getStatusString(salesOrder.status) }}
                            </span>
                        </td>
                        <td>
                            {{salesOrder.creatorUserEmail}}
                        </td>

                        <td *ngIf="requestType=='New'">
                            <p-button *ngIf="isUserSuperAdmin" pRipple type="button" label="" icon="pi pi-check"
                                [outlined]="true" [pTooltip]="'Validate Transfer Request '"
                                (click)="displayValidateSalesOrderDialog(salesOrder)"
                                [style]="{'margin-right.px': '10'}" styleClass="p-button-outlined "><span
                                    class="tooltip"></span></p-button>

                            <p-button *ngIf="!isUserSuperAdmin" pRipple type="button" label="" icon="pi pi-times"
                                [outlined]="true" [pTooltip]="'Cancel Transfer Request'"
                                (click)="showCancelConfirmation(salesOrder)" [style]="{'margin-right.px': '10'}"
                                styleClass="p-button-outlined "><span class="tooltip"></span></p-button>

                            <p-button *ngIf="isUserSuperAdmin" pRipple type="button" label="" icon="pi pi-times"
                                [outlined]="true" [pTooltip]="'Reject Transfer Request'"
                                (click)="showCancelConfirmation(salesOrder)" [style]="{'margin-right.px': '10'}"
                                styleClass="p-button-outlined "><span class="tooltip"></span></p-button>

                            <p-button pRipple type="button" label="" icon="pi pi-print" [outlined]="true"
                                [pTooltip]="'Reprint PDF'" (click)="reprintPdf(salesOrder)"
                                [disabled]="isReprintPDFDisabled(salesOrder)" [style]="{'margin-right.px': '10'}"
                                styleClass="p-button-outlined "><span class="tooltip"></span></p-button>

                            <p-button pRipple type="button" label="" [outlined]="true" [pTooltip]="'Resend Mail'"
                                icon="pi pi-envelope" (click)="resendMail(salesOrder)"
                                [disabled]="isResendMailDisabled(salesOrder)" [style]="{'margin-right.px': '10'}"
                                styleClass="p-button-outlined "><span class="tooltip"></span></p-button>
                            <!-- <button pButton pRipple type="button" class="p-button-outlined" icon="pi pi-pencil"
                  style="margin-right: 10px;" (click)="displaySalesOrderDialog(salesOrder)"></button>
                <button pButton pRipple type="button" class="p-button-outlined" icon="pi pi-trash"
                  (click)="showDeleteConfirmation(salesOrder)"></button> -->
                            <p-confirmDialog header="Confirmation" key="displayValidateSalesOrderDialog"
                                icon="pi pi-exclamation-triangle" [style]="{'confirmation-message' : true}"
                                acceptButtonStyleClass="p-button-outlined confirmation-button-success"
                                rejectButtonStyleClass="p-button-outlined  p-button-text p-button-danger"></p-confirmDialog>
                        </td>

                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-failedSalesOrder *ngIf="requestType=='Failed'">
                    <tr [pSelectableRow]="failedSalesOrder">
                        <td>
                            {{failedSalesOrder.code}}
                        </td>
                        <td>
                            {{failedSalesOrder.company.name}}
                        </td>
                        <td>
                            <span>{{ getProductTypeString(failedSalesOrder.productType) }}</span>
                        </td>
                        <td class="text-right">
                            {{failedSalesOrder.dynoAmount| number:'1.3-3'}}
                        </td>
                        <td class="text-right">
                            <div style="display: inline-block; padding-right: 5px;">
                                {{ 'TND' }}
                            </div>
                            <div style="display: inline-block;">
                                {{failedSalesOrder.netAmount| number:'1.3-3'}}
                            </div>
                        </td>
                        <td class="text-right">
                            <div style="display: inline-block; padding-right: 5px;">
                                {{ 'TND' }}
                            </div>
                            <div style="display: inline-block;">
                                {{failedSalesOrder.vatAmount| number:'1.3-3'}}
                            </div>
                        </td>
                        <td class="text-right">
                            <div style="display: inline-block; padding-right: 5px;">
                                {{ 'TND' }}
                            </div>
                            <div style="display: inline-block;">
                                {{ failedSalesOrder.totalAmount | number:'1.3-3'}}
                            </div>
                        </td>
                        <td>
                            <span>{{ getPaymentMethodString(failedSalesOrder.paymentMethod) }}</span>
                        </td>
                        <td>
                            <span>{{ failedSalesOrder.creationTime| dateTimeToDate }}</span>
                        </td>
                        <td>
                            <a [href]="S3URl+failedSalesOrder.document?.path" target="_blank"><i class="pi pi-file-pdf"
                                    style="color: red; font-size: 2rem;display: flex;
                                justify-content: space-around;"></i></a>
                        </td>
                        <td>
                            <span [class]="'component-badge status-' + getStatusString(failedSalesOrder.status)">
                                {{ getStatusString(failedSalesOrder.status) }}
                            </span>
                        </td>
                        <td>
                            {{failedSalesOrder.creatorUserEmail}}
                        </td>
                        <td>
                            {{failedSalesOrder.reason}}
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="8">No transfer request found.</td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="loadingbody">
                    <tr>
                        <td colspan="8">Loading transfer request data. Please wait.</td>
                    </tr>
                </ng-template>
            </p-table>
            <!-- Delete Confirmation Component -->
            <app-cancel-confirmation [display]="displayDeleteDialog" (confirm)="confirmDelete($event)"
                (cancelDelete)="onCancelDelete()" (elementDeleted)="onElementDeleted()"></app-cancel-confirmation>

        </div>
    </div>
    <p-toast key="toast"></p-toast>
</div>