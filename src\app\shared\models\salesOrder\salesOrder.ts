import { Currency } from "../../enums/Currency";
import { PaymentMethod } from "../../enums/PaymentMethod";
import { SalesOrderStatus } from "../../enums/SalesOrderStatus";
import { ProductType } from "../../enums/ProductType";
import { ReferentialData } from "../referentialData";
import { Documents } from "../Document/Documents";
import { Company } from "../company/company";

export interface SalesOrder extends ReferentialData {
    id?: string;
    company?: Company;
    code?: string;
    dynoAmount: number;
    currency?: Currency;
    netAmount: number;
    vatAmount: number;
    totalAmount: number;
    paymentMethod: PaymentMethod;
    productType: ProductType;
    date?: Date;
    documentId: string;
    document: Documents;
    status?: SalesOrderStatus;
}
