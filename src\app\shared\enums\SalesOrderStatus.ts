export enum SalesOrderStatus {
    Valid,
    InProgress,
    Rejected,
    Cancelled,
    Invalid,
    PDFGenerated,
    PDFFailed,
    EmailSent,
    EmailFailed,
    ValidationInProgress,
    Reprinting
};

export const SalesOrderStatusString: { [key: number]: string } = {
    [SalesOrderStatus.Valid]: 'Valid',
    [SalesOrderStatus.InProgress]: 'In Progress',
    [SalesOrderStatus.Rejected]: 'Rejected',
    [SalesOrderStatus.Cancelled]: 'Cancelled',
    [SalesOrderStatus.Invalid]: 'Invalid',
    [SalesOrderStatus.PDFGenerated]: 'PDF Generated',
    [SalesOrderStatus.PDFFailed]: 'PDF Failed',
    [SalesOrderStatus.EmailSent]: 'Email Sent',
    [SalesOrderStatus.EmailFailed]: 'Email Failed',
    [SalesOrderStatus.ValidationInProgress]: 'Validation In Progress',
    [SalesOrderStatus.Reprinting]: 'Reprinting'
};
