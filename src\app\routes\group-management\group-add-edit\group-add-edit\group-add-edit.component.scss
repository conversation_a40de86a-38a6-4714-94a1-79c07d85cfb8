.btn-save-role {
    width: 50px !important;
    height: 50px !important;
}

.row {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;
    flex-direction: row;
}

.input-name {
    padding-left: 0px;
    margin-bottom: 0px !important;
}

.input-width {
    width: 100% !important;
}

.dd-status {

    padding-left: 14px !important;
    padding-right: 0px !important;
    margin-bottom: 0px !important;

}

.footer {
    padding-top: 15px !important;
}

::ng-deep {
    .p-dropdown {
        width: 100% !important;
    }

    .p-dialog .p-dialog-footer {
        padding-top: 15px !important;
    }

}

:host ::ng-deep .p-autocomplete-input {
    width: 100%;
}