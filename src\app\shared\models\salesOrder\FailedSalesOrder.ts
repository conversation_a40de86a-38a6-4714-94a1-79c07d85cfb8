import { Currency } from "../../enums/Currency";
import { PaymentMethod } from "../../enums/PaymentMethod";
import { SalesOrderStatus } from "../../enums/SalesOrderStatus";
import { ProductType } from "../../enums/ProductType";
import { ReferentialData } from "../referentialData";
import { SalesOrder } from "./salesOrder";

export interface FailedSalesOrder extends ReferentialData {
    id?: string;
    reason?: string
    salesOrderId?: string;
    salesOrder?: SalesOrder;
    status?: SalesOrderStatus;

}
