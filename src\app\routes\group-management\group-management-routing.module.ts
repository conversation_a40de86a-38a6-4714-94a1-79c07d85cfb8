import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { GroupListComponent } from './group-list/group-list/group-list.component';
import { RoleGuard } from 'src/app/RoleGuard';

const routes: Routes = [];

@NgModule({
  imports: [RouterModule.forChild([
    { path: '', component: GroupListComponent,canActivate:[RoleGuard],data:{role:["company"]as string[]} }
  ])],
  exports: [RouterModule]
})
export class GroupManagementRoutingModule { }
