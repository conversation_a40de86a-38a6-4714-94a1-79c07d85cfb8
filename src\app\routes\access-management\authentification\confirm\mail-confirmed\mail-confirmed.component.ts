import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, map, take } from 'rxjs';
import { AuthService } from 'src/app/services/auth.service';
import { ClientURI, getClientURI } from 'src/app/shared/enums/clientURI';
import { ForgetPasswordDTO } from 'src/app/shared/models/authentification/password/ForgetPasswordDTO';
import { ResetPasswordDTO } from 'src/app/shared/models/authentification/password/ResetPasswordDTO';
import { VerifEmailDTO } from 'src/app/shared/models/confirmation/VerifEmailDTO';

@Component({
  templateUrl: './mail-confirmed.component.html',
})
export class MailConfirmedComponent implements OnInit {
  constructor(private authService: AuthService, private route: ActivatedRoute,
    private routeNavigation: Router) {

  }
  email: string = ""
  token?: string
  ngOnInit(): void {

    const token = this.route.snapshot.queryParams['token'];
    let email = this.route.snapshot.queryParams['email'];
    this.email = this.subsEmail(email);
    const dataVerify: VerifEmailDTO = {
      email: this.email,
      token: token
    }
    console.log('data sended===', dataVerify);

    this.authService.VerifEmail(dataVerify).subscribe((Response) => {
      if (Response.statusCode == 200) {
        console.log(Response.exceptionMessage);
        //remove it after fix token
        this.onSubmit();
      }
      else {
        console.log(Response.exceptionMessage);

      }
    });

  }
  subsEmail(email: string) {
    if (email.endsWith('/')) {
      email = email.slice(0, -1); // Remove the last character (which is '/')
      email = this.subsEmail(email);
    }
    return email;

  }

  onSubmit() {
    const TokenResetPassword: ForgetPasswordDTO = {
      clientURI: getClientURI(),
      email: this.email
    }
    //change the forget to createpaswword if fixed

    this.authService.ForgetPassword(TokenResetPassword).subscribe((Response) => {
      if (Response.statusCode == 200) {
        this.token = Response.objectValue.token;

        // this.routeNavigation.navigate(['/auth/resetpassword'], {
        //   queryParams: { code: this.token, email: this.email }
        // });
      }

    });

  }
}
