<p-dialog header="Add New Cashback Request" [(visible)]="display" [modal]="true" showEffect="fade"
    [style]="{width: '60vw'}" [breakpoints]="{'960px': '75vw'}" (onHide)="closeAddDialog()">
    <form [formGroup]="cashbackFormGroup">
        <div class="col-12">
            <div class="card">
                <div
                    class="flex flex-column align-items-start md:flex-row md:align-items-center md:justify-content-between border-bottom-1 surface-border pb-5 min-w-max">
                    <div class="flex flex-column">
                        <svg width="48" height="50" viewBox="0 0 48 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M33.1548 9.65956L23.9913 4.86169L5.54723 14.5106L0.924465 12.0851L23.9913 0L37.801 7.23403L33.1548 9.65956ZM23.9931 19.3085L42.4255 9.65955L47.0717 12.0851L23.9931 24.1595L10.1952 16.9361L14.8297 14.5106L23.9931 19.3085ZM4.6345 25.8937L0 23.4681V37.9149L23.0669 50V45.1489L4.6345 35.4894V25.8937ZM18.4324 28.2658L0 18.6169V13.7658L23.0669 25.8403V40.2977L18.4324 37.8615V28.2658ZM38.7301 23.468V18.6169L24.9205 25.8403V49.9999L29.555 47.5743V28.2659L38.7301 23.468ZM43.3546 35.4892V16.1914L48.0008 13.7659V37.9148L34.1912 45.1488V40.2977L43.3546 35.4892Z"
                                fill="var(--primary-color)" />
                        </svg>
                        <div class="my-3 text-4xl font-bold text-900">{{company?.name}}</div>
                        <span>Address : {{CompanyAdress}}</span>
                        <span>Tax Code: {{company?.taxCode}}</span>
                        <span>RNE Code : {{company?.rneCode}}</span>
                        <span>Phone N° : {{company?.phoneNumber}}</span>
                        <span>Email : {{company?.email}}</span>
                    </div>
                    <div class="flex flex-column">
                        <h6>Payment Details <span style="color: red;">*</span></h6>
                        <p-dropdown #PaymentDetails class="PaymentDetailsDropdown" [options]="PaymentDetailsDropdown"
                            (onChange)="onPaymentDetailsChange($event)" optionLabel="label" optionValue="value"
                            formControlName="paymentDetailsId" [showClear]="true" placeholder="select"></p-dropdown>
                        <span>Payment Method : {{getPaymentMethodString(selectedPaymentDetails?.paymentMethod)}}</span>
                        <span>RIB : {{selectedPaymentDetails?.rib}}</span>
                        <p-message severity="error" text="Payment details is required"
                            *ngIf="cashbackFormGroup.get('paymentDetailsId')?.hasError('required') && cashbackFormGroup.get('paymentDetailsId')?.touched">
                        </p-message>
                    </div>
                </div>

                <div class="row">
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0">
                        <h6>Dyno Amount <span style="color: red;">*</span></h6>
                        <input type="number" class="input-width text-right" pInputText placeholder="0"
                            formControlName="dynoAmount" (change)="onDynoAmountChange($event)">
                        <p-message severity="error" text="Dyno Amount is required"
                            *ngIf="cashbackFormGroup.get('dynoAmount')?.hasError('required') && cashbackFormGroup.get('dynoAmount')?.touched">
                        </p-message>
                        <p-message severity="error" text="Amount should be a positive number"
                            *ngIf="cashbackFormGroup.get('dynoAmount')?.hasError('pattern') && cashbackFormGroup.get('dynoAmount')?.touched">
                        </p-message>
                        <p-message severity="error" text="Insuffisant Wallet Amount"
                        *ngIf="cashbackFormGroup.get('dynoAmount')?.hasError('max') && 
                               cashbackFormGroup.get('dynoAmount')?.touched">
                      </p-message>

                    </div>
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0">
                        <h6>Dyno commission %</h6>
                        <input type="number" class="input-width text-right" pInputText placeholder="0%"
                            formControlName="shopFeePercentage">
                        <p-message severity="error" text="Dyno commission should be a positive number"
                            *ngIf="cashbackFormGroup.get('shopFeePercentage')?.hasError('pattern') && cashbackFormGroup.get('feePercentage')?.touched">
                        </p-message>
                    </div>
                </div>
                <div>
                    <div class="py-2 mt-3">
                        <div class="flex justify-content-between align-items-center mb-3">
                            <span class="text-900 font-medium">Dyno commission Amount</span>
                            <span class="text-900">TND {{ feeAmount| number:'1.3-3'}}</span>
                        </div>
                        <div class="flex justify-content-between align-items-center mb-3">
                            <span class="text-900 font-medium">Net Amount</span>
                            <span class="text-900">TND {{netAmount| number:'1.3-3'}}</span>
                        </div>
                        <div class="flex justify-content-between align-items-center mb-3">
                            <span class="text-900 font-medium">VAT Amount 19%</span>
                            <span class="text-900">TND {{vatAmount| number:'1.3-3'}}</span>
                        </div>
                        <div class="flex justify-content-between align-items-center mb-3">
                            <span class="text-900 font-bold">Total Amount</span>
                            <span class="text-900 font-medium text-xl">TND {{totalAmount| number:'1.3-3'}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <ng-template pTemplate="footer" class="footer">
        <button pButton (click)="closeAddDialog()" label="Cancel" class="p-button-outlined "></button>
        <button pButton (click)="saveCashback()" class="p-button-outlined p-button-success"
            [disabled]="loading">
            <span *ngIf="!loading">Save</span>
            <p-progressSpinner *ngIf="loading" styleClass="w-3rem h-1rem" strokeWidth="6"></p-progressSpinner></button>
        <p-toast key="toast"></p-toast>
    </ng-template>
</p-dialog>