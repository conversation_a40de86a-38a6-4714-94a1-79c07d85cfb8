import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CompanyListComponent } from './company-list/company-list.component';
import { RoleGuard } from 'src/app/RoleGuard';

@NgModule({
    imports: [RouterModule.forChild([
        { path: '', component: CompanyListComponent,canActivate:[RoleGuard],data:{role:["superadmin"]as string[]} }
    ])],
    exports: [RouterModule]
})
export class CompanyManagementRoutingModule { }
