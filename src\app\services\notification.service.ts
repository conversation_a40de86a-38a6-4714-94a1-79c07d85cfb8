import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { ResponseAPI } from "../shared/models/ResponseAPI";
import { environment } from "src/environments/environment";
import { Notification } from 'src/app/shared/models/notification/notification';

@Injectable({
    providedIn: 'root'
})
export class NotificationService {
    constructor(private http: HttpClient) {
    }

    getAll() {
        return this.http.get<ResponseAPI<Notification[]>>(`${environment.API}/SignalRNotification/GetAll`);
    }

    getAllPaged(pageNumber : number, pageSize : number) {
        return this.http.get<ResponseAPI<Notification[]>>(`${environment.API}/SignalRNotification/GetAllPaged?PageNumber=${pageNumber}&PageSize=${pageSize}`);
    }

    updateIsSeenTrue(id : string) {
        return this.http.patch<ResponseAPI<Notification[]>>(`${environment.API}/SignalRNotification/UpdateIsSeen/${id}`, null);
    }
}