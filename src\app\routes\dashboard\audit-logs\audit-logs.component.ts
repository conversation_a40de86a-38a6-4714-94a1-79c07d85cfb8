import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ConfirmationService, MessageService } from 'primeng/api';
import { Table } from 'primeng/table';
import { Observable, map } from 'rxjs';
import { BaseListComponent } from 'src/app/shared/components/base-list.component';
import { AuditLog, AuditLogFilter, AuditLogSummary, AuditAction, AuditEntityType } from 'src/app/shared/models/audit/audit-log';
import { AuditLogService } from 'src/app/services/audit-log.service';
import { ConfigService } from 'src/app/services/config.service';

@Component({
  selector: 'app-audit-logs',
  templateUrl: './audit-logs.component.html',
  styleUrls: ['./audit-logs.component.scss']
})
export class AuditLogsComponent extends BaseListComponent<AuditLog> implements OnInit {
  @ViewChild('auditTable') auditTable!: Table;

  // Service injecté
  protected service = this.auditLogService;

  // Formulaire de filtres
  filterForm!: FormGroup;
  
  // Énumérations pour les templates
  AuditAction = AuditAction;
  AuditEntityType = AuditEntityType;
  
  // Options pour les dropdowns
  actionOptions = Object.values(AuditAction).map(action => ({
    label: this.getActionLabel(action),
    value: action
  }));

  entityTypeOptions = Object.values(AuditEntityType).map(type => ({
    label: type,
    value: type
  }));

  successOptions = [
    { label: 'Tous', value: null },
    { label: 'Succès', value: true },
    { label: 'Échecs', value: false }
  ];

  // Statistiques
  summary$: Observable<AuditLogSummary>;
  
  // État des filtres
  activeFilters: AuditLogFilter = {};
  showAdvancedFilters = false;

  // Détails du log sélectionné
  selectedLogDetails: AuditLog | null = null;
  displayLogDetails = false;

  constructor(
    configService: ConfigService,
    confirmationService: ConfirmationService,
    messageService: MessageService,
    private auditLogService: AuditLogService,
    private fb: FormBuilder
  ) {
    super(configService, confirmationService, messageService);
    this.initializeFilterForm();
    this.summary$ = this.loadSummary();
  }

  ngOnInit(): void {
    super.ngOnInit();
    this.loadAuditLogs();
  }

  // Implémentation des méthodes abstraites
  protected getItemId(log: AuditLog): string {
    return log.id || '';
  }

  protected getItemDisplayName(log: AuditLog): string {
    return `${log.action} - ${log.entityType}:${log.entityId}`;
  }

  // Initialisation du formulaire de filtres
  private initializeFilterForm(): void {
    this.filterForm = this.fb.group({
      userId: [''],
      action: [null],
      entityType: [null],
      entityId: [''],
      dateFrom: [null],
      dateTo: [null],
      success: [null],
      ipAddress: ['']
    });
  }

  // Chargement des logs avec filtres
  loadAuditLogs(): void {
    const request = {
      pageSize: this.pageSize,
      pageNumber: Math.floor(this.first / this.pageSize) + 1,
      sortBy: this.sortBy || 'timestamp',
      sortDirection: this.sortDirection === 1 ? 'desc' : 'asc' // Plus récents en premier par défaut
    };

    this.loading = true;
    this.auditLogService.getFilteredLogs(this.activeFilters, request).subscribe({
      next: (response) => {
        this.items = response.data;
        this.totalRecords = response.totalCount;
        this.loading = false;
      },
      error: (error) => {
        this.loading = false;
        console.error('Erreur lors du chargement des logs:', error);
      }
    });
  }

  // Application des filtres
  applyFilters(): void {
    const formValue = this.filterForm.value;
    this.activeFilters = {};

    // Construire l'objet de filtres
    Object.keys(formValue).forEach(key => {
      const value = formValue[key];
      if (value !== null && value !== '' && value !== undefined) {
        (this.activeFilters as any)[key] = value;
      }
    });

    this.first = 0; // Retour à la première page
    this.loadAuditLogs();
  }

  // Réinitialisation des filtres
  clearFilters(): void {
    this.filterForm.reset();
    this.activeFilters = {};
    this.first = 0;
    this.loadAuditLogs();
  }

  // Chargement du résumé
  private loadSummary(): Observable<AuditLogSummary> {
    const dateFrom = this.activeFilters.dateFrom;
    const dateTo = this.activeFilters.dateTo;
    
    return this.auditLogService.getAuditSummary(dateFrom, dateTo);
  }

  // Rafraîchissement du résumé
  refreshSummary(): void {
    this.summary$ = this.loadSummary();
  }

  // Affichage des détails d'un log
  showLogDetails(log: AuditLog): void {
    this.selectedLogDetails = log;
    this.displayLogDetails = true;
  }

  // Fermeture des détails
  closeLogDetails(): void {
    this.displayLogDetails = false;
    this.selectedLogDetails = null;
  }

  // Export des logs
  exportLogs(format: 'csv' | 'excel' = 'csv'): void {
    this.auditLogService.exportLogs(this.activeFilters, format).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `audit-logs-${new Date().toISOString().split('T')[0]}.${format}`;
        link.click();
        window.URL.revokeObjectURL(url);

        this.messageService.add({
          severity: 'success',
          summary: 'Export réussi',
          detail: `Les logs ont été exportés en format ${format.toUpperCase()}`
        });
      },
      error: (error) => {
        console.error('Erreur lors de l\'export:', error);
      }
    });
  }

  // Nettoyage des anciens logs
  cleanupOldLogs(): void {
    this.confirmationService.confirm({
      message: 'Êtes-vous sûr de vouloir supprimer les logs de plus de 90 jours ? Cette action est irréversible.',
      header: 'Confirmation de nettoyage',
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.auditLogService.cleanupOldLogs(90).subscribe({
          next: (result) => {
            this.messageService.add({
              severity: 'success',
              summary: 'Nettoyage terminé',
              detail: `${result.deletedCount} logs supprimés`
            });
            this.loadAuditLogs();
            this.refreshSummary();
          },
          error: (error) => {
            console.error('Erreur lors du nettoyage:', error);
          }
        });
      }
    });
  }

  // Méthodes utilitaires
  getActionLabel(action: AuditAction): string {
    const labels: { [key in AuditAction]: string } = {
      [AuditAction.CREATE]: 'Création',
      [AuditAction.READ]: 'Lecture',
      [AuditAction.UPDATE]: 'Modification',
      [AuditAction.DELETE]: 'Suppression',
      [AuditAction.LOGIN]: 'Connexion',
      [AuditAction.LOGOUT]: 'Déconnexion',
      [AuditAction.EXPORT]: 'Export',
      [AuditAction.IMPORT]: 'Import',
      [AuditAction.APPROVE]: 'Approbation',
      [AuditAction.REJECT]: 'Rejet',
      [AuditAction.ACTIVATE]: 'Activation',
      [AuditAction.DEACTIVATE]: 'Désactivation',
      [AuditAction.RESET_PASSWORD]: 'Réinitialisation mot de passe',
      [AuditAction.CHANGE_ROLE]: 'Changement de rôle',
      [AuditAction.BULK_UPDATE]: 'Modification en masse',
      [AuditAction.BULK_DELETE]: 'Suppression en masse'
    };
    return labels[action] || action;
  }

  getActionSeverity(action: AuditAction): string {
    const criticalActions = [
      AuditAction.DELETE, 
      AuditAction.BULK_DELETE, 
      AuditAction.RESET_PASSWORD,
      AuditAction.CHANGE_ROLE
    ];
    
    const warningActions = [
      AuditAction.UPDATE,
      AuditAction.BULK_UPDATE,
      AuditAction.DEACTIVATE,
      AuditAction.REJECT
    ];

    if (criticalActions.includes(action)) return 'danger';
    if (warningActions.includes(action)) return 'warning';
    return 'info';
  }

  formatTimestamp(timestamp: Date): string {
    return new Date(timestamp).toLocaleString('fr-FR');
  }

  hasChanges(log: AuditLog): boolean {
    return !!(log.oldValues || log.newValues);
  }

  toggleAdvancedFilters(): void {
    this.showAdvancedFilters = !this.showAdvancedFilters;
  }

  getActiveFiltersCount(): number {
    return Object.keys(this.activeFilters).length;
  }
}
