import { Component, EventEmitter, Output } from '@angular/core';
import { LayoutService } from './service/app.layout.service';
import { AuthService } from '../services/auth.service';
import { MessageService } from 'primeng/api';
import { Router } from '@angular/router';
import { LocalStoreService } from '../services/local-store.service';
import { UserProfileDTO } from '../shared/models/user/UserProfileDTO';
import { ProfilePictureService } from '../services/profile-picture.service';
import { NotificationService } from '../services/notification.service';

@Component({
    selector: 'app-profilemenu',
    templateUrl: './app.profilesidebar.component.html'
})
export class AppProfileSidebarComponent {
    public invalidLogout: boolean = false;
    profile: UserProfileDTO | null = null;
    displayEditProfileDialog: boolean = false;
    isLoading: boolean = true;
    noImage: boolean = false;
    profilePictureUrl: string = '';
    notificationNumber: number = 0;
    loading: boolean = false;
    constructor(public layoutService: LayoutService, private authService: AuthService,
        private messageService: MessageService,
        private route: Router,
        private localStore: LocalStoreService,
        private notificationService: NotificationService,
        private profilePictureService: ProfilePictureService) {
        this.getUserProfile();
        this.getNotifications();
    }

    get visible(): boolean {
        return this.layoutService.state.profileSidebarVisible;
    }

    set visible(_val: boolean) {

        this.layoutService.state.profileSidebarVisible = _val;
    }

    logout(): void {
        this.loading=true;
        this.authService.logout().subscribe({
            next: (response) => {
                if (response.statusCode == 200) {
                    this.messageService.add({ key: 'toast', severity: 'success', summary: 'Success Message', detail: response.exceptionMessage });
                    localStorage.clear();
                    this.loading=false
                    this.route.navigate(['auth/login'])

                } else if (response.statusCode == 400) {
                    this.messageService.add({ key: 'toast', severity: 'warn', summary: 'Error Message', detail: response.exceptionMessage });
                    localStorage.clear();
                    this.loading=false;
                    this.route.navigate(['auth/login'])

                } else if (response.statusCode == 401) {
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Message', detail: response.exceptionMessage });
                    localStorage.clear();
                    this.loading=false;
                    this.route.navigate(['auth/login'])
                }
            },
            error: (err) => {
                console.log(err)
                this.invalidLogout = true;
                this.loading=false
            },
            complete: () => console.info('Login complete')
        });
    }

    getUserProfile() {
        this.authService.getUserProfile().subscribe({
            next: (response) => {
                if (response.objectValue) {
                    this.profile = response.objectValue;
                    this.profilePictureUrl = this.profile?.picture;
                }
            },
            error: (err) => {
                console.log(err)
                this.invalidLogout = true;
            },
            complete: () => {
                console.info('get profile complete');
                this.isLoading = false;
                if(this.profile?.picture ==  null) {
                    this.noImage = true
                }
            }
        });
    }

    toggleEditProfilePopup(): void {
        this.displayEditProfileDialog = true;
    }

    closeEditProfileDialog(): void {
        this.displayEditProfileDialog = false;
    }

    onProfileModified() {

    }

    onProfilePictureChanged(event: any): void {
        this.getUserProfile();
        const imageUrl = event;
        if (imageUrl) {
            console.log('Image source:', imageUrl);
            this.profilePictureService.setProfilePictureUrl(imageUrl);
        }
    }

    onSettingButtonClick() {
        this.layoutService.showConfigSidebar();
    }

    getNotifications () {
        this.notificationService.getAll().subscribe(
          (response) => {
              if (response.objectValue) {
                  this.notificationNumber = response.objectValue.length; // Keep only the last 5 notifications
              }
          });
      }

}
