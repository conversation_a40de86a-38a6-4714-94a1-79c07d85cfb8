<div class="grid">
    <div class="col-12">
        <div class="card">
            <p-table #dt1 [value]="companies" dataKey="id" [rows]="pageSize" [loading]="loading" [rowHover]="true"
                styleClass="p-datatable-gridlines" [paginator]="true" [lazy]="true"
                (onLazyLoad)="lazyLoadCompanies($event)" [totalRecords]="totalRecords" [first]="first"
                [globalFilterFields]="['name','id','email','phonenumber', 'picture', 'status','service', 'type']"
                responsiveLayout="scroll">
                <ng-template pTemplate="caption">
                    <div>

                        <p-button pRipple type="button" icon="pi pi-filter-slash" label="Clear" [outlined]="true"
                            [pTooltip]="'Clear Filters'" (click)="clear(dt1)" [style]="{'margin-right.px': '10'}"
                            styleClass="p-button-outlined ">
                            <span class="tooltip"></span></p-button>

                        <p-button pRipple type="button" icon="pi pi-filter-plus" label="Add" outlined="true"
                            [pTooltip]="'Add Company'" (click)="displayCompanyDialog(null)"
                            [style]="{'margin-right.px': '10'}" styleClass="p-button-outlined ">
                            <span class="tooltip"></span></p-button>

                        <app-company-add-edit [display]="displayAddEditDialog" [selectedCompany]="selectedCompany"
                            (companyAdded)="onCompanyAdded()"
                            (closeAddDialogEvent)="closeAddEditDialogEvent($event)"></app-company-add-edit>

                    </div>
                </ng-template>

                <ng-template pTemplate="header">
                    <tr>
                        <th style="min-width: 10rem" pSortableColumn="picture">
                            <div class="flex justify-content-between align-items-center">
                                Logo

                            </div>
                        </th>
                        <th style="min-width: 12rem">
                            <div class="flex justify-content-between align-items-center">
                                <span>Name</span>
                                <div class="flex align-items-center">
                                    <p-sortIcon field="Name" pTooltip="Sort Data" pTooltipPosition="right"
                                        pTooltipStyleClass="custom-tooltip"></p-sortIcon>
                                    <p-columnFilter pTooltip="Filter Data" type="text" field="Name" display="menu"
                                        placeholder="Search by name"></p-columnFilter>


                                </div>
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="email">
                            <div class="flex justify-content-between align-items-center">
                                Email <!--<p-sortIcon field="Email"></p-sortIcon>
                                <p-columnFilter type="text" field="Email" display="menu"
                                    placeholder="Search by Email"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 3rem" pSortableColumn="type">
                            <div class="flex justify-content-between align-items-center">
                                Type <!--<p-sortIcon field="EntrepriseType"></p-sortIcon>
                                <p-columnFilter type="text" field="EntrepriseType" display="menu"
                                    placeholder="Search by type"></p-columnFilter> -->
                            </div>
                        </th>
                        <th style="min-width: 3rem" pSortableColumn="service">
                            <div class="flex justify-content-between align-items-center">
                                Service <!--<p-sortIcon field="ServiceType"></p-sortIcon>
                                <p-columnFilter type="text" field="ServiceType" display="menu"
                                    placeholder="Search by service"></p-columnFilter> -->
                            </div>
                        </th>

                        <th style="min-width: 3rem" pSortableColumn="category">
                            <div class="flex justify-content-between align-items-center">
                                Category <!--<p-sortIcon field="CategoryType"></p-sortIcon>
                                <p-columnFilter type="text" field="CategoryType" display="menu"
                                    placeholder="Search by category"></p-columnFilter> -->
                            </div>
                        </th>

                        <th style="min-width: 3rem" pSortableColumn="status">
                            <div class="flex justify-content-between align-items-center">
                                Status<!-- <p-sortIcon field="status"></p-sortIcon>
                                <p-columnFilter field="Status" matchMode="equals" display="menu">
                                    <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                        <p-dropdown [ngModel]="value" [options]="StatusList"
                                            (onChange)="filter($event.value)" placeholder="Any"
                                            [style]="{'min-width': '12rem'}">
                                            <ng-template let-option pTemplate="item">
                                                <span
                                                    [class]="'customer-badge status-' + option.value">{{option.label}}</span>
                                            </ng-template>
                                        </p-dropdown>
                                    </ng-template>
                                </p-columnFilter> -->
                            </div>
                        </th>
                        <th style="min-width: 10rem">
                            <div class="flex justify-content-between align-items-center">
                                Actions
                            </div>
                        </th>

                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-company>
                    <tr>
                        <td>
                            <img src={{company.picture}}>
                        </td>
                        <td>
                            {{company.name}}
                        </td>
                        <td>
                            {{company.email}}
                        </td>
                        <td>
                            <img *ngIf="getEntrepriseTypeString(company.entrepriseType) === 'Enterprise'"
                                src="../../../../assets/entreprise-icon-Entreprise.png" alt="Enterprise">
                            <img *ngIf="getEntrepriseTypeString(company.entrepriseType) === 'Shop'"
                                src="../../../../assets/entreprise-icon-Shop.png" alt="Shop">
                            <span class="title-center">{{ getEntrepriseTypeString(company.entrepriseType) }}</span>
                        </td>


                        <td><span [class]="'service-badge service-'+ getServiceTypeString(company.serviceType)">
                                {{ getServiceTypeString(company.serviceType) }}</span>
                        </td>

                        <td><span [class]="'category-badge category-'+ getCategoryTypeString(company.categoryType)">
                                {{ getCategoryTypeString(company.categoryType) }}</span>
                        </td>


                        <td>
                            <span [class]="'component-badge status-' + getStatusString(company.status)">
                                {{ getStatusString(company.status) }}
                            </span>
                        </td>

                        <td>

                            <p-button pRipple type="button" icon="pi pi-pencil" label="" outlined="true"
                                [pTooltip]="'Edit Company'" (onClick)="displayCompanyDialog(company)"
                                [style]="{'margin-right.px': '10'}" styleClass="p-button-outlined "
                                [disabled]="company.status === Status.Deleted">
                                <span class="tooltip"></span></p-button>

                            <p-button *ngIf="company.status === Status.Deleted" pRipple type="button"
                                icon="pi pi-unlock" label="" outlined="true" [pTooltip]="'Reactivate Company'"
                                (onClick)="showReactivateConfirmation(company)" [style]="{'margin-right.px': '10'}"
                                styleClass="p-button-outlined ">
                                <span class="tooltip"></span></p-button>

                            <p-button *ngIf="company.status !== Status.Deleted" pRipple type="button" icon="pi pi-trash"
                                label="" outlined="true" [pTooltip]="'Delete Company'"
                                (onClick)="showDeleteConfirmation(company)" [style]="{'margin-right.px': '10'}"
                                styleClass="p-button-outlined ">
                                <span class="tooltip"></span></p-button>
                        </td>

                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="8">No companies found.</td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="loadingbody">
                    <tr>
                        <td colspan="8">Loading companies data. Please wait.</td>
                    </tr>
                </ng-template>
            </p-table>
            <!-- Delete Confirmation Component -->
            <app-add-confirmation [display]="displayDeleteDialog" (confirm)="confirmDelete()"
                (cancelDelete)="onCancelDelete()" (elementDeleted)="onElementDeleted()"></app-add-confirmation>

            <!-- Reactivate Confirmation Component -->
            <app-add-confirmation [display]="displayReactivateDialog" (confirm)="confirmReactivate()"
                (cancelReactivate)="onCancelReactivate()"
                (elementReactivate)="onElementDeleted()"></app-add-confirmation>

        </div>
    </div>
    <p-toast key="toast"></p-toast>
</div>