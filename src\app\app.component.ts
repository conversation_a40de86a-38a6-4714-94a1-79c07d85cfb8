import { Component, HostListener, OnInit } from '@angular/core';
import { PrimeNGConfig } from 'primeng/api';
import { LocalStoreService } from './services/local-store.service';
import { NavigationEnd, NavigationStart, Router } from '@angular/router';

@Component({
    selector: 'app-root',
    templateUrl: './app.component.html'
})
export class AppComponent implements OnInit {

    constructor(
        private primengConfig: PrimeNGConfig,
        private localStore: LocalStoreService
    ) { }




    ngOnInit(): void {
        this.primengConfig.ripple = true;
        console.log("test");
        // Hook into Angular's Router events to detect page reload
    }

}
