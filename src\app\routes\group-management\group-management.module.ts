import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GroupManagementRoutingModule } from './group-management-routing.module';
import { GroupListComponent } from './group-list/group-list/group-list.component';
import { GroupAddEditComponent } from './group-add-edit/group-add-edit/group-add-edit.component';
import { FormsModule } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { ToggleButtonModule } from 'primeng/togglebutton';
import { RippleModule } from 'primeng/ripple';
import { MultiSelectModule } from 'primeng/multiselect';
import { DropdownModule } from 'primeng/dropdown';
import { ProgressBarModule } from 'primeng/progressbar';
import { ToastModule } from 'primeng/toast';
import { SliderModule } from 'primeng/slider';
import { RatingModule } from 'primeng/rating';
import { ConfirmationDialogueModule } from 'src/app/shared/components/confirmation-dialog/confirmation-dialogue.module';
import { ReactiveFormsModule } from '@angular/forms';
import { DialogModule } from 'primeng/dialog';
import { SharedModule } from 'src/app/shared/Modules/shared-module/shared-module.module';
import { MessageService, ConfirmationService } from 'primeng/api';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { MessageModule } from 'primeng/message';
import { SendConfirmationComponent } from 'src/app/shared/components/send-confirmation/send-confirmation.component';
import { TooltipModule } from 'primeng/tooltip';
import { ProgressSpinnerModule } from 'primeng/progressspinner';

@NgModule({
    declarations: [
        GroupListComponent,
        GroupAddEditComponent
    ],
    providers: [MessageService, ConfirmationService],
    imports: [
        MessageModule,
        CommonModule,
        GroupManagementRoutingModule,
        CommonModule,
        SharedModule,
        FormsModule,
        TableModule,
        RatingModule,
        ButtonModule,
        SliderModule,
        InputTextModule,
        ToggleButtonModule,
        RippleModule,
        MultiSelectModule,
        DropdownModule,
        ProgressBarModule,
        ToastModule,
        ConfirmationDialogueModule,
        ReactiveFormsModule,
        DialogModule,
        ReactiveFormsModule,
        TableModule,
        RatingModule,
        ButtonModule,
        SliderModule,
        InputTextModule,
        ToggleButtonModule,
        RippleModule,
        MultiSelectModule,
        DropdownModule,
        ProgressBarModule,
        DialogModule,
        AutoCompleteModule,
        TooltipModule,
        ProgressSpinnerModule

    ]
})
export class GroupManagementModule { }
