import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';

@NgModule({
    imports: [RouterModule.forChild([
        { path: 'apps/roles', data: { breadcrumb: 'Roles' }, loadChildren: () => import('./role-management/role-management.module').then(m => m.RoleManagementModule) },
        { path: 'apps/companies', data: { breadcrumb: 'Companies' }, loadChildren: () => import('./company-management/company-management.module').then(m => m.CompanyManagementModule) },
        { path: 'apps/groups', data: { breadcrumb: 'Groups' }, loadChildren: () => import('./group-management/group-management.module').then(m => m.GroupManagementModule) },
        { path: 'apps/employees', data: { breadcrumb: 'Employees' }, loadChildren: () => import('./employee-management/employee-management.module').then(m => m.EmployeeManagementModule) },
        { path: 'apps/tickets', data: { breadcrumb: 'Tickets' }, loadChildren: () => import('./ticket-management/ticket-management.module').then(m => m.TicketManagementModule) },
        { path: 'users', data: { breadcrumb: 'Users' }, loadChildren: () => import('./access-management/user/user.module').then(m => m.UserModule) },
        { path: 'apps/cashiers', data: { breadcrumb: 'Cashiers' }, loadChildren: () => import('./cashier-management/cashier-management.module').then(m => m.CashierManagementModule) },
        { path: 'Dashboard', data: { breadcrumb: 'Dashboard' }, loadChildren: () => import('./dashboard/dashboard.module').then(m => m.DashboardModule) },
        { path: 'apps/newRequest', data: { breadcrumb: 'Transfer Request' }, loadChildren: () => import('./sales-management/sales-management.module').then(m => m.SalesManagementModule) },
        { path: 'apps/validRequest', data: { breadcrumb: 'Transfer Request' }, loadChildren: () => import('./sales-management/sales-management.module').then(m => m.SalesManagementModule) },
        { path: 'apps/failedRequest', data: { breadcrumb: 'Transfer Request' }, loadChildren: () => import('./sales-management/sales-management.module').then(m => m.SalesManagementModule) },
        { path: 'apps/newCashbackRequest', data: { breadcrumb: 'Cashback Request' }, loadChildren: () => import('./cashback-management/cashback-management.module').then(m => m.CashbackManagementModule) },
        { path: 'apps/validCashbackRequest', data: { breadcrumb: 'Cashback Request' }, loadChildren: () => import('./cashback-management/cashback-management.module').then(m => m.CashbackManagementModule) },
        { path: 'apps/failedCashbackRequest', data: { breadcrumb: 'Cashback Request' }, loadChildren: () => import('./cashback-management/cashback-management.module').then(m => m.CashbackManagementModule) },
        { path: 'apps/failedInvoices', data: { breadcrumb: 'Invoices' }, loadChildren: () => import('./invoice-management/invoice-management.module').then(m => m.InvoiceManagementModule) },
        { path: 'apps/validInvoices', data: { breadcrumb: 'Invoices' }, loadChildren: () => import('./invoice-management/invoice-management.module').then(m => m.InvoiceManagementModule) },
        { path: 'apps/shopownerTransaction', data: { breadcrumb: 'List Transactions' }, loadChildren: () => import('./shopowner-transaction/shopowner-transaction.module').then(m => m.ShopownerTransactionModule) }

    ])],
    exports: [RouterModule]
})
export class SecureRoutesRoutingModule { }
