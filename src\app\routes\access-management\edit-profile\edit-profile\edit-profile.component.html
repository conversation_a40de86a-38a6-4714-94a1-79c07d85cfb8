<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<p-dialog [header]="'Edit profile'" [(visible)]="display" [modal]="true" showEffect="fade" [style]="{ width: '60vw' }"
    [breakpoints]="{ '960px': '75vw' }" (onHide)="closeProfileDialog()">
    <form [formGroup]="profileFormGroup">
        <div class="col-12">
            <div class="card">
                <div class="row">
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 input-fullName">
                        <h5>FullName <span style="color: red;">*</span></h5>
                        <input type="text" class="input-width" pInputText placeholder="User"
                            formControlName="fullName" />
                        <p-message severity="error" text="Full Name is required"
                            *ngIf="profileFormGroup.get('fullName')?.hasError('required') && profileFormGroup.get('fullName')?.touched">
                        </p-message>
                    </div>
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 dd-birthDate">
                        <h5>Date Of Birth (+18)<span style="color: red;">*</span></h5>
                        <p-calendar [minDate]="minDate" [maxDate]="maxDate" [readonlyInput]="true"
                        formControlName="dateOfBirth" appendTo="body" [dateFormat]="'dd-mm-yy'"
                        (onShow)="setDefaultDate($event)"  ></p-calendar>
                        <p-message severity="error" text="Date of birth is required"
                            *ngIf="profileFormGroup.get('datebirth')?.hasError('required') && profileFormGroup.get('datebirth')?.touched">
                        </p-message>
                    </div>
                </div>

                <div class="row">
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 input-email">
                        <h5>Email<span style="color: red;">*</span></h5>
                        <input type="email" class="input-width" pInputText placeholder="<EMAIL>"
                            formControlName="email" />
                    </div>
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 input-phoneNumber">
                        <h5>Phone<span style="color: red;">*</span></h5>
                        <div class="flex items-center">
                            <!-- Country Code with Flag for Tunisia -->
                            <div class="country-code-with-flag">
                                <img src="../../../../assets/tunisiaFlag.jpeg" alt="Tunisia Flag" class="flag-icon">
                                <span class="country-code">+216</span>
                            </div>
                            <input type="tel" class="input-width" pInputText placeholder="Phone number"
                                formControlName="phoneNumber" />
                        </div>
                        <p-message severity="error" text="Phone Number is required"
                            *ngIf="profileFormGroup.get('phoneNumber')?.hasError('required') && profileFormGroup.get('phoneNumber')?.touched">
                        </p-message>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <ng-template pTemplate="footer" class="footer">
        <button pButton (click)="closeProfileDialog()" label="Cancel" class="p-button-outlined"></button>
        <button pButton (click)="editProfile()" label="Save" class="p-button-outlined p-button-success"
            [disabled]="profileFormGroup.invalid"></button>
        <p-toast key="toast"></p-toast>
    </ng-template>
</p-dialog>