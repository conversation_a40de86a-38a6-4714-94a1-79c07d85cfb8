import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ResponseAPI } from '../shared/models/ResponseAPI';
import { environment } from 'src/environments/environment';
import { InvoiceStatus } from '../shared/enums/invoice-status';
import { Invoice } from '../shared/models/invoice/invoice';
import { FilterMetadata } from 'primeng/api';

@Injectable({
    providedIn: 'root'
})
export class InvoiceService {

    constructor(private http: HttpClient) { }

    getInvoicesByStatus(status: InvoiceStatus[], pageSize: number, pageNumber: number, sortBy: string, sortDirection: number, filters: { [s: string]: FilterMetadata } | undefined) {
             let simplifiedFilters: { [s: string]: string } = {};
        if (filters) {
            for (const field in filters) {
                if (Object.prototype.hasOwnProperty.call(filters, field)) {
                    const filterValues = filters[field];

                    if (filterValues !== undefined && Array.isArray(filterValues) && filterValues.length > 0) {
                        const filterValue = filterValues[0]?.value;

                        if (filterValue !== undefined && filterValue !== null) {
                            simplifiedFilters[field] = filterValue.toString();
                        }
                    } else {
                        const filterValue = filterValues?.value;

                        if (filterValue !== undefined && filterValue !== null) {
                            simplifiedFilters[field] = filterValue.toString();
                        }
                    }
                }
            }
        }

        const requestBody = {
            salesOrderStatus: status,
            pagedParameters: {
                pageNumber: pageNumber,
                pageSize: pageSize,
                sortBy: sortBy,
                sortDirection: sortDirection,
                filters: simplifiedFilters,
              }
        };
        return this.http.post<ResponseAPI<Invoice[]>>(`${environment.API}/SalesInvoice/GetAllByStatus`, requestBody);
    }

}
