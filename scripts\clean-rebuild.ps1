# Script PowerShell pour nettoyer et reconstruire l'environnement Docker
# Résout les problèmes de dépendances et de cache

param(
    [switch]$Force,
    [switch]$SkipTests
)

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

Write-Host "🧹 Nettoyage et reconstruction de l'environnement Docker" -ForegroundColor Cyan
Write-Host "=======================================================" -ForegroundColor Cyan

# Étape 1: Arrêter tous les services
Write-Status "Arrêt de tous les services Docker..."
try {
    docker-compose down -v --remove-orphans
    Write-Success "Services arrêtés"
}
catch {
    Write-Warning "Aucun service à arrêter"
}

# Étape 2: Nettoyer les images Docker (si Force)
if ($Force) {
    Write-Status "Nettoyage complet des images Docker..."
    
    # Supprimer les images du projet
    docker images | Select-String "dyno" | ForEach-Object {
        $imageId = ($_ -split '\s+')[2]
        docker rmi $imageId -f 2>$null
    }
    
    # Nettoyer le système Docker
    docker system prune -f
    docker volume prune -f
    
    Write-Success "Images Docker nettoyées"
}

# Étape 3: Nettoyer les caches locaux
Write-Status "Nettoyage des caches locaux..."

# Supprimer node_modules s'il existe
if (Test-Path "node_modules") {
    Remove-Item -Recurse -Force "node_modules"
    Write-Success "node_modules supprimé"
}

# Supprimer package-lock.json s'il existe
if (Test-Path "package-lock.json") {
    Remove-Item -Force "package-lock.json"
    Write-Success "package-lock.json supprimé"
}

# Supprimer les dossiers de build
$buildDirs = @("dist", "coverage", ".angular")
foreach ($dir in $buildDirs) {
    if (Test-Path $dir) {
        Remove-Item -Recurse -Force $dir
        Write-Success "$dir supprimé"
    }
}

# Étape 4: Vérifier le package.json
Write-Status "Vérification du package.json..."

$packageJson = Get-Content "package.json" | ConvertFrom-Json

# Vérifier les dépendances problématiques
$problematicDeps = @("@fullcalendar/angular", "@fullcalendar/core")
$hasProblems = $false

foreach ($dep in $problematicDeps) {
    if ($packageJson.dependencies.$dep -or $packageJson.devDependencies.$dep) {
        Write-Warning "Dépendance problématique trouvée: $dep"
        $hasProblems = $true
    }
}

if (-not $hasProblems) {
    Write-Success "Aucune dépendance problématique trouvée"
}

# Étape 5: Créer un package.json temporaire pour les tests
Write-Status "Création d'un package.json optimisé pour les tests..."

# Backup du package.json original
Copy-Item "package.json" "package.json.backup"

# Créer une version simplifiée pour les tests
$testPackageJson = @{
    name = "apollo-ng"
    version = "0.0.0"
    scripts = @{
        ng = "ng"
        start = "ng serve"
        build = "ng build"
        "build:prod" = "ng build --configuration production"
        "build:staging" = "ng build --configuration staging"
        test = "ng test"
        "test:ci" = "ng test --watch=false --browsers=ChromeHeadless --code-coverage"
        lint = "ng lint"
    }
    dependencies = @{
        "@angular/animations" = "^17.3.3"
        "@angular/cdk" = "^16.2.8"
        "@angular/common" = "^17.3.3"
        "@angular/compiler" = "^17.3.3"
        "@angular/core" = "^17.3.3"
        "@angular/forms" = "^17.3.3"
        "@angular/platform-browser" = "^17.3.3"
        "@angular/platform-browser-dynamic" = "^17.3.3"
        "@angular/router" = "^17.3.3"
        "primeng" = "^16.0.2"
        "primeicons" = "6.0.1"
        "primeflex" = "^3.3.1"
        "rxjs" = "~7.8.0"
        "tslib" = "^2.3.0"
        "zone.js" = "~0.14.4"
        "jwt-decode" = "^3.1.2"
        "uuid" = "^9.0.1"
    }
    devDependencies = @{
        "@angular-devkit/build-angular" = "^17.3.3"
        "@angular/cli" = "~17.3.3"
        "@angular/compiler-cli" = "^17.3.3"
        "@types/jasmine" = "~4.3.0"
        "@types/uuid" = "^9.0.6"
        "jasmine-core" = "~4.6.0"
        "karma" = "~6.4.0"
        "karma-chrome-launcher" = "~3.2.0"
        "karma-coverage" = "~2.2.0"
        "karma-jasmine" = "~5.1.0"
        "karma-jasmine-html-reporter" = "~2.1.0"
        "typescript" = "~5.4.4"
    }
}

$testPackageJson | ConvertTo-Json -Depth 10 | Set-Content "package.json.test"

# Étape 6: Construire avec le package.json de test
Write-Status "Construction avec le package.json optimisé..."

# Utiliser le package.json de test
Copy-Item "package.json.test" "package.json"

try {
    # Construire l'image Docker
    docker-compose build --no-cache angular-dev
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Construction réussie avec le package.json optimisé"
        
        # Démarrer les services de test
        Write-Status "Démarrage des services de test..."
        docker-compose --profile testing up -d postgres-test redis mock-api
        
        # Démarrer l'application
        Write-Status "Démarrage de l'application..."
        docker-compose up -d angular-dev
        
        # Attendre que l'application soit prête
        Write-Status "Attente que l'application soit prête..."
        $timeout = 180
        $counter = 0
        
        do {
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:4200" -TimeoutSec 5 -UseBasicParsing
                if ($response.StatusCode -eq 200) {
                    break
                }
            }
            catch {
                # Continuer à attendre
            }
            
            Start-Sleep -Seconds 5
            $counter += 5
            Write-Host "." -NoNewline
            
            if ($counter -ge $timeout) {
                Write-Host ""
                Write-Error "Timeout: L'application n'a pas démarré"
                break
            }
        } while ($true)
        
        if ($counter -lt $timeout) {
            Write-Host ""
            Write-Success "Application démarrée avec succès sur http://localhost:4200"
            
            # Tests automatisés (optionnel)
            if (-not $SkipTests) {
                Write-Status "Exécution des tests automatisés..."
                docker-compose --profile testing run --rm angular-test npm run test:ci
            }
            
            Write-Host ""
            Write-Host "🎉 Environnement prêt pour les tests manuels!" -ForegroundColor Green
            Write-Host "   Application: http://localhost:4200" -ForegroundColor Blue
            Write-Host "   API Mock: http://localhost:1080" -ForegroundColor Blue
            Write-Host ""
            Write-Host "Pour arrêter: docker-compose down" -ForegroundColor Yellow
        }
    }
    else {
        Write-Error "Échec de la construction"
    }
}
catch {
    Write-Error "Erreur lors de la construction: $($_.Exception.Message)"
}
finally {
    # Restaurer le package.json original
    Write-Status "Restauration du package.json original..."
    Copy-Item "package.json.backup" "package.json"
    Remove-Item "package.json.test" -ErrorAction SilentlyContinue
    Remove-Item "package.json.backup" -ErrorAction SilentlyContinue
}

Write-Host ""
Write-Host "Script terminé." -ForegroundColor Cyan
