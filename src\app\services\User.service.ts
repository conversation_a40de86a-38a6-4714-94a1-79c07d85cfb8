import { HttpClient, Http<PERSON>ara<PERSON>, HttpResponse } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { UserDTO } from "../shared/models/user/UserDTO";
import { ResponseAPI } from "../shared/models/ResponseAPI";
import { environment } from "src/environments/environment";
import { Observable } from "rxjs/internal/Observable";
import { FilterMetadata } from "primeng/api";
import { PhoneNumber } from "libphonenumber-js";

@Injectable({
    providedIn: 'root'
})
export class UserService {
    constructor(private http: HttpClient) {

    }
    getAllPagedUsers(pageSize: number, pageNumber: number, sortBy: string, sortDirection: number, filters: { [s: string]: FilterMetadata } | undefined) : Observable<HttpResponse<ResponseAPI<UserDTO[]>>>  {
        let simplifiedFilters: { [s: string]: string } = {};
        if (filters) {
            for (const field in filters) {
                if (Object.prototype.hasOwnProperty.call(filters, field)) {
                    const filterValues = filters[field];

                    if (filterValues !== undefined && Array.isArray(filterValues) && filterValues.length > 0) {
                        const filterValue = filterValues[0]?.value;

                        if (filterValue !== undefined && filterValue !== null) {
                            simplifiedFilters[field] = filterValue.toString();
                        }
                    } else {
                        const filterValue = filterValues?.value;

                        if (filterValue !== undefined && filterValue !== null) {
                            simplifiedFilters[field] = filterValue.toString();
                        }
                    }
                }
            }
        }

        const requestBody = {
            pageSize,
            pageNumber,
            sortBy: sortBy.toString(),
            sortDirection: sortDirection.toString(),
            filters: simplifiedFilters,
        };

        return this.http.post<ResponseAPI<UserDTO[]>>(`${environment.API}/User/GetAllPagedUsers` ,requestBody, {  observe: 'response', responseType: 'json' });
    }

    getAllUsers() {
        return this.http.get<ResponseAPI<UserDTO[]>>(`${environment.API}/User/GetUsersCreatedByAdmin`);
    }
    getUsersByDefaultRole(defaultRole: string) {
        return this.http.get<ResponseAPI<UserDTO[]>>(`${environment.API}/User/GetAllUsersByDefaultRole/${defaultRole}`);
    }

    getUsersByRole(roleId: string) {
        return this.http.get<ResponseAPI<UserDTO[]>>(`${environment.API}/User/GetAllUsersByRole/${roleId}`);
    }

    getUserIdByPhoneNumber(phoneNumber: string){
        return this.http.get<ResponseAPI<UserDTO>>(`${environment.API}/User/GetUserByPhoneNumber/${phoneNumber}`);
    }

    saveUser(userDTO: UserDTO) {

        return this.http.post<ResponseAPI<UserDTO>>(`${environment.API}/AuthAdmin/Register`, userDTO);
    }
    updateUser(userDTO: UserDTO) {
        return this.http.patch<ResponseAPI<UserDTO>>(`${environment.API}/User/Update`, userDTO);
    }
    deleteUser(id: string) {
        return this.http.delete<ResponseAPI<UserDTO>>(`${environment.API}/User/Delete/${id}`);
    }
    hardDeleteUser(id: string) {
        return this.http.delete<ResponseAPI<UserDTO>>(`${environment.API}/User/HardDelete/${id}`);
    }
    getUserById(id: string) {
        return this.http.get<ResponseAPI<UserDTO>>(`${environment.API}/User/Get/${id}`);
    }


}
