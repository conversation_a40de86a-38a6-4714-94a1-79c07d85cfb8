import { PeriodType } from "../../enums/PeriodType";
import { Status } from "../../enums/status";
import { WalletType } from "../../enums/wallet-type";


export interface Ticket {
    ticketId?: string;
    name: string;
    amount : number;
    quantity:number;
    totalAmount:number;
    type: WalletType;
    isAutomatic : boolean;
    periodType: PeriodType;
    startDate:Date;
    endDate:Date;
    status: Status;
    companyId? : string ;
    creatorUserId?:string;
    creationTime?:Date;
    lastModifierUserId?:string;
    lastModificationTime?:Date;
}
