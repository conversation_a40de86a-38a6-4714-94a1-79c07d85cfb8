import { FilterMetadata } from 'primeng/api';

export interface PaginationRequest {
  pageSize: number;
  pageNumber: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc' | number;
  filters?: { [key: string]: string };
}

export interface PaginationResponse<T> {
  data: T[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface TableLazyLoadConfig {
  first?: number;
  rows?: number;
  sortField?: string;
  sortOrder?: number;
  filters?: { [s: string]: FilterMetadata };
}

export class PaginationHelper {
  /**
   * Convertit les filtres PrimeNG en format simple
   */
  static convertFilters(filters?: { [s: string]: FilterMetadata }): { [s: string]: string } {
    const simplifiedFilters: { [s: string]: string } = {};
    
    if (!filters) return simplifiedFilters;

    for (const field in filters) {
      if (Object.prototype.hasOwnProperty.call(filters, field)) {
        const filterValues = filters[field];

        if (filterValues !== undefined && Array.isArray(filterValues) && filterValues.length > 0) {
          const filterValue = filterValues[0]?.value;
          if (filterValue !== undefined && filterValue !== null) {
            simplifiedFilters[field] = filterValue.toString();
          }
        } else {
          const filterValue = filterValues?.value;
          if (filterValue !== undefined && filterValue !== null) {
            simplifiedFilters[field] = filterValue.toString();
          }
        }
      }
    }

    return simplifiedFilters;
  }

  /**
   * Convertit la configuration de lazy loading en requête de pagination
   */
  static fromTableLazyLoad(config: TableLazyLoadConfig, defaultPageSize: number = 10): PaginationRequest {
    const pageSize = config.rows || defaultPageSize;
    const pageNumber = Math.floor((config.first || 0) / pageSize) + 1;
    
    return {
      pageSize,
      pageNumber,
      sortBy: config.sortField || '',
      sortDirection: config.sortOrder === 1 ? 'asc' : config.sortOrder === -1 ? 'desc' : 'asc',
      filters: this.convertFilters(config.filters)
    };
  }

  /**
   * Extrait les informations de pagination depuis les headers HTTP
   */
  static extractPaginationFromHeaders(headers: any): Partial<PaginationResponse<any>> {
    const xPaginationHeader = headers.get('x-pagination');
    
    if (xPaginationHeader) {
      try {
        const pagination = JSON.parse(xPaginationHeader);
        return {
          totalCount: pagination.TotalCount || 0,
          pageNumber: pagination.CurrentPage || 1,
          pageSize: pagination.PageSize || 10,
          totalPages: pagination.TotalPages || 1,
          hasNextPage: pagination.HasNext || false,
          hasPreviousPage: pagination.HasPrevious || false
        };
      } catch (error) {
        console.error('Erreur lors du parsing des headers de pagination:', error);
      }
    }

    return {};
  }
}
