import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { LoginDTO } from 'src/app/shared/models/authentification/authRequest/LoginDTO';
import { AuthResponseDTO } from 'src/app/shared/models/authentification/authResponse/AuthResponseDTO';
import { ResponseAPI } from 'src/app/shared/models/ResponseAPI';
import { VerifOTPCodeDTO } from '../shared/models/OTP/VerifOTPCodeDTO';
import { ForgetPasswordDTO } from '../shared/models/authentification/password/ForgetPasswordDTO';
import { ResetPasswordDTO } from '../shared/models/authentification/password/ResetPasswordDTO';
import { UserTokenDTO } from '../shared/models/authentification/UserTokenDTO';
import { VerifEmailDTO } from '../shared/models/confirmation/VerifEmailDTO';
import { UserDTO } from '../shared/models/user/UserDTO';
import { UserProfileDTO } from '../shared/models/user/UserProfileDTO';
@Injectable({
    providedIn: 'root',
})
export class AuthService {
    constructor(private _http: HttpClient) { }

    login(data: LoginDTO,acceptedTerms:boolean=false) {

        const headers = new HttpHeaders({
            'acceptLanguage' : "En",
            'acceptTermsAndConditions':acceptedTerms.toString()
        });
        const httpOptions = { headers: headers };
        return this._http.post<ResponseAPI<AuthResponseDTO>>(`${environment.API}/AuthAdmin/login`, data, httpOptions);
    }

    logout() {
        return this._http.get<ResponseAPI<AuthResponseDTO>>(`${environment.API}/AuthAdmin/Logout`);
    }

    ForgetPassword(data: ForgetPasswordDTO) {
        return this._http.post<ResponseAPI<AuthResponseDTO>>(`${environment.API}/AuthAdmin/ForgetPassword`, data)
    }

    ResetPassword(data: ResetPasswordDTO) {
        return this._http.post<ResponseAPI<AuthResponseDTO>>(`${environment.API}/AuthAdmin/ResetPassword`, data)
    }


    VerifOPTCode(data: VerifOTPCodeDTO) {
        return this._http.post<ResponseAPI<AuthResponseDTO>>(`${environment.API}/UserOTPAdmin/VerifierDevice`, data)
    }

    RefreshToken(data: UserTokenDTO) {
        return this._http.post<ResponseAPI<AuthResponseDTO>>(`${environment.API}/AuthAdmin/RefreshToken`, data)
    }

    VerifEmail(data: VerifEmailDTO) {
        return this._http.post<ResponseAPI<AuthResponseDTO>>(`${environment.API}/UserOTPAdmin/VerifierEmail`, data)
    }

    CreatePassword(data: any) {
        return this._http.post<ResponseAPI<AuthResponseDTO>>(`${environment.API}/AuthAdmin/CreatePassword`, data)
    }

    getUserProfile() {
        return this._http.get<ResponseAPI<UserProfileDTO>>(`${environment.API}/User/GetUserProfile`);
    }

    updateUserProfile(userProfileDTO: UserProfileDTO) {
        return this._http.patch<ResponseAPI<UserProfileDTO>>(`${environment.API}/User/UpdateUserProfile`, userProfileDTO);
    }

}
