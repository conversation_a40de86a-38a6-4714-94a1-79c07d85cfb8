import { Component, ViewChild, ElementRef } from '@angular/core';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { MessageService, ConfirmationService, TreeNode, SelectItem, FilterMetadata } from 'primeng/api';
import { Role } from 'src/app/shared/models/role/role';
import { RoleManagementService } from 'src/app/services/role-management.service';
import { Status } from 'src/app/shared/enums/status';
import { EnumToStringPipe } from 'src/app/shared/pipes/enum-to-string';
import { DateTimeToDatePipe } from 'src/app/shared/pipes/dateTime-to-date';
import { Observable, map, of } from 'rxjs';
import { Permissions } from 'src/app/shared/enums/Permission';
import { Permission } from 'src/app/shared/models/role/permission';
import { UserType } from 'src/app/shared/enums/UserType';
import { UserService } from 'src/app/services/User.service';
import { UserDTO } from 'src/app/shared/models/user/UserDTO';
import { ResponseAPI } from 'src/app/shared/models/ResponseAPI';
import { catchError } from 'rxjs/operators';


@Component({
    selector: 'app-role-list',
    templateUrl: './role-list.component.html',
    styleUrls: ['./role-list.component.scss']
})
export class RoleListComponent {
    displayAddEditDialog: boolean = false;
    roles: Role[] = [];
    users: UserDTO[] = [];
    Status = Status;
    rowGroupMetadata: any;

    expandedRows: { [key: string]: boolean } = {};


    activityValues: number[] = [0, 100];

    isExpanded: boolean = false;

    loading: boolean = true;
    StatusList = this.enumToArray(Status).map(item => ({ label: item.label, value: item.label }));
    UserTypeList = this.enumToArray(UserType).map(item => ({ label: item.label, value: item.label }));
    @ViewChild('filter') filter!: ElementRef;
    roleId: string = "";
    permissions: TreeNode<any> | TreeNode<any>[] | any[] | any;
    selectedPermissions: TreeNode<any> | TreeNode<any>[] | any[] | any;
    permissionsByRole: { [key: string]: { permissions: any, selectedPermissions: any } } = {};
    displayDeleteDialog: boolean = false;
    selectedRoleId: string | null = null;
    pageSize: number = 5;
    pageNumber: number = 0;
    first!: number;
    totalRecords: number = 1;
    sortBy?: string | string[] | null | undefined;
    sortDirection: number = 1;
    currentPage: number = 1;
    expandCollapseButtonText: string = 'Expand All';

    constructor(private roleService: RoleManagementService, private messageService: MessageService,
        private userService: UserService) {
        this.expandedRows = {};
    }

    ngOnInit() {

        this.userService.getAllUsers();
        console.log("users", this.userService.getAllUsers());
        this.getrolelist(); // Load initial data

        //     const storedExpandedRows = localStorage.getItem('expandedRows');
        // if (storedExpandedRows) {
        //     this.expandedRows = JSON.parse(storedExpandedRows);
        //     // Check if any rows are expanded
        //     this.isExpanded = Object.values(this.expandedRows).some(value => value);
        // }

        // // Restore pagination parameters from local storage
        // const storedPaginationParams = localStorage.getItem('paginationParams');
        // if (storedPaginationParams) {
        //     const { first, pageSize, sortBy, sortDirection } = JSON.parse(storedPaginationParams);
        //     this.first = first;
        //     this.pageSize = pageSize;
        //     this.sortBy = sortBy;
        //     this.sortDirection = sortDirection;
        //     this.currentPage = Math.floor(this.first / this.pageSize) + 1;
        // }
    }

    getStatusString(statusValue: number): string {
        return Status[statusValue];
    }
    getUserTypeString(periodTypeValue: number): string {
        return UserType[periodTypeValue];
    }
    enumToArray(enumerable: any): SelectItem[] {
        return Object.keys(enumerable)
            .filter(key => !isNaN(Number(enumerable[key])))
            .map(key => ({ label: key, value: enumerable[key] }));
    }

    lazyLoadRoles(event: TableLazyLoadEvent) {
        this.expandedRows = {};
        this.first = event.first || 0;
        this.sortBy = event.sortField ?? '';
        this.sortDirection = event.sortOrder as number;
        this.currentPage = Math.floor(this.first / this.pageSize) + 1;
        this.getrolelist(event.filters as { [s: string]: FilterMetadata } | undefined);
        this.isExpanded=false;

    }

    // expandAll() {
    //     this.isExpanded = !this.isExpanded;
    //     if (this.isExpanded) {
    //         this.expandedRows = this.roles.reduce((acc, role) => {
    //             acc[role.id.toString()] = true;
    //             return acc;
    //         }, {} as { [key: string]: boolean });
    //     } else {
    //         this.expandedRows = {};
    //     }
    // }

    expandAll() {
        if (!this.isExpanded) {
            this.roles.forEach(role => role && role.id ? this.expandedRows[role.id] = true : '');

        } else {
            this.expandedRows = {};
        }
        this.isExpanded = !this.isExpanded;
    }

    getrolelist(filters?: { [s: string]: FilterMetadata } | undefined) {
        this.pageNumber = (this.first / this.pageSize) + 1
        const sortBy = this.sortBy as string;
        const sortDirection = this.sortDirection;
        this.roleService.getAllPagedRoles(this.pageSize, this.pageNumber, sortBy, sortDirection, filters).subscribe(roles => {
            this.roles = []
            if (roles.body && roles.body.objectValue) {
                let rolesList = roles.body.objectValue;
                if (rolesList.length > 0) {
                    this.roles = this.roles.concat(rolesList);
                    this.getPermissionsByRole(this.roles)
                    // roles.forEach(role => {
                    //     this.updatePermissionInRole(role);
                    //   });
                    const xPaginationHeader = roles.headers.get('x-pagination');
                    if (xPaginationHeader) {
                        const xPagination = JSON.parse(xPaginationHeader);


                        this.totalRecords = xPagination.TotalCount;
                    } else {
                        console.error('x-pagination header not found in the response');
                    }
                    this.loading = false;
                }
            }
        });
    }

    getPermissionsByRole(roles: Role[]): {} {
        this.permissionsByRole = {}
        roles.forEach(role => {
            this.selectedPermissions = []
            let permissions = role.permissions;
            let rootPermission: Permission[] = [{
                id: '0',
                name: 'Permissions',
                partialSelected: true,
                isSelectable: false,
                permissions: permissions
            }]
            let permissionTreeData = this.getPermissionsTreeData(rootPermission)
            this.getSelectedPermissions(permissionTreeData)
            if (role.id)
                this.permissionsByRole[role.id] = { permissions: permissionTreeData, selectedPermissions: this.selectedPermissions }
        });
        return this.permissionsByRole;
    }

    getPermissionsTreeData(data: Permission[]): TreeNode[] {
        return data.map(item => ({
            label: item.name,
            data: item.id,
            partialSelected: item.partialSelected,
            selectable: false,
            //expanded: item.permissions.length > 0,
            children: item.permissions.length > 0 ? this.getPermissionsTreeData(item.permissions) : []
        }));
    }

    getSelectedPermissions(nodes: TreeNode[]) {
        for (const node of nodes) {
            if (node.partialSelected == false) {
                this.selectedPermissions.push(node);
            }
            if (node.children) {
                this.getSelectedPermissions(node.children);
            }
        }
    }


    clear(table: Table) {
        table.clear();
        this.filter.nativeElement.value = '';
    }

    onGlobalFilter(table: Table, event: Event) {
        table.filterGlobal((event.target as HTMLInputElement).value, 'contains');
    }

    displayRoleDialog() {
        this.roleId = ""
        this.displayAddEditDialog = true;
        return this.displayAddEditDialog;
    }

    closeAddDialogEvent(e: Event) {
        this.displayAddEditDialog = false;
    }

    editRole(roleId: string) {
        this.displayAddEditDialog = true;
        this.roleId = roleId;
    }

    showDeleteConfirmation(id: string) {
        if (id !== undefined) {
            this.isRoleAttributedToUser(id).subscribe((isAttributed: boolean) => {
                if (isAttributed) {
                    this.messageService.add({ severity: 'warn', summary: 'Warning', detail: 'Role cannot be deleted because it is attributed to users.' });
                    this.selectedRoleId = null; // Reset selectedRoleId when the role is attributed to users
                } else {
                    this.selectedRoleId = id; // Set selectedRoleId when the role is not attributed to users
                    this.displayDeleteDialog = true;
                }
            });
        }
    }

    confirmDelete() {
        if (this.selectedRoleId) {
            this.roleService.deleteRole(this.selectedRoleId).subscribe(
                (response) => {
                    this.displayDeleteDialog = false;
                    this.messageService.add({ key: 'toast', severity: 'success', summary: 'Role deleted successfully', detail: response.exceptionMessage });
                    this.getrolelist();
                    this.selectedRoleId = null; // Reset selectedRoleId after successful delete
                },
                (error) => {
                    this.displayDeleteDialog = false;
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error occurred while deleting role', detail: error.error.exceptionMessage });
                }
            );
        }
    }

    onCancelDelete() {
        this.displayDeleteDialog = false;
    }

    onElementDeleted() {
        // this.loadRoles();
    }

    updateConfirmed(e: Event) {
        this.displayAddEditDialog = false
        this.getrolelist()
    }

    isClientOrSuperAdminRole(roleName: string): boolean {
        if (roleName.toUpperCase() == "CLIENT" || roleName.toUpperCase() == "SUPERADMIN" || roleName.toUpperCase() == "ADMIN" || roleName.toUpperCase() == "SHOP ADMIN" || roleName.toUpperCase() == "CASHIER") {
            return true;
        }
        else {
            return false;
        }
    }


    isRoleAttributedToUser(roleId: string): Observable<boolean> {
        return this.userService.getUsersByRole(roleId).pipe(
            map((response: ResponseAPI<UserDTO[]>) => {
                if (response && response.objectValue) {
                    this.users = response.objectValue;
                    return Array.isArray(this.users) && this.users.length > 0;
                } else {
                    return false;
                }
            })
        );
    }
}


