import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UserListComponent } from './user-list/user-list.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AutoCompleteModule } from "primeng/autocomplete";
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { ChipsModule } from "primeng/chips";
import { InputMaskModule } from "primeng/inputmask";
import { InputNumberModule } from "primeng/inputnumber";
import { CascadeSelectModule } from "primeng/cascadeselect";
import { MultiSelectModule } from "primeng/multiselect";
import { InputTextareaModule } from "primeng/inputtextarea";
import { InputTextModule } from "primeng/inputtext";
import { UserRoutingModule } from './user-routing.module';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { ConfirmationService } from 'primeng/api';
import { MessageService } from 'primeng/api';
import { ChipModule } from 'primeng/chip';
import { UserAddEditComponent } from './user-add-edit/user-add-edit.component';
import { DialogModule } from 'primeng/dialog';
import { ConfirmationDialogueModule } from 'src/app/shared/components/confirmation-dialog/confirmation-dialogue.module';
import { MessageModule } from 'primeng/message';
import { CheckboxModule } from 'primeng/checkbox';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { TooltipModule } from 'primeng/tooltip';
@NgModule({
    declarations: [
        UserListComponent,
        UserAddEditComponent
    ],
    imports: [
        CommonModule,
        FormsModule,
        AutoCompleteModule,
        CalendarModule,
        ChipsModule,
        ChipModule,
        DropdownModule,
        InputMaskModule,
        InputNumberModule,
        CascadeSelectModule,
        MultiSelectModule,
        InputTextareaModule,
        InputTextModule,
        UserRoutingModule,
        TableModule,
        ToastModule,
        ConfirmationDialogueModule,
        ChipModule,
        DialogModule,
        ReactiveFormsModule,
        MessageModule,
        CheckboxModule,
        ProgressSpinnerModule,
        TooltipModule
    ],
    providers: [ConfirmationService, MessageService]
})
export class UserModule { }
