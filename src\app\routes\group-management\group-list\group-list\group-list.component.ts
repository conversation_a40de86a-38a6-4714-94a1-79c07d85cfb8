import { Group } from 'src/app/shared/models/Group/group';
import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { ConfirmationService, FilterMetadata, SelectItem } from 'primeng/api';
import { Status } from 'src/app/shared/enums/status';
import { MessageService } from 'primeng/api';
import { GroupService } from 'src/app/services/group.service';
import { TicketService } from 'src/app/services/ticket.service';
import { Ticket } from 'src/app/shared/models/ticket/ticket';
import { Transaction } from 'src/app/services/transaction.service';
import { WalletService } from 'src/app/services/wallet.service';
interface expandedRows {
    [key: string]: boolean;
}
@Component({
    selector: 'app-group-list',
    templateUrl: './group-list.component.html',
    styleUrls: ['./group-list.component.scss']
})
export class GroupListComponent {
    groups: Group[] = [];
    Status = Status;
    displayAddEditDialog: boolean = false;
    displayDeleteDialog: boolean = false;
    displaySendDialog: boolean = false;
    selectedGroupId: string = "";
    rowGroupMetadata: any;
    selectedGroup: Group | null = null;
    expandedRows: expandedRows = {};
    activityValues: number[] = [0, 100];
    walletAmount: number = 0;
    isExpanded: boolean = false;
    idFrozen: boolean = false;
    loading: boolean = true;
    StatusList = this.enumToArray(Status);
    StatusList2 = this.enumToArray(Status).map(item => ({ label: item.label, value: item.label }));
    pageSize: number = 5;
    pageNumber: number = 0;
    sortBy?: string | string[] | null | undefined;
    sortDirection: number = 1;
    first!: number;
    totalRecords: number = 1;
    areRowsSelected = false;
    selectedRowIndexes: number[] = [];
    selectedGroups: Group[] = [];
    isDisableSend : boolean = true;
    @ViewChild('filter') filter!: ElementRef;

    constructor(private groupService: GroupService, private ticketService: TicketService,
        private confirmationService: ConfirmationService, private messageService: MessageService,
        private transactionService: Transaction, private walletService: WalletService) { }
    

    ngOnInit() {
        this.walletService.GetUserWallets().subscribe(response => {
            if (response.statusCode === 200) {
                if(response.objectValue[0] != null) {
                    this.walletAmount = response.objectValue[0].balance;
                }else {
                    this.walletAmount = 0;
                }
                
            } else {
                console.error('Failed to fetch user wallets:', response.exceptionMessage);
            }
        });

        if(this.walletAmount > 0) {
            this.isDisableSend = false;
        }
    }
    onRowSelect(event: any) {
        this.selectedRowIndexes.push(event.index);
        this.selectedGroups.push(event.data);
        this.areRowsSelected = this.selectedRowIndexes.length > 0;
    }

    onRowUnselect(event: any) {
        this.selectedRowIndexes = this.selectedRowIndexes.filter(index => index !== event.index);
        this.selectedGroups = this.selectedGroups.filter(group => group !== event.data);
        this.areRowsSelected = this.selectedRowIndexes.length > 0;
    }

    getStatusString(statusValue: number): string {
        return Status[statusValue];
    }

    enumToArray(enumerable: any): SelectItem[] {
        return Object.keys(enumerable)
            .filter(key => !isNaN(Number(enumerable[key])))
            .map(key => ({ label: key, value: enumerable[key] }));
    }

    enumToArray2(enumObject: any): any[] {
        return Object.keys(enumObject).map(key => ({ label: key, value: enumObject[key] }));
    }

    loadGroups(filters?: { [s: string]: FilterMetadata } | undefined) {
        this.pageNumber = (this.first / this.pageSize) + 1;
        const sortBy = this.sortBy as string;
        const sortDirection = this.sortDirection;
        filters = filters || {};
        this.groupService.getAllPaginationGroups(this.pageSize, this.pageNumber, sortBy, sortDirection, filters)
            .subscribe(response => {
                if (response.body && response.body.objectValue) {
                    this.groups = response.body.objectValue;
                    const xPaginationHeader = response.headers.get('x-pagination');
                    if (xPaginationHeader) {
                        const xPagination = JSON.parse(xPaginationHeader);
                        this.totalRecords = xPagination.TotalCount;
                    } else {
                        console.error('x-pagination header not found in the response');
                    }

                    this.groups.forEach(group => {
                        group.ticketNames = [];
                        group.groupTickets.forEach(groupTicket => {

                            group.ticketNames?.push(groupTicket.ticket.name);
                        });
                    });

                    this.loading = false;
                }
            },
                error => {
                    console.error('Error occurred while loading groups:', error);
                });
    }

    onSort() {
        this.updateRowsOrder();
    }

    updateRowsOrder() {
        this.rowGroupMetadata = {};

        if (this.groups) {
            for (let i = 0; i < this.groups.length; i++) {
                const rowData = this.groups[i];
                const representativeName = rowData?.name || '';

                if (i === 0) {
                    this.rowGroupMetadata[representativeName] = { index: 0, size: 1 };
                }
                else {
                    const previousRowData = this.groups[i - 1];
                    const previousRowGroup = previousRowData?.name;
                    if (representativeName === previousRowGroup) {
                        this.rowGroupMetadata[representativeName].size++;
                    }
                    else {
                        this.rowGroupMetadata[representativeName] = { index: i, size: 1 };
                    }
                }
            }
        }
    }

    onGlobalFilter(table: Table, event: Event) {
        table.filterGlobal((event.target as HTMLInputElement).value, 'contains');
    }

    clear(table: Table) {
        table.clear();
        this.filter.nativeElement.value = '';
    }

    showDeleteConfirmation(group: Group) {
        if (group.groupId !== undefined) {
            this.selectedGroupId = group.groupId;
            this.displayDeleteDialog = true;
        }
        else {
            console.log('nope');

        }
    }

    showSendConfirmation(group: Group | null) {
        this.selectedGroup = group;
        this.selectedGroupId = group?.groupId ?? "";
        this.displaySendDialog = true;
        return this.displaySendDialog;
    }

    isGroupActive(group: Group | null): boolean {
        return !!group && group.status == Status.Active && this.isDisableSend;
    }

    confirmSend(tickets: Ticket[]) {
        this.displaySendDialog = false;
        if (this.selectedGroupId !== null) {
            this.transactionService.sendTicketsToGroup(this.selectedGroupId, tickets).subscribe(
                (response) => {

                    if (response.statusCode == 200) {

                        this.messageService.add({ key: 'toast', severity: 'success', summary: 'The tickets send successfully', detail: response.exceptionMessage });

                        this.loadGroups();
                    }
                    else {
                        this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error in sending ticket', detail: response.exceptionMessage });

                    }
                },
                (error) => {

                }
            );
        }
    }

    onCancelSend() {
        this.displaySendDialog = false;
        return this.displaySendDialog;
    }

    confirmDelete() {
        if (this.selectedGroupId !== null) {
            this.groupService.deleteGroup(this.selectedGroupId).subscribe(
                (response) => {

                    if (response.statusCode == 200) {
                        this.displayDeleteDialog = false;
                        this.messageService.add({ key: 'toast', severity: 'success', summary: 'Group deleted successfully', detail: response.exceptionMessage });

                        this.loadGroups();
                    }
                    else {
                        this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Delete Group', detail: response.exceptionMessage });

                    }
                },
                (error) => {
                    this.displayDeleteDialog = false;

                }
            );
        }
    }

    displayGroupDialog(group: Group | null) {
        this.selectedGroup = group;
        this.displayAddEditDialog = true;
        return this.displayAddEditDialog;
    }

    closeAddEditDialogEvent(e: Event) {
        this.displayAddEditDialog = false;
    }

    onGroupAdded() {
        this.loadGroups();
    }
    onElementDeleted() {
        this.loadGroups();
    }
    onCancelDelete() {
        this.displayDeleteDialog = false;
    }
    lazyLoadGroups(event: TableLazyLoadEvent) {
        this.first = event.first || 0;
        this.sortBy = event.sortField ?? '';
        this.sortDirection = event.sortOrder as number;
        this.loadGroups(event.filters as { [s: string]: FilterMetadata } | undefined);
    }
}
