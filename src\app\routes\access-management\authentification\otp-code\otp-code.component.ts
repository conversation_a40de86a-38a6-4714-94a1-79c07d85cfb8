import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { VerifOTPCodeDTO } from 'src/app/shared/models/OTP/VerifOTPCodeDTO';
import { LocalStoreService } from 'src/app/services/local-store.service';
import { DeviceIdentifierService } from 'src/app/services/device-indentifier.service';
import { AuthService } from 'src/app/services/auth.service';
import { AuthDataService } from 'src/app/services/authDataService.service';
import { UserType } from 'src/app/shared/enums/UserType';
import { tokenDTO } from 'src/app/shared/models/authentification/token';
import jwtDecode from 'jwt-decode';
@Component({
    templateUrl: './otp-code.component.html'
})
export class OtpCodeComponent {
    otpForm: FormGroup;
    val1!: number;
    val2!: number;
    val3!: number;
    val4!: number;
    mailverif: string = '';
    valSwitch: boolean = false;
    isUserSuperAdmin: boolean = false;
    loading = false;
    constructor(private layoutService: LayoutService, private activatedRoute: ActivatedRoute, private route: Router,
        private messageService: MessageService, private formBuilder: FormBuilder, private deviceIdenfier: DeviceIdentifierService
        , private authService: AuthService, private localStore: LocalStoreService, private authDataService: AuthDataService,
        private localStorageService: LocalStoreService,) {
        this.otpForm = this.formBuilder.group({
            val1: [, Validators.required],
            val2: [, Validators.required],
            val3: [, Validators.required],
            val4: [, Validators.required]
        });
    }

    ngOnInit(): void {

        // this.mailverif = this.authDataService.getUserEmail()!;
        this.activatedRoute.queryParams.subscribe(params => {
            this.mailverif = params['userEmail'] || '';

        });
        let token = this.localStorageService.getData("Token");
        if (token != undefined) {
            let decodedToken: tokenDTO = jwtDecode(token);
            console.log("decodedToken", decodedToken.Role);
            let Roles = decodedToken.Role;
            if (Roles.includes("SuperAdmin")) {
                this.isUserSuperAdmin = true;
            }
        }

    }

    get dark(): boolean {
        return this.layoutService.config.colorScheme !== 'light';
    }


    onDigitInput(event: any, controlName: string) {
        const inputValue = event.target.value;

        // Existing code for navigation
        let element;
        if (event.code !== 'Backspace')
            if (event.code.includes('Numpad') || event.code.includes('Digit')) {
                element = event.srcElement.nextElementSibling;
            }
        if (event.code === 'Backspace')
            element = event.srcElement.previousElementSibling;

        if (element != null) {
            element.focus();
        }

        // Additional code for form control validation
        const control = this.otpForm.get(controlName);

        if (control && inputValue) {
            control.setValue(inputValue);
        }
    }
    Verify() {
        //this part when sms notification is ready need to change
        //call all the api and method needed to get the sms code sended

        const val1 = this.otpForm.get('val1')?.value;
        const val2 = this.otpForm.get('val2')?.value;
        const val3 = this.otpForm.get('val3')?.value;
        const val4 = this.otpForm.get('val4')?.value;

        const otp: VerifOTPCodeDTO = {
            email: this.mailverif,
            code: val1 + val2 + val3 + val4,
            macAddress: this.deviceIdenfier.getDeviceIdentifier(),
            isSaved: this.valSwitch
        }
        if(otp.code == null|| otp.code == 0|| otp.code.toString().length !== 4){
            this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Message', detail: "Code Invalide" });
            this.loading = false;
            return
        }
        this.loading = true;
        this.authService.VerifOPTCode(otp).subscribe((Response) => {
            if (Response.statusCode == 200) {
                this.loading = false;
                this.localStore.saveData("IsDeviceVerified", 'true');
                const entrepriseType = this.localStore.getData('EntrepriseType');
                if (this.isUserSuperAdmin) {
                    this.route.navigate(['Dashboard/OverviewS']);

                } else if (!this.isUserSuperAdmin) {
                    this.route.navigate(['Dashboard/Overview']);
                }
            } else {
                this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Message', detail: "Code Invalide" });
                this.loading = false;
            }
        })

    }
    cancel() {
        localStorage.clear();

        this.route.navigate(['/auth/login']);
      }


    ngOnDestroy() {
        this.authDataService.clearUserEmail();
    }

}
