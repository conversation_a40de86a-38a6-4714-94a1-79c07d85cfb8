#!/bin/bash

# Script de nettoyage et reconstruction pour résoudre les conflits de dépendances

set -e

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Paramètres
FORCE_CLEAN=false
SKIP_TESTS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --force)
            FORCE_CLEAN=true
            shift
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        *)
            echo "Usage: $0 [--force] [--skip-tests]"
            exit 1
            ;;
    esac
done

echo -e "${BLUE}🧹 Nettoyage et reconstruction de l'environnement Docker${NC}"
echo "======================================================="

# Étape 1: Arrêter tous les services
print_status "Arrêt de tous les services Docker..."
docker-compose down -v --remove-orphans 2>/dev/null || true
print_success "Services arrêtés"

# Étape 2: Nettoyage complet si demandé
if [ "$FORCE_CLEAN" = true ]; then
    print_status "Nettoyage complet des images Docker..."
    
    # Supprimer les images du projet
    docker images | grep dyno | awk '{print $3}' | xargs -r docker rmi -f 2>/dev/null || true
    
    # Nettoyer le système Docker
    docker system prune -f
    docker volume prune -f
    
    print_success "Images Docker nettoyées"
fi

# Étape 3: Nettoyer les caches locaux
print_status "Nettoyage des caches locaux..."

# Supprimer node_modules
if [ -d "node_modules" ]; then
    rm -rf node_modules
    print_success "node_modules supprimé"
fi

# Supprimer package-lock.json
if [ -f "package-lock.json" ]; then
    rm -f package-lock.json
    print_success "package-lock.json supprimé"
fi

# Supprimer les dossiers de build
for dir in dist coverage .angular; do
    if [ -d "$dir" ]; then
        rm -rf "$dir"
        print_success "$dir supprimé"
    fi
done

# Étape 4: Créer un package.json optimisé pour les tests
print_status "Création d'un package.json optimisé..."

# Backup du package.json original
cp package.json package.json.backup

# Créer une version simplifiée
cat > package.json.test << 'EOF'
{
  "name": "apollo-ng",
  "version": "0.0.0",
  "scripts": {
    "ng": "ng",
    "start": "ng serve",
    "build": "ng build",
    "build:prod": "ng build --configuration production",
    "build:staging": "ng build --configuration staging",
    "test": "ng test",
    "test:ci": "ng test --watch=false --browsers=ChromeHeadless --code-coverage",
    "lint": "ng lint"
  },
  "dependencies": {
    "@angular/animations": "^17.3.3",
    "@angular/cdk": "^16.2.8",
    "@angular/common": "^17.3.3",
    "@angular/compiler": "^17.3.3",
    "@angular/core": "^17.3.3",
    "@angular/forms": "^17.3.3",
    "@angular/platform-browser": "^17.3.3",
    "@angular/platform-browser-dynamic": "^17.3.3",
    "@angular/router": "^17.3.3",
    "primeng": "^16.0.2",
    "primeicons": "6.0.1",
    "primeflex": "^3.3.1",
    "rxjs": "~7.8.0",
    "tslib": "^2.3.0",
    "zone.js": "~0.14.4",
    "jwt-decode": "^3.1.2",
    "uuid": "^9.0.1"
  },
  "devDependencies": {
    "@angular-devkit/build-angular": "^17.3.3",
    "@angular/cli": "~17.3.3",
    "@angular/compiler-cli": "^17.3.3",
    "@types/jasmine": "~4.3.0",
    "@types/uuid": "^9.0.6",
    "jasmine-core": "~4.6.0",
    "karma": "~6.4.0",
    "karma-chrome-launcher": "~3.2.0",
    "karma-coverage": "~2.2.0",
    "karma-jasmine": "~5.1.0",
    "karma-jasmine-html-reporter": "~2.1.0",
    "typescript": "~5.4.4"
  }
}
EOF

# Utiliser le package.json de test
cp package.json.test package.json

# Étape 5: Construction
print_status "Construction avec le package.json optimisé..."

if docker-compose build --no-cache angular-dev; then
    print_success "Construction réussie"
    
    # Démarrer les services
    print_status "Démarrage des services de test..."
    docker-compose --profile testing up -d postgres-test redis mock-api
    
    print_status "Démarrage de l'application..."
    docker-compose up -d angular-dev
    
    # Attendre que l'application soit prête
    print_status "Attente que l'application soit prête..."
    timeout=180
    counter=0
    
    while ! curl -s http://localhost:4200 > /dev/null; do
        if [ $counter -ge $timeout ]; then
            print_error "Timeout: L'application n'a pas démarré"
            break
        fi
        sleep 5
        counter=$((counter + 5))
        echo -n "."
    done
    
    if [ $counter -lt $timeout ]; then
        echo ""
        print_success "Application démarrée sur http://localhost:4200"
        
        # Tests automatisés (optionnel)
        if [ "$SKIP_TESTS" = false ]; then
            print_status "Exécution des tests automatisés..."
            docker-compose --profile testing run --rm angular-test npm run test:ci || true
        fi
        
        echo ""
        echo -e "${GREEN}🎉 Environnement prêt pour les tests manuels!${NC}"
        echo -e "   Application: ${BLUE}http://localhost:4200${NC}"
        echo -e "   API Mock: ${BLUE}http://localhost:1080${NC}"
        echo ""
        echo -e "${YELLOW}Pour arrêter: docker-compose down${NC}"
    fi
else
    print_error "Échec de la construction"
fi

# Restaurer le package.json original
print_status "Restauration du package.json original..."
cp package.json.backup package.json
rm -f package.json.test package.json.backup

print_success "Script terminé"
