.heartbeat {
    width: 50px;
    height: 50px;
    background-color: green; /* Initial color */
    border-radius: 50%;
    animation: pulse 1s infinite; /* Apply the pulse animation */
  }

  .heartbeat-orange {
    background-color: orange; /* Color when microservice is orange */
    animation: pulseOrange 1s infinite; /* Apply the red pulse animation */
  }
  
  .heartbeat-red {
    background-color: red; /* Color when microservice is down */
    animation: pulseRed 1s infinite; /* Apply the red pulse animation */
  }
  
  @keyframes pulse {
    0% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(0, 128, 0, 0.7); /* Adjust color and opacity */
    }
    50% {
      transform: scale(1.2);
      box-shadow: 0 0 0 25px rgba(0, 128, 0, 0); /* Adjust spread to control pulse size */
    }
    100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(0, 128, 0, 0);
    }
  }

  @keyframes pulseOrange {
    0% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(255, 165, 0, 0.7);; /* Adjust red color and opacity */
    }
    50% {
      transform: scale(1.2);
      box-shadow: 0 0 0 25px rgba(255, 0, 0, 0); /* Adjust spread to control red pulse size */
    }
    100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(255, 0, 0, 0);
    }
  }

  @keyframes pulseRed {
    0% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.7); /* Adjust red color and opacity */
    }
    50% {
      transform: scale(1.2);
      box-shadow: 0 0 0 25px rgba(255, 0, 0, 0); /* Adjust spread to control red pulse size */
    }
    100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(255, 0, 0, 0);
    }
  }