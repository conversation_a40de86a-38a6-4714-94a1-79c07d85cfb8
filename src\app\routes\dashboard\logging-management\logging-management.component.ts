import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { LogErrorService } from 'src/app/services/log.service';
import { WatchDogNotificationService } from 'src/app/services/watchDogNotifiction.service';
import { ErrorType } from 'src/app/shared/enums/ErrorType';
import { LogLevel } from 'src/app/shared/enums/LogLevel';
import { MicroserviceName } from 'src/app/shared/enums/MicroserviceName';
import { State } from 'src/app/shared/enums/State';
import { LogError } from 'src/app/shared/models/Log/LogError';
import { LogNotification } from 'src/app/shared/models/notification/logNotification';


@Component({
    selector: 'logging-management',
    templateUrl: './logging-management.component.html',
    styleUrls: ['./logging-management.component.scss']
  })
  
export class LoggingManagementComponent implements OnInit, OnDestroy
{
  recentLogs: LogError[] = [];
  subscription: Subscription;
  

  IsAccessMicroserviceOrange: boolean = false;
  IsPaymentMicroserviceOrange: boolean = false;
  IsNotificationMicroserviceOrange: boolean = false;

  IsAccessMicroserviceDown: boolean = false;
  IsPaymentMicroserviceDown: boolean = false;
  IsNotificationMicroserviceDown: boolean = false;

  notifications: LogNotification[] = [];

  constructor(private logErrorService : LogErrorService,
    private layoutService: LayoutService, private signalRService: WatchDogNotificationService,
    private ngZone: NgZone) 
  {
    this.subscription = this.layoutService.configUpdate$.subscribe(config => {
    });
  }

  ngOnInit() {
    this.getLogErrors();
    this.notifications = this.signalRService.notifications;
    this.subscription = this.signalRService.notificationReceived$.subscribe((notification) => {
      this.ngZone.run(() => {
        this.handleNotification(notification);
      });
    });
  }

  private handleNotification(notification: LogNotification): void {
    if (notification.microserviceName == 'Access Management') {
      if(notification.state == State.Green) {
        this.IsAccessMicroserviceDown = false;
        this.IsAccessMicroserviceOrange = false;
      }
      if (notification.state == State.Orange) {
        this.IsAccessMicroserviceOrange = true;
      }
      if (notification.state == State.Red) {
        this.IsAccessMicroserviceDown = true;
      }
    }
    if (notification.microserviceName == 'Payment') {
      if(notification.state == State.Green) {
        this.IsPaymentMicroserviceDown = false;
        this.IsPaymentMicroserviceOrange = false;
      }
      if (notification.state == State.Orange) {
        this.IsPaymentMicroserviceOrange = true;
      }
      if (notification.state == State.Red) {
        this.IsPaymentMicroserviceDown = true;
      }
    }
    if (notification.microserviceName == 'Notification') {
      if(notification.state == State.Green) {
        this.IsNotificationMicroserviceDown = false;
        this.IsNotificationMicroserviceOrange = false;
      }
      if (notification.state == State.Orange) {
        this.IsNotificationMicroserviceOrange = true;
      }
      if (notification.state == State.Red) {
        this.IsNotificationMicroserviceDown = true;
      }
    }
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  getLogErrors () {
    this.logErrorService.getAllPaged(1, 5).subscribe(
      (response) => {
          if (response.objectValue) {
              this.recentLogs = response.objectValue;
          }
      });
  }

  getErrorTypeString(errorType: number): string {
    return ErrorType[errorType];
  }

  getlevelTypeString(level: number): string {
    return LogLevel[level];
  }

  getMicroserviceTypeString(microservice: number): string {
    return MicroserviceName[microservice];
  }

}