import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, map, catchError, forkJoin } from 'rxjs';
import { 
  AdvancedRole, 
  Permission, 
  RoleTemplate, 
  RoleAssignment, 
  RoleHierarchy, 
  RoleAnalytics, 
  RoleConflict,
  PermissionAction 
} from '../shared/models/role/advanced-role';
import { BaseCrudService } from '../shared/services/base-crud.service';
import { ConfigService } from './config.service';
import { ErrorHandlerService } from './error-handler.service';
import { AuditLogService } from './audit-log.service';
import { AuditAction, AuditEntityType } from '../shared/models/audit/audit-log';
import { ResponseAPI } from '../shared/models/ResponseAPI';

@Injectable({
  providedIn: 'root'
})
export class AdvancedRoleService extends BaseCrudService<AdvancedRole> {
  protected readonly endpoint = 'AdvancedRole';

  constructor(
    http: HttpClient,
    config: ConfigService,
    errorHandler: ErrorHandlerService,
    private auditService: AuditLogService
  ) {
    super(http, config, errorHandler);
  }

  /**
   * Récupère toutes les permissions disponibles
   */
  getAllPermissions(): Observable<Permission[]> {
    return this.http.get<ResponseAPI<Permission[]>>(
      this.getFullUrl('GetAllPermissions')
    ).pipe(
      map(response => response.objectValue || []),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Récupère les permissions par module
   */
  getPermissionsByModule(module: string): Observable<Permission[]> {
    return this.http.get<ResponseAPI<Permission[]>>(
      this.getFullUrl(`GetPermissionsByModule/${module}`)
    ).pipe(
      map(response => response.objectValue || []),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Crée un rôle avec audit
   */
  override create(role: AdvancedRole): Observable<AdvancedRole> {
    return super.create(role).pipe(
      map(createdRole => {
        this.auditService.logAction(
          AuditAction.CREATE,
          AuditEntityType.ROLE,
          createdRole.id!,
          createdRole.displayName,
          null,
          createdRole,
          { permissionCount: role.permissions.length }
        ).subscribe();
        return createdRole;
      })
    );
  }

  /**
   * Met à jour un rôle avec audit
   */
  override update(role: AdvancedRole): Observable<AdvancedRole> {
    return forkJoin({
      oldRole: this.getById(role.id!),
      updatedRole: super.update(role)
    }).pipe(
      map(({ oldRole, updatedRole }) => {
        this.auditService.logAction(
          AuditAction.UPDATE,
          AuditEntityType.ROLE,
          updatedRole.id!,
          updatedRole.displayName,
          oldRole,
          updatedRole,
          { 
            permissionChanges: this.calculatePermissionChanges(oldRole, updatedRole)
          }
        ).subscribe();
        return updatedRole;
      })
    );
  }

  /**
   * Supprime un rôle avec vérifications
   */
  override delete(id: string): Observable<void> {
    return this.getById(id).pipe(
      map(role => {
        if (role.isSystemRole) {
          throw new Error('Impossible de supprimer un rôle système');
        }
        if (role.userCount && role.userCount > 0) {
          throw new Error('Impossible de supprimer un rôle assigné à des utilisateurs');
        }
        return role;
      }),
      map(role => {
        this.auditService.logAction(
          AuditAction.DELETE,
          AuditEntityType.ROLE,
          id,
          role.displayName,
          role,
          null
        ).subscribe();
        return super.delete(id);
      }),
      map(() => void 0),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Assigne un rôle à un utilisateur
   */
  assignRoleToUser(assignment: RoleAssignment): Observable<RoleAssignment> {
    return this.http.post<ResponseAPI<RoleAssignment>>(
      this.getFullUrl('AssignRole'),
      assignment
    ).pipe(
      map(response => {
        const result = response.objectValue!;
        this.auditService.logAction(
          AuditAction.UPDATE,
          AuditEntityType.USER,
          assignment.userId,
          undefined,
          null,
          assignment,
          { roleAssignment: true, roleId: assignment.roleId }
        ).subscribe();
        return result;
      }),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Révoque un rôle d'un utilisateur
   */
  revokeRoleFromUser(userId: string, roleId: string): Observable<void> {
    return this.http.delete<ResponseAPI<void>>(
      this.getFullUrl(`RevokeRole/${userId}/${roleId}`)
    ).pipe(
      map(() => {
        this.auditService.logAction(
          AuditAction.UPDATE,
          AuditEntityType.USER,
          userId,
          undefined,
          null,
          null,
          { roleRevocation: true, roleId }
        ).subscribe();
        return void 0;
      }),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Récupère la hiérarchie des rôles
   */
  getRoleHierarchy(): Observable<RoleHierarchy[]> {
    return this.http.get<ResponseAPI<RoleHierarchy[]>>(
      this.getFullUrl('GetHierarchy')
    ).pipe(
      map(response => response.objectValue || []),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Récupère les rôles d'un utilisateur
   */
  getUserRoles(userId: string): Observable<AdvancedRole[]> {
    return this.http.get<ResponseAPI<AdvancedRole[]>>(
      this.getFullUrl(`GetUserRoles/${userId}`)
    ).pipe(
      map(response => response.objectValue || []),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Vérifie si un utilisateur a une permission spécifique
   */
  checkUserPermission(userId: string, permission: string, resource?: string): Observable<boolean> {
    const params = this.createHttpParams({
      userId,
      permission,
      resource
    });

    return this.http.get<ResponseAPI<boolean>>(
      this.getFullUrl('CheckPermission'),
      { params }
    ).pipe(
      map(response => response.objectValue || false),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Récupère les templates de rôles
   */
  getRoleTemplates(): Observable<RoleTemplate[]> {
    return this.http.get<ResponseAPI<RoleTemplate[]>>(
      this.getFullUrl('GetTemplates')
    ).pipe(
      map(response => response.objectValue || []),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Crée un rôle à partir d'un template
   */
  createFromTemplate(templateId: string, roleName: string, customizations?: any): Observable<AdvancedRole> {
    const requestBody = {
      templateId,
      roleName,
      customizations: customizations || {}
    };

    return this.http.post<ResponseAPI<AdvancedRole>>(
      this.getFullUrl('CreateFromTemplate'),
      requestBody
    ).pipe(
      map(response => {
        const role = response.objectValue!;
        this.auditService.logAction(
          AuditAction.CREATE,
          AuditEntityType.ROLE,
          role.id!,
          role.displayName,
          null,
          role,
          { createdFromTemplate: templateId }
        ).subscribe();
        return role;
      }),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Analyse les rôles pour détecter les conflits
   */
  analyzeRoleConflicts(): Observable<RoleConflict[]> {
    return this.http.get<ResponseAPI<RoleConflict[]>>(
      this.getFullUrl('AnalyzeConflicts')
    ).pipe(
      map(response => response.objectValue || []),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Récupère les analytics des rôles
   */
  getRoleAnalytics(): Observable<RoleAnalytics[]> {
    return this.http.get<ResponseAPI<RoleAnalytics[]>>(
      this.getFullUrl('GetAnalytics')
    ).pipe(
      map(response => response.objectValue || []),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Clone un rôle existant
   */
  cloneRole(roleId: string, newName: string, newDisplayName?: string): Observable<AdvancedRole> {
    const requestBody = {
      sourceRoleId: roleId,
      newName,
      newDisplayName: newDisplayName || newName
    };

    return this.http.post<ResponseAPI<AdvancedRole>>(
      this.getFullUrl('Clone'),
      requestBody
    ).pipe(
      map(response => {
        const clonedRole = response.objectValue!;
        this.auditService.logAction(
          AuditAction.CREATE,
          AuditEntityType.ROLE,
          clonedRole.id!,
          clonedRole.displayName,
          null,
          clonedRole,
          { clonedFrom: roleId }
        ).subscribe();
        return clonedRole;
      }),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Exporte les rôles et permissions
   */
  exportRoles(format: 'json' | 'csv' | 'excel' = 'json'): Observable<Blob> {
    return this.http.get(
      this.getFullUrl(`Export?format=${format}`),
      { responseType: 'blob' }
    ).pipe(
      map(blob => {
        this.auditService.logAction(
          AuditAction.EXPORT,
          AuditEntityType.ROLE,
          'all',
          'Export des rôles',
          null,
          null,
          { format }
        ).subscribe();
        return blob;
      }),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Importe des rôles
   */
  importRoles(file: File, options?: { overwrite?: boolean; validate?: boolean }): Observable<{ imported: number; errors: string[] }> {
    const formData = new FormData();
    formData.append('file', file);
    if (options) {
      formData.append('options', JSON.stringify(options));
    }

    return this.http.post<ResponseAPI<{ imported: number; errors: string[] }>>(
      this.getFullUrl('Import'),
      formData
    ).pipe(
      map(response => {
        const result = response.objectValue!;
        this.auditService.logAction(
          AuditAction.IMPORT,
          AuditEntityType.ROLE,
          'bulk',
          'Import des rôles',
          null,
          null,
          { fileName: file.name, imported: result.imported, errors: result.errors.length }
        ).subscribe();
        return result;
      }),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  // Méthodes utilitaires privées
  private calculatePermissionChanges(oldRole: AdvancedRole, newRole: AdvancedRole): any {
    const oldPermissions = oldRole.permissions.map(p => p.name);
    const newPermissions = newRole.permissions.map(p => p.name);
    
    const added = newPermissions.filter(p => !oldPermissions.includes(p));
    const removed = oldPermissions.filter(p => !newPermissions.includes(p));
    
    return {
      added,
      removed,
      totalBefore: oldPermissions.length,
      totalAfter: newPermissions.length
    };
  }
}
