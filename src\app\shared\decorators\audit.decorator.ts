import { tap, catchError } from 'rxjs/operators';
import { AuditAction, AuditEntityType } from '../models/audit/audit-log';

export interface AuditConfig {
  action: AuditAction;
  entityType: AuditEntityType;
  entityIdParam?: string;
  entityNameParam?: string;
  captureArgs?: boolean;
  captureResult?: boolean;
  ignoreErrors?: boolean;
}

/**
 * Décorateur pour l'audit automatique des méthodes
 * 
 * @example
 * ```typescript
 * @Audit({
 *   action: AuditAction.CREATE,
 *   entityType: AuditEntityType.COMPANY,
 *   entityIdParam: 'id',
 *   captureArgs: true,
 *   captureResult: true
 * })
 * createCompany(company: Company): Observable<Company> {
 *   return this.http.post<Company>(...);
 * }
 * ```
 */
export function Audit(config: AuditConfig) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = function (...args: any[]) {
      const startTime = Date.now();
      
      // Récupérer le service d'audit si disponible
      const auditService = this.auditService || this.injector?.get('AuditLogService');
      
      if (!auditService) {
        console.warn('AuditLogService non disponible pour l\'audit automatique');
        return method.apply(this, args);
      }

      // Extraire les informations de l'entité
      const entityId = config.entityIdParam ? 
        this.extractValueFromArgs(args, config.entityIdParam) : 
        'unknown';
      
      const entityName = config.entityNameParam ? 
        this.extractValueFromArgs(args, config.entityNameParam) : 
        undefined;

      // Capturer les arguments si demandé
      const capturedArgs = config.captureArgs ? 
        this.sanitizeForAudit(args) : 
        undefined;

      try {
        const result = method.apply(this, args);

        // Si c'est un Observable, intercepter le résultat
        if (result && typeof result.subscribe === 'function') {
          return result.pipe(
            tap((response: any) => {
              const executionTime = Date.now() - startTime;
              const capturedResult = config.captureResult ? 
                this.sanitizeForAudit(response) : 
                undefined;

              auditService.logAction(
                config.action,
                config.entityType,
                entityId,
                entityName,
                capturedArgs,
                capturedResult,
                {
                  method: propertyName,
                  executionTime,
                  timestamp: new Date().toISOString()
                }
              ).subscribe({
                error: (error: any) => {
                  if (!config.ignoreErrors) {
                    console.error('Erreur lors de l\'audit:', error);
                  }
                }
              });
            }),
            catchError((error: any) => {
              const executionTime = Date.now() - startTime;
              
              auditService.logFailedAction(
                config.action,
                config.entityType,
                entityId,
                error.message || 'Erreur inconnue',
                {
                  method: propertyName,
                  executionTime,
                  args: capturedArgs,
                  error: this.sanitizeForAudit(error)
                }
              ).subscribe({
                error: (auditError: any) => {
                  if (!config.ignoreErrors) {
                    console.error('Erreur lors de l\'audit d\'échec:', auditError);
                  }
                }
              });

              throw error;
            })
          );
        } else {
          // Pour les méthodes synchrones
          const executionTime = Date.now() - startTime;
          const capturedResult = config.captureResult ? 
            this.sanitizeForAudit(result) : 
            undefined;

          auditService.logAction(
            config.action,
            config.entityType,
            entityId,
            entityName,
            capturedArgs,
            capturedResult,
            {
              method: propertyName,
              executionTime,
              timestamp: new Date().toISOString()
            }
          ).subscribe({
            error: (error: any) => {
              if (!config.ignoreErrors) {
                console.error('Erreur lors de l\'audit:', error);
              }
            }
          });

          return result;
        }
      } catch (error: any) {
        const executionTime = Date.now() - startTime;
        
        auditService.logFailedAction(
          config.action,
          config.entityType,
          entityId,
          error.message || 'Erreur inconnue',
          {
            method: propertyName,
            executionTime,
            args: capturedArgs,
            error: this.sanitizeForAudit(error)
          }
        ).subscribe({
          error: (auditError: any) => {
            if (!config.ignoreErrors) {
              console.error('Erreur lors de l\'audit d\'échec:', auditError);
            }
          }
        });

        throw error;
      }
    };

    // Ajouter les méthodes utilitaires au prototype si elles n'existent pas
    if (!target.extractValueFromArgs) {
      target.extractValueFromArgs = function(args: any[], paramName: string): string {
        // Logique pour extraire la valeur selon le nom du paramètre
        if (args.length === 0) return 'unknown';
        
        const firstArg = args[0];
        if (typeof firstArg === 'object' && firstArg !== null) {
          return firstArg[paramName] || firstArg.id || 'unknown';
        }
        
        return firstArg?.toString() || 'unknown';
      };
    }

    if (!target.sanitizeForAudit) {
      target.sanitizeForAudit = function(data: any): any {
        if (data === null || data === undefined) {
          return data;
        }

        // Cloner l'objet pour éviter les modifications
        let sanitized: any;
        try {
          sanitized = JSON.parse(JSON.stringify(data));
        } catch {
          return { _serialized: data.toString() };
        }

        // Supprimer les champs sensibles
        const sensitiveFields = [
          'password', 'token', 'secret', 'key', 'authorization',
          'creditCard', 'ssn', 'bankAccount', 'pin'
        ];

        function removeSensitiveData(obj: any): any {
          if (typeof obj !== 'object' || obj === null) {
            return obj;
          }

          if (Array.isArray(obj)) {
            return obj.map(item => removeSensitiveData(item));
          }

          const cleaned: any = {};
          for (const [key, value] of Object.entries(obj)) {
            const lowerKey = key.toLowerCase();
            if (sensitiveFields.some(field => lowerKey.includes(field))) {
              cleaned[key] = '[REDACTED]';
            } else {
              cleaned[key] = removeSensitiveData(value);
            }
          }
          return cleaned;
        }

        return removeSensitiveData(sanitized);
      };
    }

    return descriptor;
  };
}

/**
 * Décorateur simplifié pour les actions de lecture
 */
export function AuditRead(entityType: AuditEntityType, entityIdParam?: string) {
  return Audit({
    action: AuditAction.READ,
    entityType,
    entityIdParam,
    captureArgs: false,
    captureResult: false,
    ignoreErrors: true
  });
}

/**
 * Décorateur simplifié pour les actions de création
 */
export function AuditCreate(entityType: AuditEntityType, entityIdParam?: string) {
  return Audit({
    action: AuditAction.CREATE,
    entityType,
    entityIdParam,
    captureArgs: true,
    captureResult: true,
    ignoreErrors: false
  });
}

/**
 * Décorateur simplifié pour les actions de mise à jour
 */
export function AuditUpdate(entityType: AuditEntityType, entityIdParam?: string) {
  return Audit({
    action: AuditAction.UPDATE,
    entityType,
    entityIdParam,
    captureArgs: true,
    captureResult: true,
    ignoreErrors: false
  });
}

/**
 * Décorateur simplifié pour les actions de suppression
 */
export function AuditDelete(entityType: AuditEntityType, entityIdParam?: string) {
  return Audit({
    action: AuditAction.DELETE,
    entityType,
    entityIdParam,
    captureArgs: true,
    captureResult: false,
    ignoreErrors: false
  });
}
