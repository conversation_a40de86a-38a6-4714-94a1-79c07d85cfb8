import { Gender } from "../../enums/Gender";
import { UserType } from "../../enums/UserType";
import { Status } from "../../enums/status";
import { Role } from "../role/role";

export interface UserDTO {
    "id": string,
    "fullName": string,
    "gender": Gender,
    "status": Status;
    "phoneNumber": string;
    "countryCode"?: string;
    "email": string,
    "dateOfBirth": Date,
    "macAddress": string,
    "userType": UserType,
    "roles": Role[],
    "companyId"?: string
}
