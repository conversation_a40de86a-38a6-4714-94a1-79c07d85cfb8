<div class="grid">
    <div class="col-12">
        <div class="card">
            <p-table #dt1 [value]="groups" dataKey="name" [loading]="loading" [rows]="pageSize" [rowHover]="true"
                rowGroupMode="subheader" (onSort)="onSort()" responsiveLayout="scroll"
                styleClass="p-datatable-gridlines" [globalFilterFields]="['name','tickets','status']"
                [totalRecords]="totalRecords" [first]="first" [lazy]="true" (onLazyLoad)="lazyLoadGroups($event)"
                [paginator]="true" [scrollable]="true" >


                <ng-template pTemplate="caption">
                    <div>
                        <p-button pRipple label="Clear" [outlined]="true"icon="pi pi-filter-slash"
                            (click)="clear(dt1)" [pTooltip]="'Clear All Filters'" styleClass="p-button-outlined" [style]="{'margin-right.px': 10}"></p-button>
                        <p-button pRipple  label="Add" [outlined]="true" icon="pi pi-plus"
                            (click)="displayGroupDialog(null)" [pTooltip]="'Add New Group'" styleClass="p-button-outlined" [style]="{'margin-right.px': 10}"></p-button>
                        <p-button pRipple [disabled]="!areRowsSelected"  label="Send" [outlined]="true"
                            icon="pi pi-send" (click)="showSendConfirmation(null)" [pTooltip]="'Send Money'" styleClass="p-button-outlined" [style]="{'margin-right.px': 10}"></p-button>

                        <app-group-add-edit [display]="displayAddEditDialog" [selectedGroup]="selectedGroup"
                            (groupAdded)="onGroupAdded()"
                            (closeAddDialogEvent)="closeAddEditDialogEvent($event)"></app-group-add-edit>

                    </div>
                </ng-template>
                <ng-template pTemplate="header">
                    <tr>
                        <th style="width: 3rem">
                            <p-tableHeaderCheckbox>
                            </p-tableHeaderCheckbox>
                        </th>

                        <th style="min-width: 12rem" pSortableColumn="name">
                            <div class="flex justify-content-between align-items-center">
                                <span>Name</span>
                                <div class="flex align-items-center">
                                  <p-sortIcon field="FullName" pTooltip="Sort Data" pTooltipPosition="right" pTooltipStyleClass="custom-tooltip"></p-sortIcon>
                                  <p-columnFilter pTooltip="Filter Data" type="text" field="FullName" display="menu" placeholder="Search by name"></p-columnFilter>
                              </div>
                          </div>
                        </th>             
             

        <th style="min-width: 10rem" pSortableColumn="status">
            <div class="flex justify-content-between align-items-center">
        <th>Status </th>
        <!--<p-columnFilter field="Status" matchMode="equals" display="menu">
            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                <p-dropdown [ngModel]="value" [options]="StatusList2" (onChange)="filter($event.value)"
                    placeholder="Any" [style]="{'min-width': '12rem'}">
                    <ng-template let-option pTemplate="item">
                        <span [class]="'customer-badge status-' + option.value">{{option.label}}</span>
                    </ng-template>
                </p-dropdown>
            </ng-template>
        </p-columnFilter>-->
    </div>
    </th>

    <th style="min-width: 12rem" pSortableColumn="ticketNames">
        <div class="flex justify-content-between align-items-center">
    <th>Tickets </th>
   <!-- <p-columnFilter type="text" field="GroupTickets.Ticket.Name" display="menu"
        placeholder="Search by tickets" [showAddButton]="false"></p-columnFilter>-->
</div>
</th>
<th style="min-width: 10rem">
    <div class="flex justify-content-between align-items-center">
        Actions
    </div>
</th>

</tr>
</ng-template>
<ng-template pTemplate="body" let-group let-rowIndex="rowIndex">
    <tr [pSelectableRow]="group">
        <td>
            <p-tableCheckbox [value]="group">
            </p-tableCheckbox>
        </td>
        <td>
            {{group.name}}
        </td>
        <td>
            <span [class]="'component-badge status-' + getStatusString(group.status)">
                {{ getStatusString(group.status) }}
            </span>
        </td>
        <td>
            <ng-container *ngFor="let ticketName of group.ticketNames">
                {{ ticketName }}<br> <!-- Display ticket name -->
            </ng-container>
        </td>

        <td>


            <p-button pRipple type="button" [outlined]="true" icon="pi pi-pencil" [pTooltip]="'Edit Group'"
                [style]="{'margin-right.px': '10'}" styleClass="p-button-outlined " (click)="displayGroupDialog(group)">
            </p-button>
            <p-button  Ripple type="button" [outlined]="true" icon="pi pi-trash" [pTooltip]="'Delete Group'"
            [style]="{'margin-right.px': '10'}" styleClass="p-button-outlined " (click)="showDeleteConfirmation(group)" >
            </p-button>
            <p-button pRipple type="button" [outlined]="true" icon="pi pi-send" [pTooltip]="'Send Money To Group'"
            [style]="{'margin-right.px': '10'}" styleClass="p-button-outlined "(click)="showSendConfirmation(group)" 
            [disabled]="!isGroupActive(group)"></p-button>

        </td>

    </tr>
</ng-template>
<ng-template pTemplate="emptymessage">
    <tr>
        <td colspan="8">No groups found.</td>
    </tr>
</ng-template>
<ng-template pTemplate="loadingbody">
    <tr>
        <td colspan="8">Loading groups data. Please wait.</td>
    </tr>
</ng-template>
</p-table>
<!-- Delete Confirmation Component -->
    <app-add-confirmation [display]="displayDeleteDialog" (confirm)="confirmDelete()" (cancelDelete)="onCancelDelete()"
    (elementDeleted)="onElementDeleted()"></app-add-confirmation>

<!-- Send Confirmation Component -->
<app-send-confirmation [display]="displaySendDialog" [selectedGroup]="selectedGroup" (confirm)="confirmSend($event)"
    (cancelSend)="onCancelSend()"></app-send-confirmation>

</div>
</div>
<p-toast key="toast"></p-toast>
</div>
