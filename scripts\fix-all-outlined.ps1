# Script PowerShell pour corriger toutes les erreurs outlined="true" en une fois
# Correction rapide et efficace de tous les templates Angular

Write-Host "🔧 Correction automatique de toutes les erreurs outlined='true'" -ForegroundColor Cyan
Write-Host "================================================================" -ForegroundColor Cyan

$filesFixed = 0
$totalReplacements = 0

# Trouver tous les fichiers HTML de composants
$htmlFiles = Get-ChildItem -Path "src" -Filter "*.component.html" -Recurse

Write-Host "Trouvé $($htmlFiles.Count) fichiers de template à vérifier..." -ForegroundColor Blue
Write-Host ""

foreach ($file in $htmlFiles) {
    $filePath = $file.FullName
    $relativePath = $file.FullName.Replace((Get-Location).Path, "").TrimStart('\')
    
    if (Test-Path $filePath) {
        $content = Get-Content $filePath -Raw
        $originalContent = $content
        
        # Corriger outlined="true" vers [outlined]="true"
        $content = $content -replace 'outlined="true"', '[outlined]="true"'
        
        # Corriger outlined="false" vers [outlined]="false"  
        $content = $content -replace 'outlined="false"', '[outlined]="false"'
        
        # Corriger les autres propriétés booléennes communes
        $content = $content -replace 'disabled="true"', '[disabled]="true"'
        $content = $content -replace 'disabled="false"', '[disabled]="false"'
        $content = $content -replace 'loading="true"', '[loading]="true"'
        $content = $content -replace 'loading="false"', '[loading]="false"'
        $content = $content -replace 'raised="true"', '[raised]="true"'
        $content = $content -replace 'raised="false"', '[raised]="false"'
        $content = $content -replace 'rounded="true"', '[rounded]="true"'
        $content = $content -replace 'rounded="false"', '[rounded]="false"'
        $content = $content -replace 'text="true"', '[text]="true"'
        $content = $content -replace 'text="false"', '[text]="false"'
        
        # Vérifier si des changements ont été effectués
        if ($content -ne $originalContent) {
            Set-Content $filePath $content -NoNewline
            $filesFixed++
            
            # Compter le nombre de remplacements
            $replacements = ($originalContent.Length - $content.Length) / 2  # Approximation
            $totalReplacements += [Math]::Max(1, $replacements)
            
            Write-Host "  ✅ Corrigé: $relativePath" -ForegroundColor Green
        }
    }
}

Write-Host ""
Write-Host "📊 RÉSUMÉ DES CORRECTIONS:" -ForegroundColor Yellow
Write-Host "  - Fichiers corrigés: $filesFixed" -ForegroundColor White
Write-Host "  - Corrections appliquées: $totalReplacements" -ForegroundColor White
Write-Host ""

if ($filesFixed -gt 0) {
    Write-Host "✅ Toutes les erreurs outlined='true' ont été corrigées!" -ForegroundColor Green
    Write-Host ""
    Write-Host "🚀 Vous pouvez maintenant relancer l'application:" -ForegroundColor Cyan
    Write-Host "   npm start" -ForegroundColor White
    Write-Host ""
    Write-Host "🔍 Pour vérifier qu'il n'y a plus d'erreurs TypeScript:" -ForegroundColor Cyan
    Write-Host "   npx tsc --noEmit" -ForegroundColor White
} else {
    Write-Host "ℹ️  Aucune correction nécessaire trouvée." -ForegroundColor Blue
}

Write-Host ""
Write-Host "📋 PROCHAINES ÉTAPES:" -ForegroundColor Magenta
Write-Host "  1. Vérifier la compilation: npx tsc --noEmit" -ForegroundColor White
Write-Host "  2. Démarrer l'application: npm start" -ForegroundColor White
Write-Host "  3. Tester l'interface utilisateur" -ForegroundColor White
Write-Host "  4. Valider les fonctionnalités" -ForegroundColor White
