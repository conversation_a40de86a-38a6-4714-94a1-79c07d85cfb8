
import { Component, EventEmitter, Output } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { RippleModule } from 'primeng/ripple';
import { ToggleButtonModule } from 'primeng/togglebutton';
@Component({
  selector: 'app-terms-and-conditions-dialog',
  templateUrl: './terms-and-conditions.component.html',
  styleUrls: ['/terms-and-conditions.component.scss'],
  providers: [ButtonModule,
    RippleModule,
    ButtonModule,
    ToggleButtonModule,],
})
export class TermsAndConditionsDialogComponent {
    @Output() acceptTerms = new EventEmitter<boolean>();

    // Call this method when the user accepts terms
    onAcceptTerms(): void {
      this.acceptTerms.emit(true);
    }

    // Call this method when the user refuses terms
    onRefuseTerms(): void {
      this.acceptTerms.emit(false);
    }
  }
