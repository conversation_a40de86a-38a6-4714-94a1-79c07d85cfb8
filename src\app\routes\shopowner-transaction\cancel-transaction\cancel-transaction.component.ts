import { Component, OnInit } from '@angular/core';
import { MessageService, ConfirmationService } from 'primeng/api';
import { Table } from 'primeng/table';
import { RefundService } from 'src/app/services/refund.service';
import { RefundStatus, RefundStatusString } from 'src/app/shared/enums/RefundStatus';
import { RefundDTO } from 'src/app/shared/models/refund/refund';



@Component({
    selector: 'app-cancel-transaction',
    templateUrl: './cancel-transaction.component.html',
    styleUrls: ['./cancel-transaction.component.scss']
})
export class CancelTransactionComponent implements OnInit {

    refundDialog: boolean = false;

    deleterefundDialog: boolean = false;
    acceptrefundDialog: boolean = false;
    loading: boolean = true;
    refunds: RefundDTO[] = [];
    refundId!: string;

    selectedrefunds: RefundDTO[] = [];

    submitted: boolean = false;


    constructor(private refundService: RefundService, private messageService: MessageService, private confirmationService: ConfirmationService) { }

    ngOnInit() {
        this.loadAllRefundDemands();
    }



    loadAllRefundDemands() {
        this.refundService.GetAllRefundDemands().subscribe(response => {
            if (response.objectValue) {
                this.refunds = response.objectValue;

            }
            console.log('datarefunds', this.refunds);
            this.loading = false
        });
    }

    acceptrefund(refundId: string) {
        this.refundId = refundId;
        this.acceptrefundDialog = true;
    }

    deleterefund(refundId: string) {

        this.refundId = refundId;
        this.deleterefundDialog = true;
    }


    confirmDelete() {
        this.submitted = true;
        this.refundService.RejectCancledTransaction(this.refundId).subscribe(() => {
            // Reload data after successful delete

            this.deleterefundDialog = false;
            this.loadAllRefundDemands();
            this.submitted = false;
            this.messageService.add({ severity: 'success', summary: 'Successful', detail: 'refund Refused', life: 3000 });

        },
            (error) => {
                console.error('Error deleting refund:', error);
                this.submitted = false;
                this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Failed to refuse refund', life: 3000 });
            });
    }
    confirmAccept() {
        this.submitted = true;
        this.acceptrefundDialog = false;
        this.refundService.ValidateCancledTransaction(this.refundId).subscribe(() => {
            this.loadAllRefundDemands();
            this.submitted = false;
            this.messageService.add({ severity: 'success', summary: 'Successful', detail: 'Demmande accept', life: 3000 });

        },
            (error) => {
                console.error('Error accepting refund:', error);
                this.submitted = false;
                this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Failed to delete accept', life: 3000 });
            }
        );


    }

    hideDialog() {
        this.refundDialog = false;
        this.submitted = false;
    }

    onGlobalFilter(table: Table, event: Event) {
        table.filterGlobal((event.target as HTMLInputElement).value, 'contains');
    }

    getStatusString(statusValue: number): string {
        return RefundStatusString[statusValue];
    }

}
