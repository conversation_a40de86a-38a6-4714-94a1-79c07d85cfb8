import { AfterViewInit, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { GroupTicket } from '../../models/Group/groupTicket';
import { Group } from '../../models/Group/group';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Ticket } from '../../models/ticket/ticket';
@Component({
    selector: 'app-send-confirmation',
    templateUrl: './send-confirmation.component.html',
    styleUrls: ['./send-confirmation.component.scss']
})

export class SendConfirmationComponent implements OnInit {
    @Input() display: boolean = false;
    @Input() selectedGroup: Group | null = null;
    @Output() confirm: EventEmitter<Ticket[]> = new EventEmitter<Ticket[]>();
    @Output() cancelSend = new EventEmitter<void>();
    ticketFormGroup: FormGroup;

    constructor(private formBuilder: FormBuilder
    ) {
        this.ticketFormGroup = this.formBuilder.group({
            tickets: [[], Validators.required]
        });

    }

    ngOnInit() {
        console.log("group in pop up", this.selectedGroup)
    }
    confirmSend(tickets: Ticket[]) {
        console.log("selcted tickets", tickets)
        this.confirm.emit(tickets);
        this.ticketFormGroup.reset();
    }
    onCancel() {
        this.ticketFormGroup.reset();
        this.cancelSend.emit();
    }
    onTicketValueChange(event: any) {
        console.log("ser", this.ticketFormGroup.get('tickets')?.value)
        console.log("event,", event)
    }
}
