import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';

@NgModule({
    imports: [RouterModule.forChild([
        { path: 'auth/login/verif', loadChildren: () => import('./access-management/authentification/otp-code/otp-code.module').then(m => m.OtpCodeModule) },
        { path: 'auth/forgetpassword', loadChildren: () => import('./access-management/authentification/forget-password/forget-password.module').then(m => m.ForgetPasswordModule) },
        { path: 'auth/resetpassword', loadChildren: () => import('./access-management/authentification/reset-password/reset-password.module').then(m => m.ResetPasswordModule) },
        { path: 'auth/login', loadChildren: () => import('./access-management/authentification/login/login.module').then(m => m.LoginModule) },
        { path: 'auth/MailConfirmed/:token', loadChildren: () => import('./access-management/authentification/confirm/mail-confirmed/mail-confirmed.module').then(m => (m.MailConfirmedModule)) }
    ])],
    exports: [RouterModule]
})
export class RoutesRoutingModule { }
