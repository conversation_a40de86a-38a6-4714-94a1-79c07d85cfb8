import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, ViewChild } from '@angular/core';
import { Observable, Subscription, catchError, map, of } from 'rxjs';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Status } from 'src/app/shared/enums/status';
import { HttpClient } from '@angular/common/http';
import { Group } from 'src/app/shared/models/Group/group';
import { GroupService } from 'src/app/services/group.service';
import { UserService } from 'src/app/services/User.service';
import { WalletService } from 'src/app/services/wallet.service';
import { WalletDTO } from 'src/app/shared/models/wallet/WalletDTO';
import { UserDTO } from 'src/app/shared/models/user/UserDTO';
import { CashierService } from 'src/app/services/cashier.service';
interface InventoryStatus {
    label: string;
    value: string;
}
export interface Product {
    id?: string;
    code?: string;
    name?: string;
    description?: string;
    price?: number;
    quantity?: number;
    inventoryStatus?: InventoryStatus;
    category?: string;
    image?: string;
    rating?: number;
}

interface expandedRows {
    [key: string]: boolean;
}

export interface Country {
    name?: string;
    code?: string;
}

export interface Representative {
    name?: string;
    image?: string;
}

export interface Customer {
    id?: number;
    name?: string;
    country?: Country;
    company?: string;
    date?: string;
    status?: string;
    activity?: number;
    representative?: Representative;
}
@Component({
    selector: 'app-admin-overview',
    templateUrl: './admin-overview.component.html',
    styleUrls: ['./admin-overview.component.scss']
})

export class AdminOverviewComponent implements OnInit, OnDestroy {

    knobValue: number = 90;

    selectedWeek: any;

    weeks: any[] = [];

    barData: any;

    barOptions: any;

    pieData: any;

    pieOptions: any;

    groups: Group[] = [];

    subscription: Subscription;

    cols: any[] = [];

    expandedRows: expandedRows = {};

    isExpanded: boolean = false;

    customers1: Customer[] = [];

    loading: boolean = true;

    representatives: Representative[] = [];

    statuses: any[] = [];
    wallet: WalletDTO = { id: '', privateKey: '', publicKey: '', walletType: 0, assignedToType: 0, assignedToId: '', balance: 0, status: 0 };
    activityValues: number[] = [0, 100];
    usertype?: string | null;
    users:UserDTO[] = [];
    loadingusers: boolean = true;
    
    pageSize: number = 5;
    pageNumber: number = 0;
    sortBy?: string | string[] | null | undefined;
    sortDirection: number = 1;
    first!: number;
    totalRecords: number = 1;

    @ViewChild('filter') filter!: ElementRef;

    constructor(private groupService: GroupService, private userService: UserService, private walletService: WalletService, private layoutService: LayoutService
        , private http: HttpClient, private cashierService : CashierService) {
        this.subscription = this.layoutService.configUpdate$.subscribe(config => {
            this.initCharts();
        });
        this.usertype = localStorage.getItem('UserType');
        console.log("called usertype=====",this.usertype);

    }

    ngOnInit(): void {
        this.usertype = localStorage.getItem('UserType');

        console.log("called usertype in ngoninit =====",this.usertype);
        this.weeks = [{
            label: 'Last Week',
            value: 0,
            data: [[65, 59, 80, 81, 56, 55, 40], [28, 48, 40, 19, 86, 27, 90]]
        },
        {
            label: 'This Week',
            value: 1,
            data: [[35, 19, 40, 61, 16, 55, 30], [48, 78, 10, 29, 76, 77, 10]]
        }];

        this.selectedWeek = this.weeks[0];
        this.initCharts();

        this.walletService.GetUserWallets().subscribe(response => {
            if (response.statusCode === 200) {
                this.wallet = response.objectValue[0] || this.wallet;
                // Call the function to periodically check for balance updates
                //  this.checkForBalanceUpdates();
            } else {
                console.error('Failed to fetch user wallets:', response.exceptionMessage);
            }
        });
        this.groupService.getAllGroups().subscribe(response => {
            if (response.objectValue) {
                console.log("reponse groups", response.objectValue);
                this.groups = response.objectValue;
            }
        },
            (error) => {
            }
        );
        // this.productService.getProductsSmall().then(data => this.products = data);
        // this.getCustomersLarge().then(customers => {
        //     this.customers1 = customers;
        //     this.loading = false;

        //     // @ts-ignore
        //     this.customers1.forEach(customer => customer.date = new Date(customer.date));
        // });
        this.cols = [
            { header: 'Name', field: 'name' },
            { header: 'Category', field: 'category' },
            { header: 'Price', field: 'price' },
            { header: 'Status', field: 'inventoryStatus' }
        ];
        // this.productService.getProductsWithOrdersSmall().then(data => this.products = data);
        this.representatives = [
            { name: 'Amy Elsner', image: 'amyelsner.png' },
            { name: 'Anna Fali', image: 'annafali.png' },
            { name: 'Asiya Javayant', image: 'asiyajavayant.png' },
            { name: 'Bernardo Dominic', image: 'bernardodominic.png' },
            { name: 'Elwin Sharvill', image: 'elwinsharvill.png' },
            { name: 'Ioni Bowcher', image: 'ionibowcher.png' },
            { name: 'Ivan Magalhaes', image: 'ivanmagalhaes.png' },
            { name: 'Onyama Limba', image: 'onyamalimba.png' },
            { name: 'Stephen Shaw', image: 'stephenshaw.png' },
            { name: 'XuXue Feng', image: 'xuxuefeng.png' }
        ];
        this.statuses = [
            { label: 'Unqualified', value: 'unqualified' },
            { label: 'Qualified', value: 'qualified' },
            { label: 'New', value: 'new' },
            { label: 'Negotiation', value: 'negotiation' },
            { label: 'Renewal', value: 'renewal' },
            { label: 'Proposal', value: 'proposal' }
        ];

        this.loadUsers();
    }

    async loadUsers() {
        this.pageNumber = (this.first / this.pageSize) + 1;
        const sortBy = this.sortBy as string;
        const sortDirection = this.sortDirection;
        if(this.usertype !== "ShopOwner") {
            this.userService.getAllUsers().subscribe(
                (response) => {
                  if (response.objectValue) {
                    this.users = response.objectValue;
                    this.loadingusers = false;
                  }
                },
                (error) => {
                }
              );
        }else {
            this.cashierService.getAllCashiers(this.pageSize, this.pageNumber, sortBy, this.sortDirection).subscribe(response => 
                {
                    if (response.body && response.body.objectValue) {
                        this.users = response.body.objectValue;
                        this.loadingusers = false;
                        const xPaginationHeader = response.headers.get('x-pagination');
                        if (xPaginationHeader) {
                            const xPagination = JSON.parse(xPaginationHeader);
                            this.totalRecords = xPagination.TotalCount;
                        } else {
                            console.error('x-pagination header not found in the response');
                        }
                      }
            })
        }
        
      }
    private checkForBalanceUpdates() {
        /* setInterval(()=>{
             this.walletService.GetUserWallets().subscribe(updatedata=>{
                 if(updatedata.statusCode==200){

                 if(this.wallet.balance!==updatedata.objectValue[0].balance){
                     this.wallet.balance=updatedata.objectValue[0].balance               }
             }
             });
         },5000);*/
    }



    initCharts() {
        const documentStyle = getComputedStyle(document.documentElement);
        const textColor = documentStyle.getPropertyValue('--text-color');
        const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');
        const surfaceBorder = documentStyle.getPropertyValue('--surface-border');

        this.barData = {
            labels: ['MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT', 'SUN'],
            datasets: [
                {
                    label: 'Revenue',
                    backgroundColor: documentStyle.getPropertyValue('--primary-500'),
                    barThickness: 12,
                    borderRadius: 12,
                    data: this.selectedWeek.data[0]
                },
                {
                    label: 'Profit',
                    backgroundColor: documentStyle.getPropertyValue('--primary-200'),
                    barThickness: 12,
                    borderRadius: 12,
                    data: this.selectedWeek.data[1]
                }
            ]
        };

        this.pieData = {
            labels: ['Electronics', 'Fashion', 'Household'],
            datasets: [
                {
                    data: [300, 50, 100],
                    backgroundColor: [
                        documentStyle.getPropertyValue('--primary-700'),
                        documentStyle.getPropertyValue('--primary-400'),
                        documentStyle.getPropertyValue('--primary-100')
                    ],
                    hoverBackgroundColor: [
                        documentStyle.getPropertyValue('--primary-600'),
                        documentStyle.getPropertyValue('--primary-300'),
                        documentStyle.getPropertyValue('--primary-200')
                    ]
                }
            ]
        };

        this.barOptions = {
            animation: {
                duration: 0
            },
            plugins: {
                legend: {
                    labels: {
                        color: textColor,
                        usePointStyle: true,
                        font: {
                            weight: 700,
                        },
                        padding: 28
                    },
                    position: 'bottom'
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: textColorSecondary,
                        font: {
                            weight: 500
                        }
                    },
                    grid: {
                        display: false,
                        drawBorder: false
                    }
                },
                y: {
                    ticks: {
                        color: textColorSecondary
                    },
                    grid: {
                        color: surfaceBorder,
                        drawBorder: false
                    }
                }
            }
        };

        this.pieOptions = {
            animation: {
                duration: 0
            },
            plugins: {
                legend: {
                    labels: {
                        color: textColor,
                        usePointStyle: true,
                        font: {
                            weight: 700,
                        },
                        padding: 28
                    },
                    position: 'bottom'
                }
            }
        };
    }

    onWeekChange() {
        let newBarData = { ...this.barData };
        newBarData.datasets[0].data = this.selectedWeek.data[0];
        newBarData.datasets[1].data = this.selectedWeek.data[1];
        this.barData = newBarData;
    }

    onGlobalFilter(table: Table, event: Event) {
        table.filterGlobal((event.target as HTMLInputElement).value, 'contains');
    }

    expandAll() {
        if (!this.isExpanded) {
            this.groups.forEach(group => group && group.name ? this.expandedRows[group.name] = true : '');

        } else {
            this.expandedRows = {};
        }
        this.isExpanded = !this.isExpanded;
    }

    ngOnDestroy(): void {
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }

    getStatusString(statusValue: number): string {
        return Status[statusValue];
    }

    // private userNameCache = new Map<string, Observable<string>>();
    // getUserNameById(id: string): Observable<string> {
    //     // Check if the user name is already in the cache
    //     const cachedUserName = this.userNameCache.get(id);

    //     if (cachedUserName) {
    //       // Return the cached user name
    //       return cachedUserName;
    //     } else {
    //       // Fetch the user name from the API
    //       const apiCall = this.userService.getUserById(id).pipe(
    //         map(data => {
    //           if (data.statusCode === 200) {
    //             const userName = data.objectValue.fullName;
    //             // Cache the user name for future use
    //             this.userNameCache.set(id, of(userName));
    //             return userName;
    //           } else {
    //             console.log(data.exceptionMessage);
    //             return 'Error fetching name';
    //           }
    //         }),
    //         catchError(error => {
    //           console.error('Error fetching user data', error);
    //           return of('Error fetching name');
    //         })
    //       );

    //       // Cache the observable for subsequent calls
    //       this.userNameCache.set(id, apiCall);

    //       return apiCall;
    //     }
    //   }

    clear(table: Table) {
        table.clear();
        this.filter.nativeElement.value = '';
    }

    // getCustomersLarge() {
    //     return this.http.get<any>('assets/demo/data/customers-large.json')
    //         .toPromise()
    //         .then(res => res.data as Customer[])
    //         .then(data => data);
    // }

    lazyLoadCashiers(event: TableLazyLoadEvent) {
        this.first = event.first || 0;
        this.sortBy = event.sortField ?? '';
        this.sortDirection = event.sortOrder as number;
        this.loadUsers();
      }

}
