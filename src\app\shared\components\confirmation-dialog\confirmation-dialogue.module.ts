import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DialogModule } from 'primeng/dialog'; // Import DialogModule from PrimeNG
import { ButtonModule } from 'primeng/button';
import { CancelConfirmationComponent } from './cancel-confirmation/cancel-confirmation.component';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { FormsModule } from '@angular/forms';
import { AddConfirmationComponent } from './add-confirmation/add-confirmation.component';
import { SendConfirmationComponent } from '../send-confirmation/send-confirmation.component';
import { MultiSelectModule } from "primeng/multiselect";
import { MessageModule } from 'primeng/message';
import { ReactiveFormsModule } from '@angular/forms';
import { SendTicketToEmployeeConfirmationComponent } from './send-ticket-to-employee-confirmation/send-ticket-to-employee-confirmation.component';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
@NgModule({
    declarations: [
        CancelConfirmationComponent, 
        AddConfirmationComponent, 
        SendConfirmationComponent, 
        SendTicketToEmployeeConfirmationComponent,
        
    ],
    imports: [
        CommonModule,
        DialogModule,
        ButtonModule,
        MultiSelectModule,
        InputTextareaModule,
        FormsModule,
        MessageModule,
        ReactiveFormsModule,
        ProgressSpinnerModule

    ],
    exports: [
        CancelConfirmationComponent, 
        AddConfirmationComponent, 
        SendConfirmationComponent, 
        SendTicketToEmployeeConfirmationComponent, 
        
        
    ]
})
export class ConfirmationDialogueModule { }
