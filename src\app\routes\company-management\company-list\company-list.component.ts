import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { CompanyService } from '../../../services/company.service'; // Adjust the path
import { Company } from '../../../shared/models/company/company';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { ConfirmationService, FilterMetadata, SelectItem } from 'primeng/api';
import { Status } from 'src/app/shared/enums/status';
import { EntrepriseType } from 'src/app/shared/enums/entreprise-type';
import { ServiceType } from 'src/app/shared/enums/service-type';
import { MessageService } from 'primeng/api';
import { CategoryType } from 'src/app/shared/enums/categoryType';

interface expandedRows {
    [key: string]: boolean;
}


@Component({
    selector: 'app-company-list',
    templateUrl: './company-list.component.html',
    styleUrls: ['./company-list.component.scss'],
    providers: [ConfirmationService],
})

export class CompanyListComponent {
    companies: Company[] = [];
    Status = Status;
    displayAddEditDialog: boolean = false;
    displayDeleteDialog: boolean = false;
    displayReactivateDialog: boolean = false;
    selectedCompanyId: string = "";
    rowGroupMetadata: any;
    selectedCompany: Company | null = null;
    expandedRows: expandedRows = {};
    activityValues: number[] = [0, 100];
    isExpanded: boolean = false;
    idFrozen: boolean = false;
    loading: boolean = true;
    StatusList = this.enumToArray(Status).map(item => ({ label: item.label, value: item.label }));
    pageSize: number = 5;
    pageNumber: number = 0;
    first!: number;
    totalRecords: number = 1;
    sortBy?: string | string[] | null | undefined;
    sortDirection: number = 1;
    @ViewChild('filter') filter!: ElementRef;

    constructor(private companyService: CompanyService,
        private confirmationService: ConfirmationService,
        private messageService: MessageService,) { }

    enumToArray(enumerable: any): SelectItem[] {
        return Object.keys(enumerable)
            .filter(key => !isNaN(Number(enumerable[key])))
            .map(key => ({ label: key, value: enumerable[key] }));
    }
    getStatusString(statusValue: number): string {
        return Status[statusValue];
    }
    getEntrepriseTypeString(entrepriseValue: number): string {
        return EntrepriseType[entrepriseValue];
    }
    getServiceTypeString(serviceValue: number): string {
        return ServiceType[serviceValue];
    }
    getCategoryTypeString(categoryValue: number): string {
        return CategoryType[categoryValue];
    }

    lazyLoadCompanies(event: TableLazyLoadEvent) {
        this.first = event.first || 0;
        this.sortBy = event.sortField ?? '';
        this.sortDirection = event.sortOrder as number;
        this.loadCompanies(event.filters as { [s: string]: FilterMetadata } | undefined);

    }
    loadCompanies(filters?: { [s: string]: FilterMetadata } | undefined) {
        this.pageNumber = (this.first / this.pageSize) + 1
        const sortBy = this.sortBy as string;
        const sortDirection = this.sortDirection;
        filters = filters || {};
        this.companyService.getAllPaginationCompanies(this.pageSize, this.pageNumber ,sortBy, sortDirection, filters).subscribe(
            (response) => {
                if (response.body && response.body.objectValue) {
                    this.companies = response.body.objectValue;
                    console.log("TotalCount",response);
                    const xPaginationHeader = response.headers.get('x-pagination');
                    if (xPaginationHeader) {
                        const xPagination = JSON.parse(xPaginationHeader);


                        this.totalRecords = xPagination.TotalCount;
                    } else {
                        console.error('x-pagination header not found in the response');
                    }
                    this.loading = false;
                }
            },
            (error) => {
                // Handle error
                this.loading = false;
            }
        );
    }

    onSort() {
        //this.updateRowGroupMetaData();
    }

    expandAll() {
        if (!this.isExpanded) {
            // this.products.forEach(product => product && product.name ? this.expandedRows[product.name] = true : '');

        } else {
            this.expandedRows = {};
        }
        this.isExpanded = !this.isExpanded;
    }

    formatCurrency(value: number) {
        //   return value.toLocaleString('en-US', { style: 'currency', currency: 'USD' });
    }

    onGlobalFilter(table: Table, event: Event) {
        table.filterGlobal((event.target as HTMLInputElement).value, 'contains');
    }

    clear(table: Table) {
        table.clear();
        this.filter.nativeElement.value = '';
    }
    showDeleteConfirmation(company: Company) {
        if (company.id !== undefined) {
            this.selectedCompanyId = company.id;
            this.displayDeleteDialog = true;
        }
        else {
            console.log('nope');

        }
    }
    confirmDelete() {
        debugger
        if (this.selectedCompanyId !== null) {
            this.companyService.deleteCompany(this.selectedCompanyId).subscribe(
                (response) => {

                    if (response.statusCode == 200) {
                        this.displayDeleteDialog = false;
                        this.messageService.add({ key: 'toast', severity: 'success', summary: 'Company deleted successfully', detail: response.exceptionMessage });
                        // this.companies = this.companies.filter(company => company.id !== this.selectedCompanyId);
                        this.loadCompanies();
                    }
                    else {
                        this.messageService.add({ key: 'toast', severity: 'success', summary: 'Error Delete Company', detail: response.exceptionMessage });

                    }
                },
                (error) => {
                    this.displayDeleteDialog = false;

                }
            );
        }
    }


    showReactivateConfirmation(company: Company) {
        if (company.id !== undefined) {
            this.selectedCompanyId = company.id;
            this.displayReactivateDialog = true;
        }
        else {
            console.log('nope');

        }
    }

    confirmReactivate() {
        if (this.selectedCompanyId !== null) {
            this.companyService.reactivateCompany(this.selectedCompanyId).subscribe(
                (response) => {

                    if (response.statusCode == 200) {
                        this.displayReactivateDialog = false;
                        this.messageService.add({ key: 'toast', severity: 'success', summary: 'Company Reactivated successfully', detail: response.exceptionMessage });
                        this.loadCompanies();
                    }
                    else {
                        this.messageService.add({ key: 'toast', severity: 'success', summary: 'Error Reactivating Company', detail: response.exceptionMessage });

                    }
                },
                (error) => {
                    this.displayReactivateDialog = false;

                }
            );
        }
    }

    onCancelReactivate() {
        this.displayReactivateDialog = false;
    }

    displayCompanyDialog(company: Company | null) {
        this.selectedCompany = company;
        this.displayAddEditDialog = true;
        return this.displayAddEditDialog;
    }
    closeAddEditDialogEvent(e: Event) {
        this.displayAddEditDialog = false;
    }
    onCompanyAdded() {
        this.loadCompanies();
    }
    onElementDeleted() {
        this.loadCompanies();
    }
    onCancelDelete() {
        this.displayDeleteDialog = false;
    }
    onPageChange(event: any): void {
        this.pageNumber = event.page + 1;
        this.loadCompanies();
    }


}
