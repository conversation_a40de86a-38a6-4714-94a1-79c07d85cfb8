<div class="grid">
    <div class="col-12">
        <div class="card">
            <p-table #dt1 [value]="tableData" dataKey="id" [resizableColumns]="true" [rows]="pageSize" [loading]="loading"
                [rowHover]="true" styleClass="p-datatable-gridlines" [paginator]="true"
                [totalRecords]="totalRecords" [first]="first" [lazy]="true" (onLazyLoad)="lazyLoadInvoice($event)"
                [globalFilterFields]="['code','dynoAmount','currency','netAmount','vatAmount','totalAmount','paymentMethod','date', 'status','company','document']"
                responsiveLayout="scroll">
                <ng-template pTemplate="caption">
                    <div>
                        <p-button  label="Clear" icon="pi pi-filter-slash" [outlined]="true"
                            [pTooltip]="'Clear Filters'"
                            (click)="clear(dt1)"  [style]="{'margin-right.px': '10'}"
                            styleClass="p-button-outlined "><span class="tooltip"></span></p-button>

                    </div>
                </ng-template>

                <ng-template pTemplate="header">
                    <tr>
                        <th style="min-width: 10rem" pSortableColumn="code">
                            <div class="flex justify-content-between align-items-center">
                                Id <!--<p-sortIcon field="code"></p-sortIcon>
                                <p-columnFilter type="text" field="code" display="menu"
                                    placeholder="Search by id"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 12rem" pSortableColumn="companyName">
                            <div class="flex justify-content-between align-items-center">
                                company Name <!--<p-sortIcon field="companyName"></p-sortIcon>
                                <p-columnFilter type="text" field="Company.Name" display="menu"
                                    placeholder="Search by name"></p-columnFilter>-->
                            </div>
                        </th>

                        <th style="min-width: 10rem" pSortableColumn="dynoAmount">
                            <div class="flex justify-content-between align-items-center">
                                Dyno Amount <!--<p-sortIcon field="dynoAmount"></p-sortIcon>
                                <p-columnFilter type="numeric" field="dynoAmount" display="menu"
                                    placeholder="Search by amount"></p-columnFilter> -->
                            </div>
                        </th>

                        <th style="min-width: 10rem" pSortableColumn="netAmount">
                            <div class="flex justify-content-between align-items-center">
                                Net Amount <!--<p-sortIcon field="netAmount"></p-sortIcon>
                                <p-columnFilter type="numeric" field="netAmount" display="menu"
                                    placeholder="Search by net Amount"></p-columnFilter> -->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="vatAmount">
                            <div class="flex justify-content-between align-items-center">
                                VAT Amount <!--<p-sortIcon field="vatAmount"></p-sortIcon>
                                <p-columnFilter type="numeric" field="vatAmount" display="menu"
                                    placeholder="Search by vat Amount"></p-columnFilter> -->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="totalAmount">
                            <div class="flex justify-content-between align-items-center">
                                Total Amount <!--<p-sortIcon field="totalAmount"></p-sortIcon>
                                <p-columnFilter type="numeric" field="totalAmount" display="menu"
                                    placeholder="Search by total Amount" currency="TND"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="paymentMethod">
                            <div class="flex justify-content-between align-items-center">
                                Payment Method <!--<p-sortIcon field="PaymentMethod"></p-sortIcon>
                                <p-columnFilter field="PaymentMethod" matchMode="equals" display="menu">
                                    <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                        <p-dropdown [ngModel]="value" [options]="PaymentMethodList"
                                            (onChange)="filter($event.value)" placeholder="Search by payment method"
                                            [style]="{'min-width': '12rem'}">
                                            <ng-template let-option pTemplate="item">
                                                <span
                                                    [class]="'customer-badge status-' + option.value">{{option.label}}</span>
                                            </ng-template>
                                        </p-dropdown>
                                    </ng-template>
                                </p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="date">
                            <div class="flex justify-content-between align-items-center">
                                Date <!--<p-sortIcon field="date"></p-sortIcon>
                                <p-columnFilter type="date" field="date" display="menu"
                                    placeholder="mm/dd/yyyy"></p-columnFilter>-->
                            </div>
                        </th>

                        <th style="min-width: 10rem" pSortableColumn="status">
                            <div class="flex justify-content-between align-items-center">
                                Status <!--<p-sortIcon field="Status"></p-sortIcon>
                                <p-columnFilter field="status" matchMode="equals" display="menu">
                                    <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                        <p-dropdown [ngModel]="value" [options]="invoiceStatusList"
                                            (onChange)="filter($event.value)" placeholder="Any"
                                            [style]="{'min-width': '12rem'}">
                                            <ng-template let-option pTemplate="item">
                                                <span
                                                    [class]="'customer-badge status-' + option.value">{{option.label}}</span>
                                            </ng-template>
                                        </p-dropdown>
                                    </ng-template>
                                </p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="document">
                            <div class="flex justify-content-between align-items-center">
                                Document <!--<p-sortIcon field="document"></p-sortIcon>
                                <p-columnFilter type="text" field="document" display="menu"
                                    placeholder="Search by document"></p-columnFilter>-->
                            </div>
                        </th>


                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-invoice>
                    <tr>
                        <td>
                            {{invoice.code}}
                        </td>
                        <td>
                            {{invoice.company.name}}
                        </td>

                        <td class="text-right">
                            {{invoice.dynoAmount| number:'1.3-3'}}
                        </td>
                        <td class="text-right">
                            <div style="display: inline-block; padding-right: 5px;">
                                {{ 'TND' }}
                            </div>
                            <div style="display: inline-block;">
                                {{invoice.netAmount| number:'1.3-3'}}
                            </div>
                        </td>
                        <td class="text-right">
                            <div style="display: inline-block; padding-right: 5px;">
                                {{ 'TND' }}
                            </div>
                            <div style="display: inline-block;">
                                {{invoice.vatAmount| number:'1.3-3'}}
                            </div>
                        </td>
                        <td class="text-right">
                            <div style="display: inline-block; padding-right: 5px;">
                                {{ 'TND' }}
                            </div>
                            <div style="display: inline-block;">
                                {{ invoice.totalAmount | number:'1.3-3'}}
                            </div>
                        </td>
                        <td>
                            <span>{{ getPaymentMethodString(invoice.paymentMethod) }}</span>
                        </td>
                        <td>
                            <span>{{ invoice.date | dateTimeToDate}}</span>
                        </td>

                        <td>
                            <span [class]="'component-badge status-' + getStatusString(invoice.status)">
                                {{ getStatusString(invoice.status) }}
                            </span>
                        </td>
                        <td>
                            <a [href]="S3URl+invoice.document?.path" target="_blank"><i class="pi pi-file-pdf" style="color: red; font-size: 2rem;display: flex;
                                justify-content: space-around;"></i></a>
                        </td>
                    </tr>
                </ng-template>

                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="8">No invoices found.</td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="loadingbody">
                    <tr>
                        <td colspan="8">Loading invoices data. Please wait.</td>
                    </tr>
                </ng-template>
            </p-table>

        </div>
    </div>
    <p-toast key="toast"></p-toast>
</div>
