import { Injectable } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { ToastService } from './ToastService.service';
import { Router } from '@angular/router';

export interface ErrorInfo {
  message: string;
  code?: string;
  details?: any;
  timestamp: Date;
}

@Injectable({
  providedIn: 'root'
})
export class ErrorHandlerService {
  
  constructor(
    private toastService: ToastService,
    private router: Router
  ) { }

  /**
   * Gère les erreurs HTTP de manière centralisée
   */
  handleHttpError(error: HttpErrorResponse): Observable<never> {
    const errorInfo = this.processHttpError(error);
    this.logError(errorInfo);
    this.showUserFriendlyMessage(errorInfo);
    
    return throwError(() => errorInfo);
  }

  /**
   * Gère les erreurs génériques de l'application
   */
  handleApplicationError(error: any, context?: string): void {
    const errorInfo: ErrorInfo = {
      message: error.message || 'Une erreur inattendue s\'est produite',
      details: error,
      timestamp: new Date()
    };

    if (context) {
      errorInfo.details = { ...errorInfo.details, context };
    }

    this.logError(errorInfo);
    this.showUserFriendlyMessage(errorInfo);
  }

  /**
   * Traite les erreurs HTTP et les convertit en ErrorInfo
   */
  private processHttpError(error: HttpErrorResponse): ErrorInfo {
    let message = 'Une erreur s\'est produite';
    let code = error.status?.toString();

    switch (error.status) {
      case 0:
        message = 'Impossible de se connecter au serveur. Vérifiez votre connexion internet.';
        break;
      case 400:
        message = this.extractServerMessage(error) || 'Requête invalide';
        break;
      case 401:
        message = 'Session expirée. Veuillez vous reconnecter.';
        this.handleUnauthorized();
        break;
      case 403:
        message = 'Accès refusé. Vous n\'avez pas les permissions nécessaires.';
        break;
      case 404:
        message = 'Ressource non trouvée';
        break;
      case 409:
        message = this.extractServerMessage(error) || 'Conflit de données';
        break;
      case 422:
        message = this.extractValidationErrors(error) || 'Données invalides';
        break;
      case 500:
        message = 'Erreur interne du serveur. Veuillez réessayer plus tard.';
        break;
      case 502:
      case 503:
      case 504:
        message = 'Service temporairement indisponible. Veuillez réessayer plus tard.';
        break;
      default:
        message = `Erreur ${error.status}: ${error.statusText || 'Erreur inconnue'}`;
    }

    return {
      message,
      code,
      details: error,
      timestamp: new Date()
    };
  }

  /**
   * Extrait le message d'erreur du serveur
   */
  private extractServerMessage(error: HttpErrorResponse): string | null {
    if (error.error) {
      // Format standard de l'API
      if (error.error.message) {
        return error.error.message;
      }
      
      // Format ResponseAPI
      if (error.error.errorMessage) {
        return error.error.errorMessage;
      }

      // Message simple
      if (typeof error.error === 'string') {
        return error.error;
      }
    }

    return null;
  }

  /**
   * Extrait et formate les erreurs de validation
   */
  private extractValidationErrors(error: HttpErrorResponse): string | null {
    if (error.error && error.error.errors) {
      const validationErrors = error.error.errors;
      const errorMessages: string[] = [];

      for (const field in validationErrors) {
        if (validationErrors.hasOwnProperty(field)) {
          const fieldErrors = validationErrors[field];
          if (Array.isArray(fieldErrors)) {
            errorMessages.push(...fieldErrors);
          } else {
            errorMessages.push(fieldErrors);
          }
        }
      }

      return errorMessages.length > 0 ? errorMessages.join(', ') : null;
    }

    return null;
  }

  /**
   * Affiche un message convivial à l'utilisateur
   */
  private showUserFriendlyMessage(errorInfo: ErrorInfo): void {
    const severity = this.getSeverityFromError(errorInfo);
    
    this.toastService.showToast(
      severity,
      'Erreur',
      errorInfo.message
    );
  }

  /**
   * Détermine la sévérité du message basée sur l'erreur
   */
  private getSeverityFromError(errorInfo: ErrorInfo): string {
    if (!errorInfo.code) return 'error';

    const statusCode = parseInt(errorInfo.code);
    
    if (statusCode >= 500) return 'error';
    if (statusCode >= 400) return 'warn';
    
    return 'info';
  }

  /**
   * Log l'erreur pour le debugging
   */
  private logError(errorInfo: ErrorInfo): void {
    console.group(`🚨 Error [${errorInfo.timestamp.toISOString()}]`);
    console.error('Message:', errorInfo.message);
    if (errorInfo.code) {
      console.error('Code:', errorInfo.code);
    }
    if (errorInfo.details) {
      console.error('Details:', errorInfo.details);
    }
    console.groupEnd();
  }

  /**
   * Gère les erreurs d'autorisation
   */
  private handleUnauthorized(): void {
    // Rediriger vers la page de connexion après un court délai
    setTimeout(() => {
      this.router.navigate(['/auth/login']);
    }, 2000);
  }

  /**
   * Méthode utilitaire pour créer des observables d'erreur formatés
   */
  createErrorObservable(message: string, code?: string): Observable<never> {
    const errorInfo: ErrorInfo = {
      message,
      code,
      timestamp: new Date()
    };

    this.logError(errorInfo);
    return throwError(() => errorInfo);
  }
}
