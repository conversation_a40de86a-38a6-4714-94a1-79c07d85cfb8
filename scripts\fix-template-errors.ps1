# Script PowerShell pour corriger automatiquement les erreurs de template Angular
# Corrige les propriétés booléennes mal typées dans les templates HTML

Write-Host "🔧 Correction automatique des erreurs de template Angular" -ForegroundColor Cyan
Write-Host "=========================================================" -ForegroundColor Cyan

# Propriétés booléennes communes de PrimeNG qui sont souvent mal typées
$booleanProps = @(
    'outlined',
    'disabled',
    'loading',
    'raised',
    'rounded',
    'text',
    'plain',
    'severity',
    'autoResize',
    'showClear',
    'multiple',
    'checkmark',
    'showToggleAll',
    'metaKeySelection',
    'dataKey',
    'lazy',
    'paginator',
    'rowHover',
    'resizableColumns',
    'reorderableColumns',
    'scrollable',
    'virtualScroll',
    'sortMode',
    'selectionMode',
    'contextMenu',
    'draggableRows',
    'editMode',
    'responsive'
)

# Fonction pour corriger un fichier
function Fix-TemplateFile {
    param(
        [string]$FilePath
    )
    
    if (-not (Test-Path $FilePath)) {
        return
    }
    
    Write-Host "Vérification: $FilePath" -ForegroundColor Gray
    
    $content = Get-Content $FilePath -Raw
    $originalContent = $content
    $hasChanges = $false
    
    foreach ($prop in $booleanProps) {
        # Pattern pour trouver prop="true" ou prop="false"
        $pattern = "$prop=`"(true|false)`""
        $replacement = "[$prop]=`"`$1`""
        
        if ($content -match $pattern) {
            $content = $content -replace $pattern, $replacement
            $hasChanges = $true
            Write-Host "  ✅ Corrigé: $prop" -ForegroundColor Green
        }
    }
    
    # Corrections spécifiques pour d'autres patterns courants
    $specificPatterns = @{
        'styleClass="([^"]*)"' = '[styleClass]="''$1''"'
        'style="([^"]*)"' = '[style]="''$1''"'
    }
    
    foreach ($pattern in $specificPatterns.Keys) {
        $replacement = $specificPatterns[$pattern]
        if ($content -match $pattern) {
            # Ne pas remplacer si c'est déjà dans une liaison de propriété
            if ($content -notmatch "\[$($pattern.Split('=')[0])\]") {
                $content = $content -replace $pattern, $replacement
                $hasChanges = $true
                Write-Host "  ✅ Corrigé pattern: $($pattern.Split('=')[0])" -ForegroundColor Green
            }
        }
    }
    
    if ($hasChanges) {
        Set-Content $FilePath $content -NoNewline
        Write-Host "  📝 Fichier mis à jour: $FilePath" -ForegroundColor Yellow
    }
}

# Trouver tous les fichiers HTML de composants Angular
$htmlFiles = Get-ChildItem -Path "src" -Filter "*.component.html" -Recurse

Write-Host "Trouvé $($htmlFiles.Count) fichiers de template à vérifier..." -ForegroundColor Blue
Write-Host ""

foreach ($file in $htmlFiles) {
    Fix-TemplateFile -FilePath $file.FullName
}

Write-Host ""
Write-Host "✅ Correction terminée!" -ForegroundColor Green
Write-Host ""
Write-Host "🔍 Vérification des erreurs restantes..." -ForegroundColor Blue

# Rechercher d'autres patterns problématiques
$problematicPatterns = @(
    'outlined="true"',
    'outlined="false"',
    'disabled="true"',
    'disabled="false"',
    'loading="true"',
    'loading="false"'
)

$remainingIssues = @()

foreach ($file in $htmlFiles) {
    $content = Get-Content $file.FullName -Raw
    foreach ($pattern in $problematicPatterns) {
        if ($content -match [regex]::Escape($pattern)) {
            $remainingIssues += @{
                File = $file.FullName
                Pattern = $pattern
            }
        }
    }
}

if ($remainingIssues.Count -gt 0) {
    Write-Host "⚠️  Problèmes restants détectés:" -ForegroundColor Yellow
    foreach ($issue in $remainingIssues) {
        Write-Host "  - $($issue.File): $($issue.Pattern)" -ForegroundColor Red
    }
} else {
    Write-Host "✅ Aucun problème détecté!" -ForegroundColor Green
}

Write-Host ""
Write-Host "🚀 Vous pouvez maintenant relancer la compilation Docker" -ForegroundColor Cyan
