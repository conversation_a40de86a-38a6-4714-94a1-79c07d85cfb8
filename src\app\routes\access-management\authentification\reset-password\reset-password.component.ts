import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { ToastService } from 'src/app/services/ToastService.service';
import { AuthService } from 'src/app/services/auth.service';
import { ResetPasswordDTO } from 'src/app/shared/models/authentification/password/ResetPasswordDTO';
@Component({
    selector: 'app-reset-password',
    templateUrl: './reset-password.component.html',
    styleUrls: ['./reset-password.component.scss']
})
export class ResetPasswordComponent {
    newPassword: string = '';
    repeatPassword: string = '';
    disableSubmitButton:boolean=false;
    constructor(private layoutService: LayoutService, private route: ActivatedRoute, private authservice: AuthService,
        private route_nav: Router,
        private toastService: ToastService,
        private messageService: MessageService) { }

    get dark(): boolean {
        return this.layoutService.config.colorScheme !== 'light';
    }

    ngOnInit() {
        const code = this.route.snapshot.queryParams['code'];
        const email = this.route.snapshot.queryParams['email'];
        console.log("code",code,"email",email);

    }
    onSubmit() {
        if (this.newPassword === this.repeatPassword) {
            const code = this.route.snapshot.queryParams['code'];
            const email = this.route.snapshot.queryParams['email'];

            const resetToken = code; // Assuming 'code' is the resetToken

            const requestBody: ResetPasswordDTO = {
                email: email,
                newPassword: this.newPassword,
                resetToken: resetToken
            };

            this.authservice.ResetPassword(requestBody)
                .subscribe(Response => {
                    if (Response.statusCode == 200) {
                        this.disableSubmitButton = true;
                        // this.toastService.showToast('success', 'Success Message', 'Password reset successful');
                        this.messageService.add({ key: 'toast', severity: 'success', summary: 'Success Message', detail: 'Password reset successful' });
                        setTimeout(()=>{
                            this.route_nav.navigate(['auth/login']);
                        },4000);


                    } else {
                        this.disableSubmitButton = true;
                        this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Message', detail: Response.exceptionMessage });
                        setTimeout(()=>{
                            this.route_nav.navigate(['auth/forgetpassword']);
                        },4000);
                    }
                }, error => {
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Message', detail: error });

                });
        } else {
            this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Message', detail: 'Passwords do not match' });

        }
    }
}
