.btn-save-role {
    width: 50px !important;
    height: 50px !important;
}

.row {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;
    flex-direction: row;
}


.input-width {
    width: 100% !important;
}


.footer {
    padding-top: 15px !important;
}

::ng-deep {
    .p-dropdown {
        width: 100% !important;
    }

    .p-dialog .p-dialog-footer {
        padding-top: 15px !important;
    }

}

.country-code-with-flag {
    display: flex;
    align-items: center;
    margin-right: 15px;
    /* Adjust spacing as needed */
}

.flag-icon {
    height: 20px;
    margin-right: 15px;
}

.country-code {
    font-weight: bold;
    margin-right: 15px;
    /* Adjust spacing as needed */
}

.error-messages {
    position: absolute;
    margin-top: 46px;
    margin-bottom: 50px;
}
.country-code-dropdown {
    flex: 0 0 40%; /* Modifiez cette valeur selon vos besoins */
}
.country-code-dropdown .p-dropdown {
    width: 100%; /* Maximiser la largeur du dropdown */
}
:host ::ng-deep .p-autocomplete-input {
    width: 100%;
}

