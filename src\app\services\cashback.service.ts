import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ResponseAPI } from '../shared/models/ResponseAPI';
import { environment } from 'src/environments/environment';
import { Cashback } from '../shared/models/cashback/cashback';
import { CashbackStatus } from '../shared/enums/cashbackStatus';
import { FailedCashback } from '../shared/models/cashback/failedCashback';

@Injectable({
    providedIn: 'root'
})
export class CashbackService {
    constructor(private http: HttpClient) {

    }

    getCashbacks(pageSize: number) {
        const params = new HttpParams().set('PageSize', pageSize.toString());
        return this.http.get<ResponseAPI<Cashback[]>>(`${environment.API}/CashBack/GetAllPaged`, { params });
    }

    getAllCashbacksPerPeriod() {
        return this.http.get<ResponseAPI<number[]>>(`${environment.API}/Cashback/GetAllPerPeriod`);
    }

    getAllCashbacksByStatus(status: CashbackStatus) {
        return this.http.get<ResponseAPI<Cashback[]>>(`${environment.API}/Cashback/GetAllByStatus?cashbackStatus=${status}`);
    }
    getAllFailedCashbacks() {
        return this.http.get<ResponseAPI<FailedCashback[]>>(`${environment.API}/FailedCashback/GetAll`);
    }
    getCashbackById(id: string) {
        return this.http.get<ResponseAPI<Cashback>>(`${environment.API}/Cashback/Get/${id}`);
    }

    addCashback(cashback: Cashback) {
        return this.http.post<ResponseAPI<Cashback>>(`${environment.API}/Cashback/Create`, cashback);
    }
    validateCashback(cashback: Cashback) {
        return this.http.put<ResponseAPI<Cashback>>(`${environment.API}/Cashback/ValidateCashBackRequest?cashBackId=` + cashback.id, cashback);
    }

    updateCashback(cashback: Cashback) {
        return this.http.put<ResponseAPI<Cashback>>(`${environment.API}/Cashback/Update`, cashback);
    }

    deleteCashback(failedCashback: FailedCashback) {
        return this.http.post<ResponseAPI<FailedCashback>>(`${environment.API}/FailedCashback/Create`, failedCashback);
    }

}
