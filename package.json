{"name": "apollo-ng", "version": "16.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:prod": "ng build --configuration production", "build:staging": "ng build --configuration staging", "watch": "ng build --watch --configuration development", "test": "ng test", "test:ci": "ng test --watch=false --browsers=ChromeHeadless --code-coverage", "test:coverage": "ng test --watch=false --browsers=ChromeHeadless --code-coverage", "e2e": "ng e2e", "lint": "ng lint", "docker:dev": "docker-compose up angular-dev", "docker:prod": "docker-compose --profile production up angular-prod", "docker:test": "docker-compose --profile testing up angular-test", "docker:test-full": "docker-compose --profile testing up -d postgres-test redis mock-api && docker-compose --profile testing up angular-test", "docker:build": "docker build -t dyno-angular .", "docker:clean": "docker-compose down -v && docker system prune -f"}, "private": true, "dependencies": {"@angular/animations": "^17.3.3", "@angular/cdk": "^16.1.0", "@angular/common": "^17.3.3", "@angular/compiler": "^17.3.3", "@angular/core": "^17.3.3", "@angular/forms": "^17.3.3", "@angular/platform-browser": "^17.3.3", "@angular/platform-browser-dynamic": "^17.3.3", "@angular/router": "^17.3.3", "@fullcalendar/angular": "^6.0.3", "@fullcalendar/core": "^6.0.3", "@fullcalendar/daygrid": "^6.0.3", "@fullcalendar/interaction": "^6.0.3", "@fullcalendar/timegrid": "^6.0.3", "@microsoft/signalr": "^8.0.0", "chart.js": "^3.3.2", "jwt-decode": "^3.1.2", "libphonenumber-js": "^1.10.57", "ngx-bootstrap": "^12.0.0", "primeflex": "^3.3.1", "primeicons": "6.0.1", "primeng": "^16.0.2", "quill": "^1.3.7", "rxjs": "~7.8.0", "tslib": "^2.3.0", "uuid": "^9.0.1", "vite": "^5.1.5", "zone.js": "~0.14.4"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.3", "@angular/cli": "~17.3.3", "@angular/compiler-cli": "^17.3.3", "@types/jasmine": "~4.3.0", "@types/uuid": "^9.0.6", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.4.4"}}