import { RouterModule } from "@angular/router";
import { NgModule } from '@angular/core';
import { UserListComponent } from "./user-list/user-list.component";
import { RoleGuard } from "src/app/RoleGuard";
@NgModule({
    imports: [RouterModule.forChild([
        {path:'',component:UserListComponent,canActivate:[RoleGuard],data:{role:["company", "shopowner","superadmin"]as string[]}}
    ])],
    exports: [RouterModule]
  })
  export class UserRoutingModule { }
