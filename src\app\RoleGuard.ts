import { Injectable } from '@angular/core';
import { ActivatedRoute, ActivatedRouteSnapshot, CanActivate, Router } from '@angular/router';
import { LocalStoreService } from './services/local-store.service';

@Injectable({
    providedIn: 'root'
})
export class RoleGuard implements CanActivate {

    constructor(private router: Router, private route: ActivatedRoute, private localStorageService: LocalStoreService) { }

    canActivate(route: ActivatedRouteSnapshot): boolean {
            const userType = this.localStorageService.getData('UserType');
            if (userType) {
                const requiredRoles = route.data['role'] ;
                const userTypeLowercase = userType.toLowerCase();
                if (requiredRoles && requiredRoles.some((role: string) => role === userTypeLowercase)) {
                    return true;
                } else {
                    this.router.navigate(['/unauthorized']);
                    return false;
                }
            } else {
                this.router.navigate(['/auth/login']);
                return false;
            }
    }
}
