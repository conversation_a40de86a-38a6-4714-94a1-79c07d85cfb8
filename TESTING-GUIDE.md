# 🧪 Guide de Test - Application Angular Dyno

## 📋 Vue d'Ensemble

Ce guide détaille comment exécuter les tests manuels et automatisés pour l'application Angular Dyno en utilisant Docker pour un environnement isolé et reproductible.

## 🐳 Prérequis

### Logiciels Requis
- **Docker Desktop** (version 4.0+)
- **Docker Compose** (inclus avec Docker Desktop)
- **Git** pour cloner le repository
- **Navigateur web** moderne (Chrome, Firefox, Edge)

### Vérification de l'Installation
```bash
# Vérifier Docker
docker --version
docker-compose --version

# Vérifier que Docker est en cours d'exécution
docker info
```

## 🚀 Démarrage Rapide

### Option 1: Script de Nettoyage et Reconstruction (Recommandé en cas de problèmes)

#### Sur Windows (PowerShell):
```powershell
# Nettoyage complet et reconstruction
.\scripts\clean-rebuild.ps1

# Avec nettoyage forcé des images Docker
.\scripts\clean-rebuild.ps1 -Force

# Sans tests automatisés
.\scripts\clean-rebuild.ps1 -SkipTests
```

#### Sur Linux/Mac:
```bash
# Rendre le script exécutable
chmod +x scripts/clean-rebuild.sh

# Nettoyage et reconstruction
./scripts/clean-rebuild.sh

# Avec nettoyage forcé
./scripts/clean-rebuild.sh --force

# Sans tests
./scripts/clean-rebuild.sh --skip-tests
```

### Option 2: Script de Test Standard

#### Sur Linux/Mac:
```bash
# Rendre le script exécutable
chmod +x scripts/manual-test.sh

# Lancer les tests
./scripts/manual-test.sh
```

#### Sur Windows (PowerShell):
```powershell
# Exécuter le script PowerShell
.\scripts\manual-test.ps1

# Avec options
.\scripts\manual-test.ps1 -RunTests -SkipBuild
```

### Option 2: Commandes Manuelles

```bash
# 1. Construire les images
docker-compose build

# 2. Démarrer les services de test
docker-compose --profile testing up -d postgres-test redis mock-api

# 3. Démarrer l'application
docker-compose up -d angular-dev

# 4. Accéder à l'application
# http://localhost:4200
```

## 🧪 Types de Tests

### 1. Tests Manuels Interactifs

#### Phase 1 - Corrections Prioritaires
- **Authentification**
  - [ ] Connexion avec identifiants valides
  - [ ] Connexion avec identifiants invalides
  - [ ] Vérification des messages d'erreur
  - [ ] Test du refresh automatique du token
  - [ ] Déconnexion

- **Gestion d'Erreurs**
  - [ ] Erreurs réseau (débrancher internet temporairement)
  - [ ] Erreurs serveur (arrêter l'API mock)
  - [ ] Erreurs de validation de formulaire
  - [ ] Messages d'erreur conviviaux

#### Phase 2 - Optimisations
- **Pagination et Listes**
  - [ ] Navigation entre les pages
  - [ ] Changement de taille de page
  - [ ] Tri par colonnes
  - [ ] Filtres de recherche
  - [ ] Performance de chargement

- **Actions CRUD**
  - [ ] Création d'une nouvelle entreprise
  - [ ] Modification d'une entreprise existante
  - [ ] Suppression d'une entreprise
  - [ ] Validation des formulaires

#### Phase 3 - Fonctionnalités Avancées
- **Audit Logs**
  - [ ] Accès à la page d'audit (/dashboard/audit-logs)
  - [ ] Filtrage par utilisateur, action, date
  - [ ] Affichage des détails d'un log
  - [ ] Export des logs

- **Notifications**
  - [ ] Réception de notifications
  - [ ] Marquage comme lu
  - [ ] Préférences de notification

### 2. Tests Automatisés

#### Tests Unitaires
```bash
# Exécuter tous les tests unitaires
npm run test:ci

# Avec couverture de code
npm run test:coverage

# En mode Docker
docker-compose --profile testing run --rm angular-test npm run test:ci
```

#### Tests d'Intégration
```bash
# Tests end-to-end (si configurés)
npm run e2e

# En mode Docker
docker-compose --profile testing run --rm angular-test npm run e2e
```

## 🔧 Configuration des Tests

### Environnements Disponibles

#### Développement
- **URL**: http://localhost:4200
- **Hot Reload**: Activé
- **Source Maps**: Activés
- **Optimisations**: Désactivées

#### Production (Test)
- **URL**: http://localhost:8080
- **Optimisations**: Activées
- **Minification**: Activée
- **Service Worker**: Activé

#### Test Automatisé
- **Navigateur**: Chrome Headless
- **Couverture**: Activée
- **Rapports**: Générés dans `/coverage`

### Services Mock

#### API Mock (MockServer)
- **URL**: http://localhost:1080
- **Configuration**: `mock-api/config/mockserver.properties`
- **Expectations**: `mock-api/expectations/api-expectations.json`

#### Base de Données Test
- **Type**: PostgreSQL
- **Port**: 5433
- **Base**: dyno_test
- **Utilisateur**: dyno_user

#### Cache Redis
- **Port**: 6379
- **Utilisation**: Cache des sessions et données

## 📊 Monitoring et Debugging

### Logs des Services
```bash
# Logs de l'application Angular
docker-compose logs -f angular-dev

# Logs de l'API mock
docker-compose logs -f mock-api

# Logs de tous les services
docker-compose logs -f
```

### État des Services
```bash
# Voir l'état de tous les services
docker-compose ps

# Voir les ressources utilisées
docker stats
```

### Debugging
```bash
# Accéder au container Angular
docker-compose exec angular-dev sh

# Inspecter les variables d'environnement
docker-compose exec angular-dev env

# Vérifier les fichiers de configuration
docker-compose exec angular-dev cat /app/src/environments/environment.ts
```

## 🎯 Checklist de Test Complet

### Fonctionnalités de Base
- [ ] Application se charge sans erreur
- [ ] Navigation entre les pages fonctionne
- [ ] Responsive design sur mobile/tablet
- [ ] Performance acceptable (< 3s chargement initial)

### Authentification et Sécurité
- [ ] Connexion/déconnexion
- [ ] Gestion des sessions
- [ ] Refresh automatique des tokens
- [ ] Redirection après expiration

### Interface Utilisateur
- [ ] Cohérence visuelle
- [ ] Messages d'erreur clairs
- [ ] Feedback utilisateur (loading, success, error)
- [ ] Accessibilité de base

### Fonctionnalités Métier
- [ ] CRUD des entreprises
- [ ] Pagination et filtres
- [ ] Audit logs
- [ ] Notifications
- [ ] Gestion des rôles

### Performance
- [ ] Temps de chargement initial
- [ ] Réactivité de l'interface
- [ ] Gestion de la mémoire
- [ ] Optimisation des requêtes

## 🚨 Résolution de Problèmes

### Problèmes Courants

#### L'application ne démarre pas
```bash
# Vérifier les logs
docker-compose logs angular-dev

# Reconstruire l'image
docker-compose build --no-cache angular-dev

# Nettoyer et redémarrer
docker-compose down -v
docker-compose up angular-dev
```

#### Erreurs de connexion à l'API
```bash
# Vérifier que l'API mock fonctionne
curl http://localhost:1080/health

# Redémarrer l'API mock
docker-compose restart mock-api
```

#### Tests qui échouent
```bash
# Nettoyer le cache des tests
docker-compose run --rm angular-test npm run test:ci -- --clearCache

# Vérifier la configuration Karma
docker-compose exec angular-test cat /app/karma.conf.ci.js
```

### Nettoyage Complet
```bash
# Arrêter tous les services
docker-compose down -v

# Supprimer les images
docker-compose down --rmi all

# Nettoyer Docker complètement
docker system prune -a -f
```

## 📈 Rapports de Test

### Couverture de Code
- **Localisation**: `./coverage/apollo-ng/`
- **Format**: HTML, LCOV, Cobertura
- **Seuils**: 80% statements, 70% branches

### Rapports de Performance
- **Lighthouse**: Intégré dans les tests e2e
- **Bundle Analyzer**: `npm run analyze`
- **Memory Profiling**: Chrome DevTools

## 🎉 Validation Finale

Une fois tous les tests passés avec succès:

1. ✅ **Fonctionnalités de base** opérationnelles
2. ✅ **Performance** acceptable
3. ✅ **Sécurité** validée
4. ✅ **Interface utilisateur** cohérente
5. ✅ **Tests automatisés** passent
6. ✅ **Documentation** à jour

L'application est prête pour le déploiement en production ! 🚀
