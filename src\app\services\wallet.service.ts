import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { ResponseAPI } from "../shared/models/ResponseAPI";
import { WalletDTO } from "../shared/models/wallet/WalletDTO";
import { UserType } from "../shared/enums/UserType";
import { environment } from "src/environments/environment";
import { UserDTO } from "../shared/models/user/UserDTO";

@Injectable({
  providedIn: 'root'
})
export class WalletService {
  constructor(private http: HttpClient) {

  }
  GetUserWallets() {
    return this.http.get<ResponseAPI<WalletDTO[]>>(`${environment.API}/Wallet/GetUserWallets`);
  }
  GetTotalBalanceByUserType(userType: UserType) {
    return this.http.get<ResponseAPI<number>>(`${environment.API}/Wallet/GetTotalBalanceByUserType?userType=${userType}`
    );
  }
  GetCompaniesTotalBalancePreviousMonth() {
    return this.http.get<ResponseAPI<number>>(`${environment.API}/Transaction/GetCompanyReceivedTransactionsTotalAmountPerMonth`
    );
  }
  GetShopsTotalBalancePreviousMonth() {
    return this.http.get<ResponseAPI<number>>(`${environment.API}/Transaction/GetCompanySendedTransactionsTotalAmountPerMonth`
    );
  }

  GetUserDetailsByWallet(id: string) {
    return this.http.get<ResponseAPI<UserDTO[]>>(`${environment.API}/Wallet/GetUserDetailsByWallet/${id}`);
  }
}
