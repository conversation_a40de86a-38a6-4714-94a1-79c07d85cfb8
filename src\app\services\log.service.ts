import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { LogError } from "../shared/models/Log/LogError";
import { ResponseAPI } from "../shared/models/ResponseAPI";
import { environment } from "src/environments/environment";

@Injectable({
    providedIn: 'root'
})
export class LogErrorService {
    constructor(private http: HttpClient) {
    }

    getAll() {
        return this.http.get<ResponseAPI<LogError[]>>(`${environment.API}/LogError/GetAll`);
    }

    getAllPaged(pageNumber : number, pageSize : number) {
        return this.http.get<ResponseAPI<LogError[]>>(`${environment.API}/LogError/GetAllPaged?PageNumber=${pageNumber}&PageSize=${pageSize}`);
    }
}