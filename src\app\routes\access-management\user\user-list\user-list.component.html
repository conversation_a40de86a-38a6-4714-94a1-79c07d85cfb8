<div class="grid">
  <div class="col-12">
    <div class="card">
      <p-table #dt1 [value]="users" dataKey="id" [rows]="pageSize" [loading]="loading" [rowHover]="true"
        rowGroupMode="subheader" (onSort)="onSort()" responsiveLayout="scroll" styleClass="p-datatable-gridlines"
        [paginator]="true" [totalRecords]="totalRecords" [first]="first" [lazy]="true"
        (onLazyLoad)="lazyLoadUsers($event)" [globalFilterFields]="['name','email','gender','phonenumber']"
        responsiveLayout="scroll">
        <ng-template pTemplate="caption">
          <div>
            <p-button pRipple label="Clear" [outlined]="true" icon="pi pi-filter-slash" (click)="clear(dt1)"
              [pTooltip]="'Clear All Filters'" styleClass="p-button-outlined"
              [style]="{'margin-right.px': 10}"></p-button>
            <p-button pRipple label="Add" [outlined]="true" icon="pi pi-plus" (click)="displayUserDialog(null)"
              [pTooltip]="'Add New User'" styleClass="p-button-outlined" [style]="{'margin-right.px': 10}"></p-button>




            <app-user-add-edit [display]="displayAddEditDialog" [selectedUser]="selectedUser"
              (userAdded)="onUserAdded()" (closeAddDialogEvent)="closeAddEditDialogEvent($event)"></app-user-add-edit>
          </div>
        </ng-template>

        <ng-template pTemplate="header">
          <tr>
            <th style="min-width: 12rem" pSortableColumn="user.fullName">
              <div class="flex justify-content-between align-items-center">
                <span>Name</span>
                <div class="flex align-items-center">
                  <p-sortIcon field="FullName" pTooltip="Sort Data" pTooltipPosition="right"
                    pTooltipStyleClass="custom-tooltip"></p-sortIcon>
                  <p-columnFilter pTooltip="Filter Data" type="text" field="FullName" display="menu"
                    placeholder="Search by name"></p-columnFilter>
                </div>
              </div>
            </th>
            <th style="min-width: 12rem" pSortableColumn="email">
              <div class="flex justify-content-between align-items-center">
                Email
                <!--<p-sortIcon field="Email"></p-sortIcon>
                <p-columnFilter type="text" field="Email" display="menu" placeholder="Search by email"></p-columnFilter>-->
              </div>
            </th>
            <th style="min-width: 10rem" pSortableColumn="gender">
              <div class="flex justify-content-between align-items-center">
                Gender
                <!-- <p-sortIcon field="gender"></p-sortIcon>
            <p-columnFilter field="gender" matchMode="equals" display="menu">
              <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                <p-dropdown [ngModel]="value" [options]="GenderList" (onChange)="filter($event.value)"
                  placeholder="Any" [style]="{'min-width': '12rem'}">
                  <ng-template let-option pTemplate="item">
                    <span [class]="'customer-badge status-' + option.value">{{option.label}}</span>
                  </ng-template>
                </p-dropdown>
              </ng-template>
            </p-columnFilter> -->
              </div>
            </th>
            <th style="min-width: 12rem" pSortableColumn="phonenumber">
              <div class="flex justify-content-between align-items-center">
                Phone<!-- <p-sortIcon field="PhoneNumber"></p-sortIcon>
                <p-columnFilter type="text" field="PhoneNumber" display="menu"
                  placeholder="Search by phone"></p-columnFilter>-->
              </div>
            </th>

            <th style="min-width: 12rem" pSortableColumn="role">
              <div class="flex justify-content-between align-items-center">
                Role <!--<p-sortIcon field="Roles"></p-sortIcon>
                <p-columnFilter type="text" field="Roles.Name" display="menu"
                  placeholder="Search by role"></p-columnFilter>-->
              </div>
            </th>

            <th style="min-width: 10rem" pSortableColumn="status">
              <div class="flex justify-content-between align-items-center">
                Status <!--<p-sortIcon field="Status"></p-sortIcon>
                <p-columnFilter field="Status" matchMode="equals" display="menu">
                  <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                    <p-dropdown [ngModel]="value" [options]="StatusList" (onChange)="filter($event.value)"
                      placeholder="Any" [style]="{'min-width': '12rem'}">
                      <ng-template let-option pTemplate="item">
                        <span [class]="'customer-badge status-' + option.value">{{option.label}}</span>
                      </ng-template>
                    </p-dropdown>
                  </ng-template>
                </p-columnFilter>-->
              </div>
            </th>
            <th style="min-width: 10rem">
              <div class="flex justify-content-between align-items-center">
                Actions
              </div>
            </th>

          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-User>
          <tr>
            <td>
              {{User.fullName}}
            </td>
            <td>
              {{User.email}}
            </td>
            <td class="image-container">
              <img *ngIf=" getGenderString(User.gender) === 'Male'" src="../../../../assets/male.png" alt="Male">
              <img *ngIf=" getGenderString(User.gender) === 'Female'" src="../../../../assets/female.png" alt="Female">
              <img *ngIf=" getGenderString(User.gender) === 'Other'" src="../../../../assets/other.png" alt="Male">
            </td>
            <td>{{ User.phoneNumber }}</td>
            <td>
              <span *ngFor="let role of User.roles;let last = last">
                <p-chip class="mr-2">{{ role.name }}</p-chip>
                <span *ngIf="!last">,</span>
              </span>
            </td>
            <td>
              <span [class]="'component-badge status-' + getStatusString(User.status)">
                {{ getStatusString(User.status) }}
              </span>
            </td>
            <td>
               <p-button pRipple type="button" [outlined]="true" icon="pi pi-pencil" [pTooltip]="'Edit User'"
                [style]="{'margin-right.px': '10'}" styleClass="p-button-outlined " (click)="displayUserDialog(User)"
                [disabled]="getStatusString(User.status) === 'Deleted'">
                </p-button>
                <p-button Ripple type="button" [outlined]="true" icon="pi pi-trash" [pTooltip]="'Delete User'"
                  [disabled]="User.id == itsMe" [style]="{'margin-right.px': '10'}" styleClass="p-button-outlined "
                  (click)="showDeleteConfirmation(User.id)">
                </p-button>
                <p-button Ripple type="button" [outlined]="true" icon="pi pi-user-minus" [pTooltip]="'Blacklist User'"
                  [style]="{'margin-right.px': '10'}" styleClass="p-button-outlined "
                  (click)="showBlacklistConfirmation(User.id)">
                </p-button>
            </td>

          </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage">
          <tr>
            <td colspan="8">No Users found.</td>
          </tr>
        </ng-template>
        <ng-template pTemplate="loadingbody">
          <tr>
            <td colspan="8">Loading Users data. Please wait.</td>
          </tr>
        </ng-template>
      </p-table>
      <!-- Delete Confirmation Component -->
       
        <app-add-confirmation [display]="displayBlacklistDialog" (confirm)="confirmBlacklist()" (cancelDelete)="onCancelBlacklist()"
        (elementDeleted)="onElementBlacklist()"></app-add-confirmation>
        
        <app-add-confirmation [display]="displayDeleteDialog" (confirm)="confirmDelete()" (cancelDelete)="onCancelDelete()"
        (elementDeleted)="onElementDeleted()"></app-add-confirmation>
 
  
  
      </div>
  </div>
  <p-toast key="toast"></p-toast>
</div>