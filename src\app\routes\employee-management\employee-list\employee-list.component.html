<div class="grid">
    <div class="col-12">
        <div class="card">

            <p-table #dt1 [value]="employees" dataKey="id" [loading]="loading" [rows]="10" [rowHover]="true"
                rowGroupMode="subheader" (onSort)="onSort()" responsiveLayout="scroll"
                styleClass="p-datatable-gridlines"
                [globalFilterFields]="['user.fullName','user.gender','user.group','user.email','user.status']"
                [paginator]="true" [scrollable]="true" scrollHeight="400px">

                <ng-template pTemplate="caption">
                    <div>

                        <p-button pRipple type="button" icon="pi pi-filter-slash" label="Clear" [outlined]="true"
                        [pTooltip]="'Clear Filters'" (click)="clear(dt1)" [style]="{'margin-right.px': '10'}"
                        styleClass="p-button-outlined ">
                        <span class="tooltip"></span></p-button>

                        <p-button pRipple type="button" icon="pi pi-plus" label="Add" [outlined]="true"
                        [pTooltip]="'Add Employee'" (click)="displayEmployeeDialog(null)" [style]="{'margin-right.px': '10'}"
                        styleClass="p-button-outlined ">
                        <span class="tooltip"></span></p-button>

                        <app-employee-associate [display]="displayAddEditDialog" [selectedEmployee]="selectedEmployee"
                            [companyId]="companyId" (employeeAdded)="onEmployeeAdded()"
                            (closeAddDialogEvent)="closeAddEditDialogEvent($event)"></app-employee-associate>

                    </div>
                </ng-template>

                <ng-template pTemplate="header">
                    <tr>
    
                        <th style="min-width: 12rem" pSortableColumn="user.fullName">
                        <div class="flex justify-content-between align-items-center">
                        <span>Name</span>
                        <div class="flex align-items-center">
                            <p-sortIcon field="FullName" pTooltip="Sort Data" pTooltipPosition="right" pTooltipStyleClass="custom-tooltip"></p-sortIcon>
                            <p-columnFilter pTooltip="Filter Data" type="text" field="FullName" display="menu" placeholder="Search by name"></p-columnFilter>
                        </div>
                    </div>
                        </th>



        <th style="min-width: 12rem">
            <div class="flex justify-content-between align-items-center">
        <th pSortableColumn="user.gender">Gender<!-- <p-sortIcon field="user.gender"></p-sortIcon></th>
        <p-columnFilter type="text" field="user.gender" display="menu" placeholder="Search by gender"></p-columnFilter>-->
    </div>
    </th>

    <th style="min-width: 12rem">
        <div class="flex justify-content-between align-items-center">
    <th pSortableColumn="user.group">Group <!--<p-sortIcon field="user.group"></p-sortIcon></th>
    <p-columnFilter type="text" field="user.group" display="menu" placeholder="Search by group"></p-columnFilter>-->
</div>
</th>

<th style="min-width: 12rem">
    <div class="flex justify-content-between align-items-center">
<th pSortableColumn="user.phoneNumber">Phone <!--<p-sortIcon field="user.phoneNumber"></p-sortIcon></th>
<p-columnFilter type="text" field="user.phoneNumber" display="menu" placeholder="Search by phone"></p-columnFilter>-->
</div>
</th>

<th style="min-width: 12rem">
    <div class="flex justify-content-between align-items-center">
<th pSortableColumn="user.email">Email <!--<p-sortIcon field="user.email"></p-sortIcon></th>
<p-columnFilter type="text" field="user.email" display="menu" placeholder="Search by email"></p-columnFilter>-->
</div>
</th>

<th style="min-width: 12rem">
    <div class="flex justify-content-between align-items-center">
<th pSortableColumn="user.status">Status <!--<p-sortIcon field="user.status"></p-sortIcon></th>
<p-columnFilter field="status" matchMode="equals" display="menu">
    <ng-template pTemplate="filter" let-value let-filter="filterCallback">
        <p-dropdown [ngModel]="value" [options]="StatusList" (onChange)="filter($event.value)" placeholder="Any"
            [style]="{'min-width': '12rem'}">
            <ng-template let-option pTemplate="item">
                <span [class]="'customer-badge status-' + option.value">{{option.label}}</span>
            </ng-template>
        </p-dropdown>
    </ng-template>
</p-columnFilter>-->
</div>
</th>
<th style="min-width: 10rem">
    <div class="flex justify-content-between align-items-center">
        Actions
    </div>
</th>
</tr>
</ng-template>

<ng-template pTemplate="body" let-employee let-rowIndex="rowIndex">
    <tr>
        <td>{{employee.user.fullName}}</td>
        <td >
            <img *ngIf="employee.user.gender == Gender.Male" src="../../../../assets/male.png" alt="Male">
            <img *ngIf="employee.user.gender == Gender.Female" src="../../../../assets/female.png" alt="Female">
            <img *ngIf="employee.user.gender == Gender.Other" src="../../../../assets/other.png" alt="Other">


        </td>
        <td>{{employee.groupName}}</td>
        <td>{{employee.user.phoneNumber}}</td>
        <td>{{employee.user.email}}</td>
        <td>
            <span [class]="'component-badge status-' + getStatusString(employee.status)">
                {{getStatusString(employee.status)}}
            </span>
        </td>
        <td>

            <p-button pRipple type="button" icon="pi pi-pencil" label="" [outlined]="true"
            [pTooltip]="'Edit Employee'" (click)="displayEmployeeDialog(employee)" [style]="{'margin-right.px': '10'}"
            styleClass="p-button-outlined ">
            <span class="tooltip"></span></p-button>

            <p-button pRipple type="button" icon="pi pi-trash" label="" [outlined]="true"
            [pTooltip]="'Delete Employee'" (click)="showDeleteConfirmation(employee)" [style]="{'margin-right.px': '10'}"
            styleClass="p-button-outlined ">
            <span class="tooltip"></span></p-button>

            <p-button pRipple type="button" icon="pi pi-send" label="" [outlined]="true"
            [pTooltip]="'Send Money'" (click)="showSendConfirmation(employee)" [style]="{'margin-right.px': '10'}"
            styleClass="p-button-outlined " [disabled]="!isEmployeeActive(employee)">
            <span class="tooltip"></span></p-button>
        </td>
    </tr>
</ng-template>

<ng-template pTemplate="emptymessage">
    <tr>
        <td colspan="7">No employees found.</td>
    </tr>
</ng-template>

<ng-template pTemplate="loadingbody">
    <tr>
        <td colspan="7">Loading employees data. Please wait.</td>
    </tr>
</ng-template>
</p-table>



<!-- Delete Confirmation Component -->
<app-add-confirmation [display]="displayDeleteDialog" (confirm)="confirmDelete()" (cancelDelete)="onCancelDelete()"
(elementDeleted)="onElementDeleted()"></app-add-confirmation>

<!-- Send Confirmation Component -->
<app-send-ticket-to-employee-confirmation [display]="displaySendDialog" [selectedEmployee]="selectedEmployee"
    (confirm)="confirmSend($event)" (cancelSend)="onCancelSend()"></app-send-ticket-to-employee-confirmation>
</div>
</div>
<p-toast key="toast"></p-toast>
</div>