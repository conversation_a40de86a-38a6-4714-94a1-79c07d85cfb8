# MockServer Configuration for Testing

# Enable CORS
mockserver.enableCORSForAPI=true
mockserver.enableCORSForAllResponses=true

# Logging
mockserver.logLevel=INFO

# Port configuration
mockserver.port=1080

# Initialize expectations from file
mockserver.initializationJsonPath=/expectations

# Disable TLS
mockserver.disableTLS=true

# Request matching
mockserver.matchersFailFast=false

# Response delays (for testing)
mockserver.maxExpectations=1000
mockserver.maxWebSocketExpectations=1000
