import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { DialogModule } from 'primeng/dialog';

@Component({
    selector: 'app-cancel-confirmation',
    templateUrl: './cancel-confirmation.component.html',
    styleUrls: ['./cancel-confirmation.component.scss']
})
export class CancelConfirmationComponent implements OnInit {
    @Input() display: boolean = false;
    @Output() confirm: EventEmitter<string> = new EventEmitter<string>();
    @Output() elementDeleted = new EventEmitter<void>();
    @Output() cancelDelete = new EventEmitter<void>();
    disableConfirmCanlcelButton = false;
    loading = false;
    constructor() { }
    reason: string = ""
    ngOnInit() { }

    confirmDelete() {
        this.loading = true;
        this.disableConfirmCanlcelButton = true;
        this.confirm.emit(this.reason);
        this.elementDeleted.emit();
    }
    onCancel() {
        this.reason = ""
        this.cancelDelete.emit();
    }

}
