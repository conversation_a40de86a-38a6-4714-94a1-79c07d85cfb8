import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LoggingManagementComponent } from './logging-management/logging-management.component';
import { AdminOverviewComponent } from './admin-overview/admin-overview.component';
import { SuperadminOverviewComponent } from './superadmin-overview/superadmin-overview.component';
import { RoleGuard } from 'src/app/RoleGuard';
const routes: Routes = [
  { path: 'Overview',canActivate:[RoleGuard],data: { breadcrumb: 'Overview',role:["company", "shopowner"]as string[] }, component: AdminOverviewComponent },
  { path: 'OverviewS', canActivate:[RoleGuard],data: { breadcrumb: 'Overview',role:["superadmin"]as string[] }, component: SuperadminOverviewComponent },
  { path: 'Logging', canActivate:[RoleGuard],data: { breadcrumb: 'Logging',role:["superadmin"]as string[] }, component: LoggingManagementComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DashboardRoutingModule { }
