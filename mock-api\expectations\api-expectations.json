[{"httpRequest": {"method": "POST", "path": "/Api/Auth/login"}, "httpResponse": {"statusCode": 200, "headers": {"Content-Type": ["application/json"]}, "body": {"success": true, "objectValue": {"token": "mock-jwt-token-12345", "refreshToken": "mock-refresh-token-67890", "expiredDate": "2024-12-31T23:59:59Z", "user": {"id": "user-123", "name": "Test User", "email": "<EMAIL>", "role": "admin"}}, "errorMessage": null}}}, {"httpRequest": {"method": "POST", "path": "/Api/Company/GetAllPaged"}, "httpResponse": {"statusCode": 200, "headers": {"Content-Type": ["application/json"], "x-pagination": ["{\"TotalCount\":50,\"CurrentPage\":1,\"PageSize\":10,\"TotalPages\":5,\"HasNext\":true,\"HasPrevious\":false}"]}, "body": {"success": true, "objectValue": [{"id": "company-1", "name": "Test Company 1", "email": "<EMAIL>", "phoneNumber": "+1234567890", "status": 1, "entrepriseType": 1, "serviceType": 1, "employees": []}, {"id": "company-2", "name": "Test Company 2", "email": "<EMAIL>", "phoneNumber": "+1234567891", "status": 1, "entrepriseType": 2, "serviceType": 2, "employees": []}], "errorMessage": null}}}, {"httpRequest": {"method": "POST", "path": "/Api/AuditLog/GetFiltered"}, "httpResponse": {"statusCode": 200, "headers": {"Content-Type": ["application/json"], "x-pagination": ["{\"TotalCount\":25,\"CurrentPage\":1,\"PageSize\":10,\"TotalPages\":3,\"HasNext\":true,\"HasPrevious\":false}"]}, "body": {"success": true, "objectValue": [{"id": "audit-1", "userId": "user-123", "userName": "Test User", "userRole": "admin", "action": "CREATE", "entityType": "Company", "entityId": "company-1", "entityName": "Test Company 1", "ipAddress": "*************", "userAgent": "Mozilla/5.0...", "timestamp": "2024-01-15T10:30:00Z", "success": true, "sessionId": "session-123", "correlationId": "corr-456"}], "errorMessage": null}}}, {"httpRequest": {"method": "GET", "path": "/Api/AuditLog/GetSummary"}, "httpResponse": {"statusCode": 200, "headers": {"Content-Type": ["application/json"]}, "body": {"success": true, "objectValue": {"totalLogs": 1250, "successfulActions": 1180, "failedActions": 70, "uniqueUsers": 45, "mostCommonAction": "READ", "mostActiveUser": "<EMAIL>", "actionDistribution": {"CREATE": 150, "READ": 800, "UPDATE": 200, "DELETE": 30, "LOGIN": 70}, "entityDistribution": {"User": 300, "Company": 400, "Employee": 350, "Role": 50, "System": 150}}, "errorMessage": null}}}, {"httpRequest": {"method": "GET", "path": "/Api/AdvancedRole/GetAll"}, "httpResponse": {"statusCode": 200, "headers": {"Content-Type": ["application/json"]}, "body": {"success": true, "objectValue": [{"id": "role-1", "name": "admin", "displayName": "Administrateur", "description": "Accès complet au système", "isSystemRole": true, "isActive": true, "permissions": [{"id": "perm-1", "name": "company.create", "displayName": "Créer des entreprises", "action": "create", "resource": "company", "isGranted": true}], "userCount": 5, "hierarchy": 0}], "errorMessage": null}}}, {"httpRequest": {"method": "GET", "path": "/Api/Notification/GetUserNotifications"}, "httpResponse": {"statusCode": 200, "headers": {"Content-Type": ["application/json"], "x-pagination": ["{\"TotalCount\":15,\"CurrentPage\":1,\"PageSize\":10,\"TotalPages\":2,\"HasNext\":true,\"HasPrevious\":false}"]}, "body": {"success": true, "objectValue": [{"id": "notif-1", "title": "Nouvelle entreprise créée", "message": "L'entreprise Test Company a été créée avec succès", "type": "success", "priority": "normal", "category": "business", "isRead": false, "isArchived": false, "createdAt": "2024-01-15T14:30:00Z", "source": "system"}], "errorMessage": null}}}]