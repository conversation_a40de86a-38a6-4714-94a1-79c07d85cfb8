import { Injectable } from "@angular/core";
import { ResponseAPI } from "../shared/models/ResponseAPI";
import { environment } from "src/environments/environment";
import { HttpClient } from "@angular/common/http";
import { RefundDTO } from "../shared/models/refund/refund";


@Injectable({
    providedIn: 'root'
})
export class RefundService {
    constructor(private http: HttpClient) {

    }

    GetAllRefundDemands() {
        return this.http.get<ResponseAPI<RefundDTO[]>>(`${environment.API}/RefundTransaction/GetRefundDemands`);
    }
    ValidateCancledTransaction(TransactionId:string) {
        return this.http.get<ResponseAPI<RefundDTO[]>>(`${environment.API}/RefundTransaction/ValidateCancledTransaction/${TransactionId}`);
    }
    RejectCancledTransaction(TransactionId:string) {
        return this.http.get<ResponseAPI<RefundDTO[]>>(`${environment.API}/RefundTransaction/RejectCancledTransaction/${TransactionId}`);
    }




}
