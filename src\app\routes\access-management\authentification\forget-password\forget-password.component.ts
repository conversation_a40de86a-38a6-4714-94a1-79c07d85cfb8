import { Component } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';

import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { AuthService } from 'src/app/services/auth.service';
import { ForgetPasswordDTO } from 'src/app/shared/models/authentification/password/ForgetPasswordDTO';
import { emailFormatValidator } from 'src/app/shared/components/validator';
import { MessageService } from 'primeng/api';
import { Router } from '@angular/router';
import { ClientURI, getClientURI } from 'src/app/shared/enums/clientURI';
@Component({
    templateUrl: './forget-password.component.html'
})
export class ForgetPasswordComponent {
    disableSubmitButton: boolean = false;
    isSubmitClicked: boolean = false;
    countdownValue: number = 20;
    email: string = '';
    isEmailValid: boolean = true;
    constructor(private layoutService: LayoutService, private authService: AuthService, private messageService: MessageService,
        private router: Router) { }



    get dark(): boolean {
        return this.layoutService.config.colorScheme !== 'light';
    }

    cancel() {
        this.router.navigate(['/auth/login']);
    }

    forgotPassword(): void {
        this.isEmailValid = emailFormatValidator(this.email);
        if (this.isEmailValid) {
            this.disableSubmitButton = true;
            this.isSubmitClicked = true;
            var data: ForgetPasswordDTO = {
                email: this.email,
                clientURI: getClientURI()
            };
            this.authService.ForgetPassword(data).subscribe((Response) => {
                if (Response.statusCode == 200) {
                    this.messageService.add({ key: 'toast', severity: 'success', summary: 'Success Message', detail: Response.exceptionMessage });
                    this.startCountdown();
                }
                else {
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Message', detail: Response.exceptionMessage });
                    this.disableSubmitButton = false;
                    this.isSubmitClicked = false;
                }
            })
        }

    }
    startCountdown() {
        const countdownInterval = setInterval(() => {
            this.countdownValue--;
            if (this.countdownValue <= 0) {
                clearInterval(countdownInterval);
                this.disableSubmitButton = false;
                this.countdownValue = 20;
                this.isSubmitClicked = false;
            }
        }, 1000)
    }
}
