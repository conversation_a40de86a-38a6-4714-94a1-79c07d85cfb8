import { Component, ElementRef, OnChanges, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { SelectItem } from 'primeng/api/selectitem';
import { Table } from 'primeng/table';
import { CashbackService } from 'src/app/services/cashback.service';
import { PaymentMethod } from 'src/app/shared/enums/PaymentMethod';
import { CashbackStatus, CashbackStatusString } from 'src/app/shared/enums/cashbackStatus';
import { ProductType } from 'src/app/shared/enums/ProductType';
import { Cashback } from 'src/app/shared/models/cashback/cashback';
import { LocalStoreService } from 'src/app/services/local-store.service';
import { tokenDTO } from 'src/app/shared/models/authentification/token';
import jwtDecode from 'jwt-decode';
import { FailedCashback } from 'src/app/shared/models/cashback/failedCashback';
import { ResponseAPI } from 'src/app/shared/models/ResponseAPI';

@Component({
    selector: 'app-cashback-list',
    templateUrl: './cashback-list.component.html',
    styleUrls: ['./cashback-list.component.scss']
})
export class CashbackListComponent implements OnInit {
    requestType: string = "";
    cashbacks: Cashback[] = [];
    Status: CashbackStatus = CashbackStatus.InProgress;
    displayAddEditDialog: boolean = false;
    displayDeleteDialog: boolean = false;
    selectedCashbackId: string = "";
    rowGroupMetadata: any;
    selectedCashback: Cashback | null = null;
    activityValues: number[] = [0, 100];
    isExpanded: boolean = false;
    idFrozen: boolean = false;
    loading: boolean = true;
    isUserSuperAdmin: boolean = false;
    cashbackStatusList = this.enumToArray(CashbackStatus);
    productTypeList = this.enumToArray(ProductType);
    PaymentMethodList = this.enumToArray(PaymentMethod);
    failedCashback: FailedCashback[] = [];
    cashbackToDelete: FailedCashback | null = null;
    @ViewChild('filter') filter!: ElementRef;
    tableData: any;

    constructor(private route: ActivatedRoute,
        private cashbackService: CashbackService,
        private localStorageService: LocalStoreService,
        private confirmationService: ConfirmationService,
        private messageService: MessageService) { }
    ngOnInit(): void {
        this.route.queryParams.subscribe(params => {
            console.log('params', params)
            this.requestType = params['requestType'];
            this.loadCashbacks();
            // this.getData();
        });

        let token = this.localStorageService.getData("Token");
        if (token != undefined) {
            let decodedToken: tokenDTO = jwtDecode(token);
            console.log("decodedToken", decodedToken.Role);
            let Roles = decodedToken.Role;
            if (Roles.includes("SuperAdmin")) {
                this.isUserSuperAdmin = true;
            }
        }

    }
    // ngOnChanges() {
    //     this.loadCashbacks();
    // }
    enumToArray(enumerable: any): SelectItem[] {
        return Object.keys(enumerable)
            .filter(key => !isNaN(Number(enumerable[key])))
            .map(key => ({ label: key, value: enumerable[key] }));
    }
    getStatusString(statusValue: number): string {
        return CashbackStatusString[statusValue];
    }
    getProductTypeString(productTypeValue: number): string {
        return ProductType[productTypeValue];
    }
    getPaymentMethodString(payentMethodValue: number): string {
        return PaymentMethod[payentMethodValue];
    }
    // getData() {
    //     if (this.requestType == "Failed") {
    //         this.tableData = this.failedCashback;
    //         console.log("tableData1111111", this.tableData)
    //     }
    //     else {
    //         this.tableData = this.cashbacks;
    //         console.log("tableData2", this.tableData)
    //     }
    // }
    loadCashbacks() {
        if (this.requestType == "New") {
            this.Status = CashbackStatus.InProgress;
            this.cashbackService.getAllCashbacksByStatus(this.Status).subscribe(response => {
                if (response.objectValue) {
                    this.cashbacks = response.objectValue;
                    this.tableData = this.cashbacks;
                    console.log("reponse cashbacks", response.objectValue);
                    this.loading = false;
                }
            },
                (error) => {
                }
            );
        }
        else if (this.requestType == "Failed") {
            this.Status = CashbackStatus.Invalid;
            this.cashbackService.getAllFailedCashbacks().subscribe(response => {
                if (response.objectValue) {
                    this.failedCashback = response.objectValue;
                    this.tableData = this.failedCashback;
                    console.log("reponse cashbacks", response.objectValue);
                    this.loading = false;
                    console.log('failedSO', this.failedCashback)
                }
            },
                (error) => {
                }
            );

        }
        else if (this.requestType == "Valid") {
            this.Status = CashbackStatus.Valid;
            this.cashbackService.getAllCashbacksByStatus(this.Status).subscribe(response => {
                if (response.objectValue) {

                    this.cashbacks = response.objectValue;
                    this.tableData = this.cashbacks;
                    console.log("reponse cashbacks", response.objectValue);
                    this.loading = false;
                }
            },
                (error) => {
                }
            );
        }

    }

    onSort() {
        //this.updateRowGroupMetaData();
    }

    onGlobalFilter(table: Table, event: Event) {
        table.filterGlobal((event.target as HTMLInputElement).value, 'contains');
    }

    clear(table: Table) {
        table.clear();
        this.filter.nativeElement.value = '';
    }
    showCancelConfirmation(cashback: Cashback) {
        if (cashback.id !== undefined) {
            this.selectedCashbackId = cashback.id;
            this.displayDeleteDialog = true;
        }
        else {
            console.log('nope');

        }
    }
    confirmDelete(reason: string) {
        if (this.selectedCashbackId !== null) {
            this.cashbackToDelete = {
                reason: reason,
                cashbackId: this.selectedCashbackId
            }
            this.cashbackService.deleteCashback(this.cashbackToDelete).subscribe(
                (response) => {

                    if (response.statusCode == 201) {
                        this.displayDeleteDialog = false;
                        this.messageService.add({ key: 'toast', severity: 'success', summary: 'Cashback rejected successfully', detail: response.exceptionMessage });
                        this.loadCashbacks();
                    }
                    else {
                        this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Delete Cashback', detail: response.exceptionMessage });

                    }
                },
                (error) => {
                    this.displayDeleteDialog = false;

                }
            );
        }
    }


    displayCashbackDialog(cashback: Cashback | null) {
        console.log("tiiiicket", this.cashbacks)

        this.selectedCashback = cashback;
        this.displayAddEditDialog = true;
        return this.displayAddEditDialog;
    }
    // displayValidateCashbackDialog(cashback: Cashback) {
    //     console.log("click", cashback)
    //     this.confirmationService.confirm({
    //         key: 'displayValidateCashbackDialog',
    //         message: 'Are you sure that you want to validate the cashback ' +
    //             'request of ' + cashback.dynoAmount.toString().bold() + ' Dynos from ' + cashback?.company?.name.toString().bold() + ' ?',
    //         accept: () => {
    //             console.log("tttt")
    //             this.messageService.add({ severity: 'info', summary: 'Confirmed', detail: 'You have validated the transfert request' });
    //             this.cashbackService.validateCashback(cashback);

    //         }
    //     });
    // }
    async displayValidateCashbackDialog(cashback: Cashback) {
        const confirmed = await this.showConfirmationDialog(cashback);

        if (confirmed) {
            console.log("tttt");
            this.cashbackService.validateCashback(cashback).subscribe({
                next: (response: ResponseAPI<Cashback>) => {
                    if (response.statusCode == 200) {
                        this.messageService.add({ key: 'toast', severity: 'success', summary: 'Confirmed', detail: 'You have validated the transfer request' });
                    } else {
                        this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Message', detail: response.exceptionMessage });
                    }

                    this.loadCashbacks()
                },
                error: (error: any) => {
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Transaction Failed' });
                    console.log('Error validating sales order:', error);
                    this.loadCashbacks()
                }
            });
        }
    }

    showConfirmationDialog(cashback: Cashback): Promise<boolean> {
        return new Promise<boolean>(resolve => {
            this.confirmationService.confirm({
                key: 'displayValidateCashbackDialog',
                message: 'Are you sure that you want to validate the cashback ' +
                    'request of ' + cashback.dynoAmount.toString().bold() + ' Dynos from ' + cashback?.company?.name.toString().bold() + ' ?',
                accept: () => {
                    resolve(true)
                    close()
                },
                reject: () => {
                    resolve(false)
                    close()
                }
            });
        });
    }
    closeAddEditDialogEvent(e: Event) {
        this.displayAddEditDialog = false;
    }
    onCashbackAdded() {
        this.loadCashbacks();
    }
    onElementDeleted() {

        this.loadCashbacks();
    }
    onCancelDelete() {
        this.displayDeleteDialog = false;
    }

}
