<div class="card">
    <p-table #transactionsTable [value]="cashiers" dataKey="userId" [loading]="loading" [expandedRowKeys]="expandedRows"
        [tableStyle]="{ 'min-width': '60rem' }">
        <ng-template pTemplate="caption">

            <p-button pRipple icon="pi pi-fw {{isExpanded ? 'pi-minus' : 'pi-plus'}}" outlined="true"
                [pTooltip]="'Expand Or Collapse All Transactions'" [style]="{'margin-right.px': '10'}"
                styleClass="p-button-outlined " label="{{isExpanded ? 'Collapse All' : 'Expand All'}}"
                (click)="expandAll()">
                <span class="tooltip"></span></p-button>

            <p-button pRipple label="Clear" outlined="true" [pTooltip]="'Clear Filters'"
                [style]="{'margin-right.px': '10'}" styleClass="p-button-outlined " icon="pi pi-filter-slash"
                (click)="clear(transactionsTable)"></p-button>
        </ng-template>
        <ng-template pTemplate="header">
            <tr>
                <th style="width: 5rem"></th>
                <th pSortableColumn="badget">Badget <p-sortIcon field="badget"></p-sortIcon></th>
                <th pSortableColumn="fullName">FullName <p-sortIcon field="fullName"></p-sortIcon></th>
                <th pSortableColumn="email">Email <p-sortIcon field="email"></p-sortIcon></th>
                <th pSortableColumn="phoneNumber">Phone Number <p-sortIcon field="phoneNumber"></p-sortIcon></th>
                <th pSortableColumn="gender">Gender <p-sortIcon field="gender"></p-sortIcon></th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-cashier let-expanded="expanded">
            <tr>
                <td>
                    <button type="button" pButton pRipple [pRowToggler]="cashier"
                        class="p-button-text p-button-rounded p-button-plain"
                        [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"></button>
                </td>
                <td>{{ cashier.userName }}</td>
                <td>{{ cashier.fullName }}</td>
                <td>{{cashier.email}}</td>
                <td>{{ cashier.phoneNumber}}</td>
                <td class="image-container">
                    <img *ngIf=" getGenderString(cashier.gender) === 'Male'" src="../../../../assets/male.png"
                        alt="Male">
                    <img *ngIf=" getGenderString(cashier.gender) === 'Female'" src="../../../../assets/female.png"
                        alt="Female">
                    <img *ngIf=" getGenderString(cashier.gender) === 'Other'" src="../../../../assets/other.png"
                        alt="Male">
                </td>

            </tr>
        </ng-template>
        <ng-template pTemplate="rowexpansion" let-cashier>
            <tr>
                <td colspan="7">
                    <div class="p-3">
                        <p-table [value]="cashier.transactions" [rows]="4" [paginator]="true"
                            [showCurrentPageReport]="true"
                            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
                            selectionMode="multiple" [rowHover]="true" dataKey="userId">
                            <ng-template pTemplate="header">
            <tr>
       

                <th pSortableColumn="senderWalletId">Sender Name </th>
                <th pSortableColumn="senderWalletId">Receiver Name </th>
                <th pSortableColumn="amount">Amount </th>
                <th pSortableColumn="transactionDate">Transaction Date</th>
                <th pSortableColumn="isCredit">Credit </th>
                <th style="width: 4rem"></th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-transaction>
            <tr>

                <td>{{ transaction.senderName }}</td>
                <td>{{ transaction.receiverName }}</td>
                <td>{{ transaction.amount }}</td>
                <td>{{ transaction.transactionDate }}</td>
                <td>
                    <i class="pi"
                        [ngClass]="{'pi-sort-amount-down-alt': transaction.isCredit, 'pi-sort-amount-up': !transaction.isCredit}"
                        [ngStyle]="{'color': transaction.isCredit ? 'green' : 'red', 'font-size': '24px' }">
                    </i>
                </td>

            </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage">
            <tr>
                <td colspan="6">There are no transaction for this cashier yet.</td>
            </tr>
        </ng-template>
    </p-table>
</div>
</td>
</tr>
</ng-template>
</p-table>
</div>