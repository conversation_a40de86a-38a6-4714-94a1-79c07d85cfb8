import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ForgetPasswordComponent } from './forget-password.component';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { AppConfigModule } from 'src/app/layout/config/app.config.module';
import { AuthentificationRoutingModule } from '../authentification-routing.module';
import { FormsModule } from '@angular/forms';
import { ToastModule } from 'primeng/toast';

@NgModule({

  imports: [
    AuthentificationRoutingModule,
    ButtonModule,
    InputTextModule,
    AppConfigModule,
    CommonModule,
    FormsModule,
    ToastModule,
  ],
  declarations: [
    ForgetPasswordComponent
  ]
})
export class ForgetPasswordModule { }
