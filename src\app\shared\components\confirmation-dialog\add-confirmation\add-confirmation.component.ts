import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-add-confirmation',
  templateUrl: './add-confirmation.component.html',
  styleUrls: ['./add-confirmation.component.scss']
})
export class AddConfirmationComponent implements OnInit {
  @Input() display: boolean = false;
  @Output() confirm: EventEmitter<void> = new EventEmitter<void>();
  @Output() elementDeleted = new EventEmitter<void>();
  @Output() cancelDelete = new EventEmitter<void>();
  loading = false;
  constructor() { }

  ngOnInit() { }

  confirmAdd() {
    this.loading = true;
    this.confirm.emit();
    this.elementDeleted.emit();
  }
  onCancel(){
    this.cancelDelete.emit();
  }
}
