import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { Observable, catchError } from 'rxjs';
import { ErrorHandlerService } from 'src/app/services/error-handler.service';

@Injectable()
export class ErrorInterceptor implements HttpInterceptor {

  constructor(private errorHandler: ErrorHandlerService) { }

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(req).pipe(
      catchError((error: HttpErrorResponse) => {
        // Ne pas traiter les erreurs 401 ici car elles sont gérées par l'intercepteur d'auth
        if (error.status !== 401) {
          return this.errorHandler.handleHttpError(error);
        }
        
        // Laisser passer les erreurs 401 pour l'intercepteur d'auth
        throw error;
      })
    );
  }
}
