
.btn-save-role{
    width:50px !important;
    height: 50px !important;
}
.row{
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;
    flex-direction: row;
}
.input-name{
    margin-bottom: 0px !important;
}
.input-width{
    width:100% !important;
}
.dd-status{

    padding-left: 14px !important;
    margin-bottom: 0px !important;

}
.footer{
padding-top: 15px !important;
}
.zero-padding{
    width:100% !important;
    padding: 0px !important;
    border: 0px !important;
}
.input-VAT{
    width:100% !important;
}
:host::ng-deep{
    .p-dropdown{
        width:100% !important;
    }
    .p-dialog .p-dialog-footer {
        padding-top: 15px !important;
    }
    .p-dropdown-items-wrapper {
        overflow: auto;
        max-height:13vh !important;
    }
    .p-inputnumber{
        width: 100%;
    }

}
.country-code-with-flag {
    display: flex;
    align-items: center;
    margin-right: 5px; /* Adjust spacing as needed */
}

.flag-icon {
    height: 20px; /* Adjust size as needed */
    margin-right: 5px; /* Adjust spacing as needed */
}

.country-code {
    font-weight: bold;
    margin-right: 5px; /* Adjust spacing as needed */
}

.custom-file-upload {
    cursor: pointer;
    display: inline-block;
    padding: 10px 20px;
    background-color: #007bff;
    color: #fff;
    border-radius: 5px;
}

.custom-file-upload i {
    margin-right: 5px;
}

/* Additional styling for the icon (FontAwesome used in this example) */
.fa-cloud-upload {
    font-size: 18px;
}

#file-upload {
    display: none;
}

.custom-row {
    display: flex;
    
}

.left {
    width: 75%;
}
.right {
    justify-content: flex-end;
}


