<p-dialog [(visible)]="display" [modal]="true" (onHide)="onCancel()" class="confirmation-dialog" [closable]="false">
    <ng-template pTemplate="content">
        <div class="flex flex-column align-items-center p-2 surface-overlay border-round">
            <div class="circle-container">
                <div class="circle-icon">
                    <i class="pi pi-question text-5xl"></i>
                </div>
            </div>
            <span class="font-bold text-2xl block mb-2 mt-4">Are you sure?</span>
            <p class="mb-2">Please confirm to proceed.</p>
            <div>
                <button pButton type="button" label="Cancel" (click)="onCancel()" class="p-button-outlined cancel-button"></button>
                <button pButton type="button" (click)="confirmAdd()" 
                class="p-button-outlined confirm-button" [ngClass]="{ 'loading': loading }">
                    <span *ngIf="!loading">Confirm</span>
                    <i *ngIf="loading" class="pi pi-spin pi-spinner loader"></i>
                </button>
            </div>
        </div>
    </ng-template>
</p-dialog>
