.btn-save-role {
    width: 50px !important;
    height: 50px !important;
}

.row {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;
    flex-direction: row;
}

:host ::ng-deep .p-calendar{
    width: 100%;
}

.input-name {
    margin-bottom: 0px !important;
}

.input-width {
    width: 100% !important;
}

.dd-status {

    padding-left: 14px !important;
    margin-bottom: 0px !important;

}

.footer {
    padding-top: 15px !important;
}

.selected-role {
    display: inline-flex;
    align-items: center;
    gap: 2px;
    padding: 2px 6px;
    background-color: #bebebea4;
    /* Change the background color as needed */
    color: #292828;
    /* Change the text color as needed */
    border-radius: 4px;
}

.cancel-icon {
    cursor: pointer;
    margin-left: 4px;
    font-weight: bold;
    font-size: 16px;
    color: #292828;
    /* Change the color to match the text color of the selected chip */
}

.cancel-icon:hover {
    font-size: 20px;
}

::ng-deep {
    .p-dropdown {
        width: 100% !important;
    }

    // .p-inputtext {
    //     width: 100% !important;
    // }

    .p-dialog .p-dialog-footer {
        padding-top: 15px !important;
    }

    .p-dropdown-items-wrapper {
        overflow: auto;
        max-height: 13vh !important;
    }

    .input-min-width {
        min-width: 100% !important;
    }

    .p-multiselect-header {
        display: none !important;
    }

    // .row {
    //     display: flex;
    //     justify-content: flex-start;
    //     align-items: flex-start;
    //     flex-wrap: wrap;
    //     flex-direction: row;
    // }
}

.country-code-with-flag {
    display: flex;
    align-items: center;
    margin-right: 5px;
    /* Adjust spacing as needed */
}

.flag-icon {
    height: 20px;
    /* Adjust size as needed */
    margin-right: 5px;
    /* Adjust spacing as needed */
}

.country-code {
    font-weight: bold;
    margin-right: 5px;
    /* Adjust spacing as needed */
}

#file-upload {
    display: none;
}

/* Styling for the label */
.custom-file-upload {
    cursor: pointer;
    display: inline-block;
    padding: 10px 20px;
    background-color: #007bff;
    color: #fff;
    border-radius: 5px;
}

/* Styling for the icon inside the label */
.custom-file-upload i {
    margin-right: 5px;
}

/* Additional styling for the icon (FontAwesome used in this example) */
.fa-cloud-upload {
    font-size: 18px;
}
