import { UserType } from "../../enums/UserType";
import { Permission, PermissionRequest } from "./permission";

export class Role {
    id: string = "";
    name: string = "";
    userType: UserType = 0;
    companyId : string = "";
    status: number = 0;
    creationTime?: string = "";
    creatorUserId?: string;
    deleterUserId?: string;
    deletionTime?: string;
    lastModifierUserId?: string;
    lastModificationTime?: string;
    permissions: Permission[] = [];
}


export class RoleRequest {
    id?: string = "";
    name: string = "";
    userType: UserType = 0;
    status: number = 0;
    creationTime?: string = "";
    creatorUserId?: string;
    deleterUserId?: string;
    deletionTime?: string;
    lastModifierUserId?: string;
    lastModificationTime?: string;
    permissions: PermissionRequest[] = [];
}
