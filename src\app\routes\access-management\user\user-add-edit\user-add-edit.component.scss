.btn-save-role {
    width: 50px !important;
    height: 50px !important;
}

.row {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;
    flex-direction: row;
}

.input-name {
    margin-bottom: 0px !important;
}

.input-width {
    width: 100% !important;
}

.dd-status {

    padding-left: 14px !important;
    margin-bottom: 0px !important;

}

:host ::ng-deep .p-calendar{
    width: 100%;
}

.footer {
    padding-top: 15px !important;
}
.selected-role {
    display: inline-flex;
    align-items: center;
    gap: 2px;
    padding: 2px 6px;
    background-color: #bebebea4; /* Change the background color as needed */
    color: #292828; /* Change the text color as needed */
    border-radius: 4px;
}

.cancel-icon {
    cursor: pointer;
    margin-left: 4px;
    font-weight: bold;
    font-size: 16px;
    color: #292828; /* Change the color to match the text color of the selected chip */
}

.cancel-icon:hover {
    font-size: 20px;
}
:host ::ng-deep {
    .p-dropdown {
        width: 100% !important;
    }
    .p-inputtext {
        width: 100% !important;
    }
    .p-dialog .p-dialog-footer {
        padding-top: 15px !important;
    }

    .p-dropdown-items-wrapper {
        overflow: auto;
        max-height: 13vh !important;
    }
    .input-min-width {
        min-width: 100% !important;
    }
    .p-multiselect-header
    {
        display: none !important;
    }

    .p-multiselect-items-wrapper{
        position: relative;
        z-index: 1001 !important;
        background-color: white;
        min-width: 30%;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
    }
    .p-dropdown-items-wrapper{
        position: relative;
        z-index: 1001 !important;
        background-color: white;
        min-width: 30%;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
    }
    .multiselect-custom .p-multiselect-panel {
        z-index: 1001 !important;
        background-color: white;
        padding: 0px !important;
    }
    .p-dropdown-panel .p-dropdown-items{
        z-index: 1002 !important;
        background-color: white;

        padding: 0px !important;

    }

}

.country-code-with-flag {
    display: flex;
    align-items: center;
    margin-right: 5px; /* Adjust spacing as needed */
}

.flag-icon {
    height: 20px; /* Adjust size as needed */
    margin-right: 5px; /* Adjust spacing as needed */
}

.country-code {
    font-weight: bold;
    margin-right: 5px; /* Adjust spacing as needed */
}
