import { Component, ElementRef, NgZone, OnChanges, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ConfirmationService, FilterMetadata, MessageService } from 'primeng/api';
import { SelectItem } from 'primeng/api/selectitem';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { SalesOrderService } from 'src/app/services/sales-order.service';
import { PaymentMethod } from 'src/app/shared/enums/PaymentMethod';
import { SalesOrderStatus, SalesOrderStatusString } from 'src/app/shared/enums/SalesOrderStatus';
import { ProductType } from 'src/app/shared/enums/ProductType';
import { SalesOrder } from 'src/app/shared/models/salesOrder/salesOrder';
import { LocalStoreService } from 'src/app/services/local-store.service';
import { tokenDTO } from 'src/app/shared/models/authentification/token';
import jwtDecode from 'jwt-decode';
import { FailedSalesOrder } from 'src/app/shared/models/salesOrder/FailedSalesOrder';
import { environment } from 'src/environments/environment';
import { Subscription } from 'rxjs';
import { SalesOrderStatusNotifService } from 'src/app/services/salesOrderStatusNotif.service';


@Component({
    selector: 'app-sales-order-list',
    templateUrl: './sales-order-list.component.html',
    styleUrls: ['./sales-order-list.component.scss']
})

export class SalesOrderListComponent implements OnInit {
    requestType: string = "";
    salesOrders: SalesOrder[] = [];
    Status: SalesOrderStatus[] = [];
    displayAddEditDialog: boolean = false;
    displayDeleteDialog: boolean = false;
    selectedSalesOrderId: string = "";
    rowGroupMetadata: any;
    selectedSalesOrder: SalesOrder | null = null;
    activityValues: number[] = [0, 100];
    isExpanded: boolean = false;
    idFrozen: boolean = false;
    loading: boolean = true;
    isUserSuperAdmin: boolean = false;
    salesOrderStatusList = this.enumToArray(SalesOrderStatus).map(item => ({ label: item.label, value: item.value }));
    productTypeList = this.enumToArray(ProductType);
    PaymentMethodList = this.enumToArray(PaymentMethod);
    failedSalesOrder: FailedSalesOrder[] = [];
    salesOrderToDelete: FailedSalesOrder | null = null;
    @ViewChild('filter') filter!: ElementRef;
    tableData: any;
    S3URl: string = ""
    sortBy?: string | string[] | null | undefined;
    sortDirection: number = 1;
    first!: number;
    totalRecords: number = 1;
    pageSize: number = 5;
    pageNumber: number = 0;
    notifications: Notification[] = [];
    subscription!: Subscription;

    constructor(private route: ActivatedRoute,
        private salesOrderService: SalesOrderService,
        private localStorageService: LocalStoreService,
        private confirmationService: ConfirmationService,
        private messageService: MessageService,
        private salesOrderStatusNotifService: SalesOrderStatusNotifService,
        private ngZone: NgZone) {
        this.S3URl = environment.S3Url;
    }
    ngOnInit(): void {
        let token = this.localStorageService.getData("Token");
        if (token != undefined) {
            let decodedToken: tokenDTO = jwtDecode(token);
            console.log("decodedToken", decodedToken.Role);
            let Roles = decodedToken.Role;
            if (Roles.includes("SuperAdmin")) {
                this.isUserSuperAdmin = true;
            }
        }

        this.subscription = this.salesOrderStatusNotifService.notificationReceived.subscribe((notification) => {
            this.ngZone.run(() => {
              console.log(notification);
              const index = this.salesOrders.findIndex(sales => sales.id == notification.id);
              if(index != -1) {
                const parsedMessage = JSON.parse(notification.message);
                this.salesOrders[index].status = parsedMessage.Status;
                if (this.salesOrders[index].document) {
                    this.salesOrders[index].document.path = parsedMessage.Document.Path;
                } else {
                    this.salesOrders[index].document = { 
                        id: parsedMessage.Document.Id, 
                        name: parsedMessage.Document.Name,  
                        path: parsedMessage.Document.Path, 
                        code: parsedMessage.Document.Code, 
                        companyId: parsedMessage.Document.CompanyId, 
                        type: parsedMessage.Document.Type 
                    };
                }
              }
            });
            
        });

    }

    lazyLoadSales(event: TableLazyLoadEvent) {
        this.route.queryParams.subscribe(params => {
            console.log('params', params)
            this.requestType = params['requestType'];
            this.first = event.first || 0;
            this.sortBy = event.sortField ?? '';
            this.sortDirection = event.sortOrder as number;
            this.loadSalesOrders(event.filters as { [s: string]: FilterMetadata } | undefined);
        });


    }
    enumToArray(enumerable: any): SelectItem[] {
        return Object.keys(enumerable)
            .filter(key => !isNaN(Number(enumerable[key])))
            .map(key => ({ label: key, value: enumerable[key] }));
    }
    getStatusString(statusValue: number): string {
        return SalesOrderStatusString[statusValue];
    }
    getProductTypeString(productTypeValue: number): string {
        return ProductType[productTypeValue];
    }
    getPaymentMethodString(payentMethodValue: number): string {
        return PaymentMethod[payentMethodValue];
    }

    loadSalesOrders(filters?: { [s: string]: FilterMetadata } | undefined) {

        this.pageNumber = (this.first / this.pageSize) + 1;
        const sortBy = this.sortBy as string;
        const sortDirection = this.sortDirection;
        filters = filters || {};
        if (this.requestType == "New") {

            this.Status = [SalesOrderStatus.InProgress, SalesOrderStatus.PDFFailed, SalesOrderStatus.PDFGenerated, SalesOrderStatus.EmailSent, SalesOrderStatus.EmailFailed, SalesOrderStatus.ValidationInProgress, SalesOrderStatus.Reprinting, SalesOrderStatus.Invalid];


            this.salesOrderService.getAllSalesOrdersByStatus(this.Status, this.pageSize, this.pageNumber, sortBy, sortDirection, filters).subscribe(response => {

                if (response.body && response.body.objectValue) {
                    this.salesOrders = response.body.objectValue;
                    this.tableData = this.salesOrders;
                    const xPaginationHeader = response.headers.get('x-pagination');
                    if (xPaginationHeader) {
                        const xPagination = JSON.parse(xPaginationHeader);
                        this.totalRecords = xPagination.TotalCount;
                    } else {
                        console.error('x-pagination header not found in the response');
                    }
                    console.log("reponse salesOrders", response.body.objectValue);
                    this.loading = false;
                }
            },
                (error) => {
                    console.log("error", error);

                }
            );
        }
        else if (this.requestType == "Failed") {
            this.Status = [SalesOrderStatus.Invalid, SalesOrderStatus.Cancelled, SalesOrderStatus.Rejected];
            this.salesOrderService.getAllSalesOrdersByStatus(this.Status, this.pageSize, this.pageNumber, sortBy, sortDirection, filters).subscribe(response => {

                if (response.body && response.body.objectValue) {
                    this.failedSalesOrder = response.body.objectValue;
                    this.tableData = this.failedSalesOrder;
                    const xPaginationHeader = response.headers.get('x-pagination');
                    if (xPaginationHeader) {
                        const xPagination = JSON.parse(xPaginationHeader);
                        this.totalRecords = xPagination.TotalCount;
                    } else {
                        console.error('x-pagination header not found in the response');
                    }
                    console.log("reponse salesOrders", response.body.objectValue);
                    this.loading = false;
                    console.log('failedSO', this.failedSalesOrder)
                }
            },
                (error) => {
                    console.log("error", error);

                }
            );

        }
        else if (this.requestType == "Valid") {
            this.Status = [SalesOrderStatus.Valid];
            this.salesOrderService.getAllSalesOrdersByStatus(this.Status, this.pageSize, this.pageNumber, sortBy, sortDirection, filters).subscribe(response => {

                if (response.body && response.body.objectValue) {
                    this.salesOrders = response.body.objectValue;
                    this.tableData = this.salesOrders;
                    const xPaginationHeader = response.headers.get('x-pagination');
                    if (xPaginationHeader) {
                        const xPagination = JSON.parse(xPaginationHeader);
                        this.totalRecords = xPagination.TotalCount;
                    } else {
                        console.error('x-pagination header not found in the response');
                    }
                    console.log("reponse salesOrders", response.body.objectValue);
                    this.loading = false;
                }
            },
                (error) => {
                }
            );
        }

    }

    onSort() {
        //this.updateRowGroupMetaData();
    }

    onGlobalFilter(table: Table, event: Event) {
        table.filterGlobal((event.target as HTMLInputElement).value, 'contains');
    }

    clear(table: Table) {
        table.clear();
        this.filter.nativeElement.value = '';
    }
    showCancelConfirmation(salesOrder: SalesOrder) {
        if (salesOrder.id !== undefined) {
            this.selectedSalesOrderId = salesOrder.id;
            this.displayDeleteDialog = true;
        }
        else {
            console.log('nope');

        }
    }
    async confirmDelete(reason: string) {
        if (this.selectedSalesOrderId !== null) {
            this.salesOrderToDelete = {
                reason: reason,
                salesOrderId: this.selectedSalesOrderId
            }
            await this.salesOrderService.deleteSalesOrder(this.salesOrderToDelete).subscribe(
                (response) => {

                    if (response.statusCode == 201) {
                        this.displayDeleteDialog = false;
                        this.messageService.add({ key: 'toast', severity: 'success', summary: 'SalesOrder deleted successfully', detail: response.exceptionMessage });
                        this.loadSalesOrders();
                    }
                    else {
                        this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Delete SalesOrder', detail: response.exceptionMessage });

                    }
                    this.displayDeleteDialog = false;
                },
                (error) => {
                    this.displayDeleteDialog = false;

                }
            );
        }
    }


    displaySalesOrderDialog(salesOrder: SalesOrder | null) {
        console.log("tiiiicket", this.salesOrders)

        this.selectedSalesOrder = salesOrder;
        this.displayAddEditDialog = true;
        return this.displayAddEditDialog;
    }

    async displayValidateSalesOrderDialog(salesOrder: SalesOrder) {
        console.log("1");

        const confirmed = await this.showConfirmationDialog(salesOrder);

        if (confirmed) {
            console.log("tttt");
            try {
                let result = await this.salesOrderService.validateSalesOrder(salesOrder).toPromise();
                if (result?.statusCode)
                    this.messageService.add({ severity: 'info', summary: 'Confirmed', detail: 'You have validated the transfer request' });
                    this.confirmationService.close();
            } catch (error) {
                console.error('Error validating sales order:', error);
                // Handle error notification or other actions
            }
            this.loadSalesOrders()
        }
    }
    reprintPdf(salesOrder: SalesOrder) {
        this.salesOrderService.reprintPdf(salesOrder).subscribe(
            (response) => {

                if (response.statusCode == 200) {
                    this.displayDeleteDialog = false;
                    this.messageService.add({ key: 'toast', severity: 'success', summary: 'PDF reprinted successfully', detail: response.exceptionMessage });
                    this.loadSalesOrders();
                }
                else {
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error while reprinting PDF', detail: response.exceptionMessage });

                }
            },
            (error) => {
                this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error while reprinting PDF' });
            }
        );
    }
    isReprintPDFDisabled(salesOrder: SalesOrder) {
        return salesOrder.status != SalesOrderStatus.PDFFailed && salesOrder.status != SalesOrderStatus.InProgress;
    }
    resendMail(salesOrder: SalesOrder) {
        this.salesOrderService.resendMail(salesOrder).subscribe(
            (response) => {

                if (response.statusCode == 202) {
                    this.displayDeleteDialog = false;
                    this.messageService.add({ key: 'toast', severity: 'success', summary: 'Mail Resent successfully', detail: response.exceptionMessage });
                    this.loadSalesOrders();
                }
                else {
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error while resending mail', detail: response.exceptionMessage });

                }
            },
            (error) => {
                this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error while resending mail' });
            }
        );
    }
    isResendMailDisabled(salesOrder: SalesOrder) {
        return salesOrder.status != SalesOrderStatus.EmailFailed && salesOrder.status != SalesOrderStatus.PDFGenerated
    }
    showConfirmationDialog(salesOrder: SalesOrder): Promise<boolean> {
        console.log("caled 1");

        return new Promise<boolean>(resolve => {
            this.confirmationService.confirm({
                key: 'displayValidateSalesOrderDialog',
                message: 'Are you sure that you want to validate the transfer request of ' +
                    salesOrder.dynoAmount.toString().bold() + ' Dynos to ' + salesOrder?.company?.name?.toString().bold() + ' ?',
                accept: () => {
                    resolve(true)
                    this.confirmationService.close();
                },
                reject: () => {
                    resolve(false)
                    this.confirmationService.close();
                }
            });
        });
    }
    closeAddEditDialogEvent(e: Event) {
        this.displayAddEditDialog = false;
    }
    onSalesOrderAdded() {
        this.loadSalesOrders();
    }
    onElementDeleted() {

        this.loadSalesOrders();
    }
    onCancelDelete() {
        this.displayDeleteDialog = false;
    }

}
