import { RouterModule } from "@angular/router";
import { NgModule } from '@angular/core';
import { RoleGuard } from "src/app/RoleGuard";
import { BlacklistListComponent } from "./blacklist-list/blacklist-list.component";
@NgModule({
    imports: [RouterModule.forChild([
        {path:'',component: BlacklistListComponent,
        canActivate:[RoleGuard],data:{role:["company", "shopowner","superadmin"]as string[]}}
    ])],
    exports: [RouterModule]
  })
  export class BlacklistuserRoutingModule { }