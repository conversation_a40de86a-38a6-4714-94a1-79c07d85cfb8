import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { Table } from 'primeng/table';
import { ConfirmationService, SelectItem } from 'primeng/api';
import { Status } from 'src/app/shared/enums/status';
import { MessageService } from 'primeng/api';
import { CompanyService } from 'src/app/services/company.service';
import { UserDTO } from 'src/app/shared/models/user/UserDTO';
import jwtDecode from 'jwt-decode';
import { Employee } from 'src/app/shared/models/employee/employee';
import { v4 as uuidv4 } from 'uuid';
import { Gender } from 'src/app/shared/enums/Gender';
import { GroupService } from 'src/app/services/group.service';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Ticket } from 'src/app/shared/models/ticket/ticket';
import { Transaction } from 'src/app/services/transaction.service';

interface expandedRows {
    [key: string]: boolean;
}
@Component({
    selector: 'app-employee-list',
    templateUrl: './employee-list.component.html',
    styleUrls: ['./employee-list.component.scss']
})
export class EmployeeListComponent {
    employees: Employee[] = [];
    Status = Status;
    Gender = Gender;
    displayAddEditDialog: boolean = false;
    displayDeleteDialog: boolean = false;
    companyId: string = "";
    selectedEmployeeId: string = "";
    rowGroupMetadata: any;
    selectedEmployee: Employee | null = null;
    expandedRows: expandedRows = {};
    activityValues: number[] = [0, 100];
    isExpanded: boolean = false;
    idFrozen: boolean = false;
    loading: boolean = true;
    employeeToDelete: Employee | null = null;
    groupNameToDisplay: string = "";
    StatusList = this.enumToArray(Status);
    GenderList = this.enumToArray(Gender);
    @ViewChild('filter') filter!: ElementRef;
    displaySendDialog: boolean = false;

    constructor(private companyService: CompanyService, private groupService: GroupService,
        private confirmationService: ConfirmationService,
        private messageService: MessageService,
        private transactionService : Transaction) { }

    ngOnInit() {
        const token = localStorage.getItem('Token');
        if (token) {
            const decodedToken: any = jwtDecode(token);
            this.companyId = decodedToken.Company;
            this.loadEmployees();
        }
    }

    enumToArray(enumerable: any): SelectItem[] {
        return Object.keys(enumerable)
            .filter(key => !isNaN(Number(enumerable[key])))
            .map(key => ({ label: key, value: enumerable[key] }));
    }
    getStatusString(statusValue: number): string {
        return Status[statusValue];
    }
    getGenderString(statusValue: number): string {
        return Gender[statusValue];
    }

    loadEmployees() {
        this.companyService.getCompanyById(this.companyId).subscribe(
            (response) => {
                if (response.objectValue) {
                    this.employees = response.objectValue.employees;

                    // For each employee, fetch and set the group name
                    this.employees.forEach((employee) => {
                        if (employee.groupId != null)
                            this.getGroupNameById(employee.groupId).subscribe((groupName) => {
                                employee.groupName = groupName;
                            });
                    });

                    this.loading = false;
                }
            },
            (error) => {

            }
        );
    }

    getGroupNameById(id: string): Observable<string> {
        return this.groupService.getGroupById(id).pipe(
            map(response => {
                if (response.statusCode === 200) {
                    return response.objectValue.name;
                } else {
                    // Handle error or return a default value
                    return 'Unknown Group';
                }
            })
        );
    }

    getGroupNameById1(id: string) {
        this.groupService.getGroupById(id).subscribe(response => {
            if (response.statusCode == 200) {
                this.groupNameToDisplay = response.objectValue.name
            }
        });
    }

    onSort() {
        this.updateRowsOrder();
    }


    updateRowsOrder() {
        this.rowGroupMetadata = {};

        if (this.employees) {
            for (let i = 0; i < this.employees.length; i++) {
                const rowData = this.employees[i];
                const representativeName = rowData?.user?.fullName || '';

                if (i === 0) {
                    this.rowGroupMetadata[representativeName] = { index: 0, size: 1 };
                }
                else {
                    const previousRowData = this.employees[i - 1];
                    const previousRowGroup = previousRowData?.user?.fullName;
                    if (representativeName === previousRowGroup) {
                        this.rowGroupMetadata[representativeName].size++;
                    }
                    else {
                        this.rowGroupMetadata[representativeName] = { index: i, size: 1 };
                    }
                }
            }
        }
    }

    expandAll() {
        if (!this.isExpanded) {
            // this.products.forEach(product => product && product.name ? this.expandedRows[product.name] = true : '');

        } else {
            this.expandedRows = {};
        }
        this.isExpanded = !this.isExpanded;
    }

    onGlobalFilter(table: Table, event: Event) {
        table.filterGlobal((event.target as HTMLInputElement).value, 'contains');
    }

    clear(table: Table) {
        table.clear();
        this.filter.nativeElement.value = '';
    }
    showDeleteConfirmation(employee: Employee) {
        if (employee.employeeId !== undefined) {
            this.selectedEmployeeId = employee.employeeId;
            this.displayDeleteDialog = true;
        }
    }
    getEmployeeById(id: string) {

        this.companyService.getEmployeeById(id).subscribe(response => {
            if (response.statusCode == 200) {
                this.employeeToDelete = response.objectValue
            }
        });

    }
    confirmDelete() {
        if (this.selectedEmployeeId !== null) {
            const emplToDelete: Employee = {
                employeeId: this.selectedEmployeeId,
                companyId: this.companyId,
                userId: this.selectedEmployeeId,
                groupName: '',
                groupId: uuidv4(),
                status: Status.Active,

            }
            this.companyService.deleteEmployee(emplToDelete).subscribe(
                (response) => {

                    if (response.statusCode == 200) {
                        this.displayDeleteDialog = false;
                        this.messageService.add({ key: 'toast', severity: 'success', summary: 'Employee deleted successfully', detail: response.exceptionMessage });
                        this.loadEmployees();
                    }
                    else {
                        this.messageService.add({ key: 'toast', severity: 'success', summary: 'Error Delete Employee', detail: response.exceptionMessage });

                    }
                },
                (error) => {
                    this.displayDeleteDialog = false;
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error', detail: 'An unexpected error occurred while calling the backend service.' });

                }
            );

        }
    }

    displayEmployeeDialog(employee: Employee | null) {
        this.selectedEmployee = employee;
        this.displayAddEditDialog = true;
        return this.displayAddEditDialog;
    }
    closeAddEditDialogEvent(e: Event) {
        this.displayAddEditDialog = false;
    }
    onEmployeeAdded() {
        this.loadEmployees();
    }
    onElementDeleted() {
        this.loadEmployees();
    }
    onCancelDelete() {
        this.displayDeleteDialog = false;
    }
    showSendConfirmation(employee: Employee | null) {
        this.selectedEmployee = employee;
        this.displaySendDialog = true;
    }

    isEmployeeActive(employee: Employee | null): boolean {
        return !!employee && employee.status == Status.Active;
    }

    confirmSend(tickets: Ticket[]) {
        this.displaySendDialog = false;
        if (this.selectedEmployee !== null) {
            this.transactionService.sendTicketsToEmployee(this.selectedEmployee.userId, tickets).subscribe(
                (response) => {

                    if (response.statusCode == 200) {

                        this.messageService.add({ key: 'toast', severity: 'success', summary: 'Send tickets successfully', detail: response.exceptionMessage });

                        this.loadEmployees();
                    }
                    else {
                        this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error in sending tickets to employee', detail: response.exceptionMessage });

                    }
                },
                (error) => {

                }
            );
        }
    }

    onCancelSend() {
        this.displaySendDialog = false;
        return this.displaySendDialog;
    }

}


