server {

     listen       80;
     server_name  localhost;

     root   /usr/share/nginx/html;
     index  index.html index.htm;

     error_page   500 502 503 504  /50x.html;

     location / {
          add_header 'Access-Control-Allow-Origin' '*';
          try_files $uri $uri/ /index.html; # Rewrites requests to Angular app
      }

     location ~^/test/ {
         alias /usr/share/nginx/public;
     }

     location = /50x.html {
         root   /usr/share/nginx/html;
     }
}
