import { HttpClient, HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { UserDTO } from "../shared/models/user/UserDTO";
import { ResponseAPI } from "../shared/models/ResponseAPI";
import { environment } from "src/environments/environment";

@Injectable({
    providedIn: 'root'
})
export class CashierService {
    constructor(private http: HttpClient) {
    }

    getAllCashiers(pageSize: number, pageNumber: number, sortBy: string, sortDirection: number) {
        const params = new HttpParams()
            .set('pageSize', pageSize.toString())
            .set('pageNumber', pageNumber.toString())
            .set('sortBy', sortBy.toString())
            .set('sortDirection', sortDirection.toString());
        return this.http.get<ResponseAPI<UserDTO[]>>(`${environment.API}/User/GetCashiers` , { params, observe: 'response', responseType: 'json' });
    }

    AddCashier(){
        return this.http.get<ResponseAPI<UserDTO>>(`${environment.API}/AuthCashier/Add`);
    }

    deleteCashier(id: string) {
        return this.http.delete<ResponseAPI<UserDTO>>(`${environment.API}/AuthCashier/Delete/${id}`);
    }
}
