import { DatePipe } from '@angular/common';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Table } from 'primeng/table';
import { Transaction } from 'src/app/services/transaction.service';
import { WalletService } from 'src/app/services/wallet.service';
import { Gender } from 'src/app/shared/enums/Gender';
import { TransactionDTO } from 'src/app/shared/models/transaction/TransactionDTO';


@Component({
    selector: 'app-list-transaction',
    templateUrl: './list-transaction.component.html',
    styleUrls: ['./list-transaction.component.scss']
})
export class ListTransactionComponent implements OnInit {
    cashiers!: TransactionDTO[];
    loading: boolean = true;
    isExpanded: boolean = false;
    expandedRows: { [key: string]: boolean } = {};
    @ViewChild('filter') filter!: ElementRef;

    constructor(private transactionService: Transaction, private walletService: WalletService, private datePipe: DatePipe
    ) { this.expandedRows = {}; }
    ngOnInit(): void {
        this.expandedRows = {};
        this.loadAllTransaction();
        this.isExpanded=false;
    }

    loadAllTransaction() {
        this.transactionService.getAllTransaction(1, 5).subscribe(response => {
            if (response.objectValue) {
                this.cashiers = response.objectValue;
                this.cashiers.forEach(cashier => {
                    cashier.transactions.forEach(transaction => {
                        transaction.transactionDate = this.datePipe.transform(transaction.transactionDate, 'yyyy-MM-dd HH:mm:ss') ?? "";
                    })
                })
                this.loading = false;
            } else {
                this.loading = false;
            }
        });
    }
    async fetchUserDetailsForTransactions() {
        const userDetailsPromises = this.cashiers.flatMap(cashier =>
            cashier.transactions.map(transaction => this.getUserDetailsForTransaction(transaction))
        );

        await Promise.all(userDetailsPromises);
    }

    getUserDetailsForTransaction(transaction: any): Promise<void> {
        return new Promise((resolve, reject) => {
            this.walletService.GetUserDetailsByWallet(transaction.senderWalletId)
                .subscribe(userDetails => {
                    const user = userDetails.objectValue;

                    console.log('User Object:', user);

                    if (user) {
                        transaction.senderDetails = user;
                        console.log('Sender Details Added:', transaction.senderDetails);
                    }

                    resolve();
                }, error => {
                    console.error('Error fetching user details:', error);
                    reject(error);
                });
        });
    }

    getGenderString(gender: number): string {
        return Gender[gender];
    }

    expandAll() {
        if (!this.isExpanded) {
            this.cashiers.forEach(cashier => cashier && cashier.userId ? this.expandedRows[cashier.userId] = true : '');

        } else {
            this.expandedRows = {};
        }
        this.isExpanded = !this.isExpanded;
    }

    clear(table: Table) {
        table.clear();
        this.filter.nativeElement.value = '';
    }

}
