import { Currency } from "../../enums/Currency";
import { PaymentMethod } from "../../enums/PaymentMethod";
import { ProductType } from "../../enums/ProductType";
import { InvoiceStatus } from "../../enums/invoice-status";
import { Company } from "../company/company";
import { InvoiceDocument } from "../Document/invoiceDocument";
import { ReferentialData } from "../referentialData";

export interface Invoice extends ReferentialData {
    id: string;
    code: string;
    dynoAmount: number;
    currency: Currency;
    netAmount: number;
    vatAmount: number;
    totalAmount: number;
    paymentMethod: PaymentMethod;
    productType: ProductType;
    date: Date;
    status: InvoiceStatus;
    documentId: string;
    document: InvoiceDocument;
    companyId: string;
    company: Company;

}