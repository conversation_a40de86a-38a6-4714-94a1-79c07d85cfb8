// signalr.service.ts
import { Injectable, EventEmitter, InjectionToken } from '@angular/core';
import * as signalR from '@microsoft/signalr';
import { Notification } from '../shared/models/notification/notification';
import { IHttpConnectionOptions } from '@microsoft/signalr';
import { environment } from 'src/environments/environment';

export const SIGNALR_SERVICE = new InjectionToken<SalesOrderStatusNotifService>('SIGNALR_SERVICE');

@Injectable({
  providedIn: 'root',
  useFactory: () => new SalesOrderStatusNotifService(),
  deps: [],
})

export class SalesOrderStatusNotifService {
  private hubConnection!: signalR.HubConnection;
  public notifications: Notification[] = [];
  public notificationReceived = new EventEmitter<Notification>();



  constructor() {
       // Check if SignalR is available
    if (signalR) {
      // Create a new instance of HubConnection
      const accessToken = localStorage.getItem('Token');
      const options: IHttpConnectionOptions = {
        accessTokenFactory: () => {
          return accessToken ? accessToken : "Token is null";
        }
      };

      this.hubConnection = new signalR.HubConnectionBuilder()
        .withUrl(`${environment.NotificationAPI}/SalesOrderStatusNotif`, options)
        .build();

      // Set up event handler for ReceiveNotification
      this.hubConnection.on('ReceiveNotification', (message: Notification) => {
        this.notificationReceived.emit(message);
        this.notifications.push(message);
      });

      // Start the connection
      this.hubConnection.start()
        .then(() => console.log('SignalR connection started'))
        .catch(err => console.error('Error while starting SignalR connection: ', err));
    } else {
      console.error('SignalR library not available.');
    }
  }
}