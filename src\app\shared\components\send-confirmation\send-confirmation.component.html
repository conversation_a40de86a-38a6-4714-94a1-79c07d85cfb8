<p-dialog [(visible)]="display" [modal]="true" (onHide)="onCancel()">
    <form [formGroup]="ticketFormGroup">
        <p-header>
            <h5>Confirm to send ticket</h5>
        </p-header>

        <div>
            <div class="zero-padding col-6 mb-6 lg:col-6 lg:mb-0 ">
                <h5>Tickets <span style="color: red;">*</span></h5>
                <p-multiSelect [options]="selectedGroup?.groupTickets" (onChange)="onTicketValueChange($event)"
                    formControlName="tickets" placeholder="Select tickets" optionLabel="ticket.name"
                    optionValue="ticket" class="multiselect-custom" display="chip" [style]="{ 'width': '100%' }"
                    [showClear]="true">

                </p-multiSelect>
                <p-message severity="error" text="Ticket is required"
                    *ngIf="ticketFormGroup.get('tickets')?.hasError('required') && ticketFormGroup.get('tickets')?.touched">
                </p-message>
            </div>
            <p>Are you sure you want to these ticket to the group <b>{{selectedGroup?.name}}</b>?</p>

            <div class="overflow-x-auto">
                <table class="w-full" style="border-collapse: collapse; table-layout: auto">
                    <thead>
                        <tr>
                            <th class="text-left font-semibold py-3 border-bottom-1 surface-border white-space-nowrap">
                                Name</th>
                            <th
                                class="text-right font-semibold py-3 border-bottom-1 surface-border white-space-nowrap px-3">
                                Quantity</th>
                            <th
                                class="text-right font-semibold py-3 border-bottom-1 surface-border white-space-nowrap px-3">
                                Unit Price</th>
                            <th class="text-right font-semibold py-3 border-bottom-1 surface-border white-space-nowrap">
                                Total Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let ticket of ticketFormGroup.get('tickets')?.value">
                            <td class="text-left py-3 border-bottom-1 surface-border white-space-nowrap">
                                {{ticket.name}}
                            </td>
                            <td class="text-right py-3 border-bottom-1 surface-border px-3">{{ticket.quantity}}</td>
                            <td class="text-right py-3 border-bottom-1 surface-border px-3">{{ticket.amount}}</td>
                            <td class="text-right py-3 border-bottom-1 surface-border">{{ticket.totalAmount}}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>


    </form>
    <p-footer>
        <button pButton type="button" label="Cancel" (click)="onCancel()"></button>
        <button pButton type="button" label="Confirm" (click)="confirmSend(ticketFormGroup.get('tickets')?.value)"
            class="p-button-danger" [disabled]="ticketFormGroup.get('tickets')?.hasError('required')"></button>

    </p-footer>
</p-dialog>