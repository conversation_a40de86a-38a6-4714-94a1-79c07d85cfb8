import { Component, ElementRef, ViewChild } from '@angular/core';
import { response } from 'express';
import jwtDecode from 'jwt-decode';
import { ConfirmationService, FilterMetadata, MessageService, SelectItem } from 'primeng/api';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Observer } from 'rxjs';
import { UserService } from 'src/app/services/User.service';
import { LocalStoreService } from 'src/app/services/local-store.service';
import { Gender } from 'src/app/shared/enums/Gender';
import { Status } from 'src/app/shared/enums/status';
import { ResponseAPI } from 'src/app/shared/models/ResponseAPI';
import { tokenDTO } from 'src/app/shared/models/authentification/token';
import { UserDTO } from 'src/app/shared/models/user/UserDTO';




interface expandedRows {
    [key: string]: boolean;
  }
@Component({
  selector: 'app-user-list',
  templateUrl: './user-list.component.html',
  styleUrls: ['./user-list.component.scss']
})
export class UserListComponent {
    users: UserDTO[] = [];
    displayAddEditDialog: boolean = false;
    displayDeleteDialog: boolean = false;
    displayBlacklistDialog: boolean = false;
    selectedUserId!: string ;
    rowGroupMetadata: any;
    selectedUser: UserDTO | null = null;
    expandedRows: expandedRows = {};
    activityValues: number[] = [0, 100];
    isExpanded: boolean = false;
    idFrozen: boolean = false;
    loading: boolean = true;
    Status = Status;
    StatusList = this.enumToArray(Status).map(item => ({ label: item.label, value: item.label }));
    GenderList = this.enumToArray(Gender);
    pageSize: number = 5;
    pageNumber: number = 0;
    first: number = 0;
    totalRecords: number = 1;
    sortBy?: string | string[] | null | undefined;
    sortDirection: number = 1;
    itsMe: String = '';
    
    @ViewChild('filter') filter!: ElementRef;
    constructor(
        private userService:UserService,
        private confirmationService: ConfirmationService,
        private messageService: MessageService,
        private localStorageService: LocalStoreService) { }

      ngOnInit() {
        // this.loadUsers();
        let token = this.localStorageService.getData("Token");
        if (token != undefined) {
            let decodedToken: tokenDTO = jwtDecode(token);
            this.itsMe = decodedToken.Id;
        }

      }

    lazyLoadUsers(event: TableLazyLoadEvent) {
        this.first = event.first || 0;
        this.sortBy = event.sortField ?? '';
        this.sortDirection = event.sortOrder as number;
        this.loadUsers(event.filters as { [s: string]: FilterMetadata } | undefined);

    }
      async loadUsers(filters?: { [s: string]: FilterMetadata } | undefined) {
        this.pageNumber = (this.first / this.pageSize) + 1
        const sortBy = this.sortBy as string;
        const sortDirection = this.sortDirection;
        filters = filters || {};
        this.userService.getAllPagedUsers(this.pageSize, this.pageNumber, sortBy, sortDirection, filters).subscribe(
          (response) => {
            if (response.body && response.body.objectValue) {
              this.users = response.body.objectValue;
              console.log("TotalCount",response);
              const xPaginationHeader = response.headers.get('x-pagination');
              if (xPaginationHeader) {
                  const xPagination = JSON.parse(xPaginationHeader);


                  this.totalRecords = xPagination.TotalCount;
              } else {
                  console.error('x-pagination header not found in the response');
              }
              this.loading = false;
            }
          },
          (error) => {
              // Handle error
              this.loading = false;
          }

        );
    }

      onSort() {

      }
      enumToArray(enumerable: any): SelectItem[] {
        return Object.keys(enumerable)
            .filter(key => !isNaN(Number(enumerable[key])))
            .map(key => ({ label: key, value: enumerable[key] }));
    }

      expandAll() {
        if (!this.isExpanded) {


        } else {
          this.expandedRows = {};
        }
        this.isExpanded = !this.isExpanded;
      }

    onGlobalFilter(table: Table, event: Event) {
        table.filterGlobal((event.target as HTMLInputElement).value, 'contains');
      }

      clear(table: Table) {
        table.clear();
        this.filter.nativeElement.value = '';
      }

      showDeleteConfirmation(id: any) {

        if (id !== undefined) {
          this.selectedUserId = id;
          this.displayDeleteDialog = true;
        }
        else{
            console.log('nope');

        }
      }

      showBlacklistConfirmation(id: any) {

        if (id !== undefined) {
          this.selectedUserId = id;
          this.displayBlacklistDialog = true;
        }
        else{
            console.log('nope');

        }
      }

    confirmDelete() {
      debugger
        if (this.selectedUserId !== null) {
        this.userService.hardDeleteUser(this.selectedUserId).subscribe({
            next: (response) => {
                if (response.statusCode === 200) {
                    this.displayDeleteDialog = false;
                    this.onElementDeleted()
                    this.messageService.add({ key: 'toast', severity: 'success', summary: 'User deleted successfully', detail: response.exceptionMessage });
                } else {
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Delete User', detail: response.exceptionMessage });
                }
            },
            error: (err) => {
                this.displayDeleteDialog = false;
                this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Delete User', detail: err });
            }
        });
              }
              else {
                this.displayDeleteDialog = false;
                this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Delete User', detail: "response.exceptionMessage" });

              }
    }

    confirmBlacklist() {
      if (this.selectedUserId !== null) {
      this.userService.hardDeleteUser(this.selectedUserId).subscribe({
          next: (response) => {
              if (response.statusCode === 200) {
                  this.displayDeleteDialog = false;
                  this.onElementDeleted()
                  this.messageService.add({ key: 'toast', severity: 'success', summary: 'User deleted successfully', detail: response.exceptionMessage });
              } else {
                  this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Delete User', detail: response.exceptionMessage });
              }
          },
          error: (err) => {
              this.displayDeleteDialog = false;
              this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Delete User', detail: err });
          }
      });
            }
            else {
              this.displayDeleteDialog = false;
              this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Delete User', detail: "response.exceptionMessage" });

            }
  }

      getGenderString(gender: number): string {
        return Gender[gender];
      }

      displayUserDialog(user:UserDTO | null) {
        this.selectedUser = user;
        this.displayAddEditDialog = true;
        return this.displayAddEditDialog;
      }

      closeAddEditDialogEvent(e: Event) {
        this.displayAddEditDialog = false;
      }

      onUserAdded() {
        this.loadUsers();
      }

      async onElementDeleted() {
        await this.loadUsers();
      }

      async onElementBlacklist() {
        await this.loadUsers();
      }

      onCancelDelete(){
        this.displayDeleteDialog=false;
      }

      onCancelBlacklist(){
        this.displayBlacklistDialog = false;
      }

      getStatusString(statusValue: number): string {
        return Status[statusValue];
      }
}
