# Script simple pour corriger toutes les erreurs outlined="true"

Write-Host "Correction des erreurs outlined en cours..." -ForegroundColor Green

$filesFixed = 0

# Trouver tous les fichiers HTML
$htmlFiles = Get-ChildItem -Path "src" -Filter "*.component.html" -Recurse

foreach ($file in $htmlFiles) {
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    # Corriger outlined="true" vers [outlined]="true"
    $content = $content -replace 'outlined="true"', '[outlined]="true"'
    $content = $content -replace 'outlined="false"', '[outlined]="false"'
    
    # Corriger disabled="true" vers [disabled]="true"
    $content = $content -replace 'disabled="true"', '[disabled]="true"'
    $content = $content -replace 'disabled="false"', '[disabled]="false"'
    
    # Corriger loading="true" vers [loading]="true"
    $content = $content -replace 'loading="true"', '[loading]="true"'
    $content = $content -replace 'loading="false"', '[loading]="false"'
    
    if ($content -ne $originalContent) {
        Set-Content $file.FullName $content -NoNewline
        $filesFixed++
        Write-Host "Corrige: $($file.Name)" -ForegroundColor Yellow
    }
}

Write-Host "Termine! $filesFixed fichiers corriges." -ForegroundColor Green
Write-Host "Vous pouvez maintenant lancer: npm start" -ForegroundColor Cyan
