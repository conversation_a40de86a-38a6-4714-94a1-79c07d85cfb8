<p-dialog [header]="mode === 'add' ? 'Add new role' : 'Edit Role'" [(visible)]="display" [modal]="true"
    showEffect="fade" [style]="{width: '60vw'}" [breakpoints]="{'960px': '75vw'}" (onHide)="closeAddDialog()">
    <form [formGroup]="roleFormGroup">
        <div class="col-12">
            <div class="card">
                <div class="row">
                    <div class="col-5 mb-5 lg:col-5 lg:mb-0 input-name">
                        <h5>Name <span style="color: red;">*</span></h5>
                        <input type="text" class="input-width" pInputText placeholder="Name" formControlName="name">
                        <div *ngIf="roleFormGroup.get('name')?.hasError('required') && roleFormGroup.get('name')?.touched"
                            class="error-message">
                            Name is required.
                        </div>
                    </div>
                    <div class="col-4 mb-4 lg:col-4 lg:mb-0 dd-status">
                        <h5>User Type</h5>
                        <p-dropdown [options]="UserTypeDropdown" optionLabel="label" optionValue="value"
                            formControlName="userType" class="input-width"></p-dropdown>
                    </div>
                    <div class="col-3 mb-3 lg:col-3 lg:mb-0 dd-status">
                        <h5>Status</h5>
                        <p-dropdown [options]="dropdownItems" optionLabel="status" formControlName="status"
                            class="input-width"></p-dropdown>
                    </div>


                </div>


                <h5>Permissions <span style="color: red;">*</span></h5>
                <p-tree [value]="permissions" selectionMode="checkbox" [(selection)]="selectedPermissions"
                    (onNodeSelect)="nodeSelect($event)" (onNodeUnselect)="NodeUnselect($event)" [filter]="true"
                    filterMode="strict" filterPlaceholder="permission name">
                    <ng-template pTemplate="emptymessage">
                        <tr>
                            <td colspan="6">There are no permissions for this role yet.</td>
                        </tr>
                    </ng-template>
                </p-tree>
                <!-- <div *ngIf="roleFormGroup.get('permissions')?.errors?.['permissionsRequired'] && roleFormGroup.get('permissions')?.touched" class="error-message">
                    Permissions are required.
                </div> -->
                <div *ngIf="!this.selectedPermissions || this.selectedPermissions.length === 0" class="error-message">
                    At least select one permission. Permissions are required.
                </div>



            </div>
        </div>
    </form>
    <ng-template pTemplate="footer" class="footer">
        <button pButton (click)="closeAddDialog()" label="Cancel" class="p-button-outlined "></button>
        <button pButton (click)="saveRole()"  class="p-button-outlined p-button-success"
            [disabled]="loading">
            <span *ngIf="!loading"

            >Save</span>
           <p-progressSpinner *ngIf="loading"  styleClass="w-3rem h-1rem" strokeWidth="6"></p-progressSpinner>
        </button>      
        
            <p-toast key="toast"></p-toast>

    </ng-template>
</p-dialog>
