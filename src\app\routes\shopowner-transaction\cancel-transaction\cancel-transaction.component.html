<div class="grid">
    <div class="col-12">
        <div class="card">
            <p-table #dt [value]="refunds" responsiveLayout="scroll"
                [globalFilterFields]="['name','country.name','representative.name','status']" [paginator]="true"
                dataKey="id" selectionMode="single" [resizableColumns]="true" [rows]="10" [loading]="loading"
                [rowHover]="true" styleClass="p-datatable-gridlines" responsiveLayout="scroll">
                <ng-template pTemplate="caption">
                    <div class="flex flex-column md:flex-row md:justify-content-between md:align-items-center">
                        <h5 class="m-0">Manage refunds</h5>
                        <span class="block mt-2 md:mt-0 p-input-icon-left">
                            <i class="pi pi-search"></i>
                            <input pInputText type="text" (input)="onGlobalFilter(dt, $event)" placeholder="Search..."
                                class="w-full sm:w-auto" />
                        </span>
                    </div>
                </ng-template>
                <ng-template pTemplate="header">
                    <tr>
                        <th pSortableColumn="casherName">Cashier Name <p-sortIcon field="casherName"></p-sortIcon></th>
                        <th pSortableColumn="amount">Amount <p-sortIcon field="amount"></p-sortIcon></th>
                        <th pSortableColumn="transactionDate"> Transaction Date <p-sortIcon
                                field="transactionDate"></p-sortIcon>
                        </th>
                        <th pSortableColumn="refundStatus">Refund Status <p-sortIcon field="refundStatus"></p-sortIcon>
                        </th>
                        <th>Actions</th>

                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-refund>
                    <tr *ngFor="let transaction of refund.transactions">

                        <td style="width:14%; min-width:10rem;">
                            {{refund.fullName}}
                        </td>
                        <td style="width:14%; min-width:8rem;">
                            {{transaction.amount}}
                        </td>
                        <td style="width:14%; min-width:10rem;">
                            {{transaction.transactionDate | date:'yyyy-MM-dd HH:mm:ss'}}
                        </td>
                        <td style="width:14%; min-width:10rem;">
                            <span [class]="'component-badge status-' + getStatusString(transaction.refundStatus)">
                                {{getStatusString(transaction.refundStatus)}}
                            </span>

                        </td>

                        <td>

                          
                            <p-button pRipple type="button" icon="pi pi-check"  outlined="true"
                            [pTooltip]="'Accept Refund'" [style]="{'margin-right.px': '10'}" styleClass="p-button-outlined "
                                [ngClass]="{ 'Hide': transaction.refundStatus === 0 || transaction.refundStatus === 1 }"
                                (click)="acceptrefund(transaction.id)"></p-button>
                            <p-button pRipple type="button" icon="pi pi-times" outlined="true"
                            [pTooltip]="'Cancel Refund'" [style]="{'margin-right.px': '10'}" styleClass="p-button-outlined "
                                [ngClass]="{ 'Hide': transaction.refundStatus === 0 || transaction.refundStatus === 1 }"
                                (click)="deleterefund(transaction.id)"></p-button>
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="8">No refund request found.</td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="loadingbody">
                    <tr>
                        <td colspan="8">Loading refund request data. Please wait.</td>
                    </tr>
                </ng-template>
            </p-table>
        </div>
    </div>
</div>
<p-dialog [(visible)]="deleterefundDialog" header="Confirm" [modal]="true" [style]="{width:'450px'}">
    <div class="flex align-items-center justify-content-center">
        <i class="pi pi-exclamation-triangle mr-3" style="font-size: 2rem"></i>
        <span>Are you sure you want to Cancel this transaction?</span>
    </div>
    <ng-template pTemplate="footer">

        <p-button  pRipple icon="pi pi-times"  label="No" outlined="true"  [pTooltip]="'Cancel Refund'"
            (click)="deleterefundDialog = false"  [style]="{'margin-right.px': '10'}"
            styleClass="p-button-outlined "><span class="tooltip"></span></p-button>

        <p-button pRipple icon="pi pi-check"  label="Yes" outlined="true"  [pTooltip]="'Cancel Refund'"
           (click)="confirmDelete()" [style]="{'margin-right.px': '10'}"
           styleClass="p-button-outlined "><span class="tooltip"></span></p-button>
    </ng-template>
</p-dialog>
<p-dialog [(visible)]="acceptrefundDialog" header="Confirm" [modal]="true" [style]="{width:'450px'}"
    [closable]="!submitted">
    <div class="flex align-items-center justify-content-center">
        <i *ngIf="submitted" class="pi pi-spin pi-spinner mr-3" style="font-size: 2rem"></i>
        <i *ngIf="!submitted" class="pi pi-exclamation-triangle mr-3" style="font-size: 2rem"></i>
        <span>Are you sure you want to Accept refund ?</span>
    </div>
    <ng-template pTemplate="footer">
        <p-button  pRipple icon="pi pi-times" [disabled]="submitted" label="No" outlined="true"  [pTooltip]="'Cancel Refund'"
            (click)="acceptrefundDialog = false"  [style]="{'margin-right.px': '10'}"
            styleClass="p-button-outlined "><span class="tooltip"></span></p-button>>
        <p-button  pRipple icon="pi pi-check" [disabled]="submitted" label="Yes" outlined="true"  [pTooltip]="'Accept Refund'"
            (click)="confirmAccept()"  [style]="{'margin-right.px': '10'}"
            styleClass="p-button-outlined "><span class="tooltip"></span></p-button>
    </ng-template>
</p-dialog>