.confirmation-dialog .p-dialog-content {
    padding: 0; 
    border-radius: 12px; 
    overflow: hidden; 
}

.circle-container {
    position: absolute;
    top: -3rem; 
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
}

.circle-icon {
    position: relative;
    width: 8rem;
    height: 8rem;
    background-color: #007bff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.circle-icon i {
    color: white; 
}

.content {
    padding: 1rem;
    text-align: center;
}

.cancel-button,
.confirm-button {
    margin: 0 0.5rem;
    width: 8rem;
    text-align: center;
}

.confirm-button {
    color: white;
    background-color: #007bff;
    border-color: #007bff;
    min-height: 42px;
}

.loader {
    margin-left: 25px; /* Adjust margin as needed */
    font-size: 1rem; /* Adjust size as needed */
    color: white; /* Color of the loader */
}

.loading .confirm-button {
    pointer-events: none; /* Disable button when loading */
    opacity: 0.6; /* Reduce opacity of button when loading */
}