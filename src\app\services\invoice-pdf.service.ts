import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ResponseAPI } from '../shared/models/ResponseAPI';
import { environment } from 'src/environments/environment';
import { InvoiceDocument } from 'src/app/shared/models/Document/invoiceDocument';
@Injectable({
  providedIn: 'root'
})
export class InvoicePdfService {

  constructor(private http: HttpClient) { }

  getInvoicePDF(id: string) {
    return this.http.get<ResponseAPI<InvoiceDocument>>(`${environment.API}/InvoicePDF/Get/${id}`);
  }
}
