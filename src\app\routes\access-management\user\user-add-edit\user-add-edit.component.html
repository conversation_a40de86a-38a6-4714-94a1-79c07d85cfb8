<p-dialog [header]="mode === 'add' ? 'Add New User' : 'Edit User'" [(visible)]="display" [modal]="true"
    showEffect="fade" [style]="{ width: '60vw' }" [breakpoints]="{ '960px': '75vw' }" (onHide)="closeAddDialog()">
    <form [formGroup]="UserFormGroup">
        <div class="col-12">
            <div class="card">
                <div class="row">
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 input-name">
                        <h5>Full Name <span style="color: red;">*</span></h5>
                        <input type="text" class="input-width" pInputText placeholder="User"
                            formControlName="FullName" />
                        <p-message severity="error" text="Full Name is required"
                            *ngIf="UserFormGroup.get('FullName')?.hasError('required') && UserFormGroup.get('FullName')?.touched">
                        </p-message>
                    </div>

                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 input-email">
                        <h5>Gender <span style="color: red;">*</span></h5>
                        <p-dropdown [options]="GenderTypeDropdown" (onChange)="onGenderTypeChange($event)"
                            optionLabel="label" optionValue="value" formControlName="gendertype" class="input-width">
                        </p-dropdown>
                    </div>
                </div>

                <div class="row">
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 input-phonenumber">
                        <h5>Email<span style="color: red;">*</span></h5>
                        <input type="email" class="input-width" pInputText placeholder="<EMAIL>"
                            formControlName="email" />
                        <p-message severity="error" text="Email is required"
                            *ngIf="UserFormGroup.get('email')?.hasError('required') && UserFormGroup.get('email')?.touched">
                        </p-message>
                    </div>
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 input-phonenumber">
                        <h5>Phone <span style="color: red;">*</span></h5>
                        <div class="flex items-center">
                            <div class="country-code-dropdown">
                                <p-dropdown [options]="countryCodes" (onChange)="onCountryCodeChange($event)"
                                    formControlName="countryCode" [showClear]="true" [filter]="true" placeholder="Code"
                                    appendTo="body">
                                    <ng-template let-option pTemplate="selectedItem">
                                        <img [src]="option.flagUrl" alt="Flag" class="flag-icon">
                                        {{ option.label }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        <img [src]="option.flagUrl" alt="Flag" class="flag-icon">
                                        {{ option.label }}
                                    </ng-template>
                                </p-dropdown>
                            </div>

                            <!-- Phone Number Input -->
                            <input type="text" class="input-width" pInputText placeholder="Phone"
                                formControlName="phonenumber">
                        </div>
                        <p-message severity="error" text="Phone number is required"
                            *ngIf="UserFormGroup.get('phonenumber')?.hasError('required') && UserFormGroup.get('phonenumber')?.touched">
                        </p-message>
                        <p-message severity="error" text="Phone number must be in correct form"
                            *ngIf="UserFormGroup.get('phonenumber')?.hasError('invalidPhoneNumber') && UserFormGroup.get('phonenumber')?.touched">
                        </p-message>
                        <p-message severity="error" text="Phone number must contain non-digit characters"
                            *ngIf="UserFormGroup.get('phonenumber')?.hasError('invalidPhoneNumber') && isNotANumber(UserFormGroup.get('phonenumber')?.value)">
                        </p-message>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 dd-birthDate">
                        <h5>Date Of Birth (+18)<span style="color: red;">*</span></h5>
                        <p-calendar [minDate]="minDate" [maxDate]="maxDate" [readonlyInput]="true"
                            formControlName="datebirth" appendTo="body" [dateFormat]="'dd-mm-yy'"
                            (onShow)="setDefaultDate($event)"></p-calendar>
                        <p-message severity="error" text="Date of birth is required"
                            *ngIf="UserFormGroup.get('datebirth')?.hasError('required') && UserFormGroup.get('datebirth')?.touched">
                        </p-message>
                    </div>
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 dd-status">
                        <h5>Status <span style="color: red;">*</span></h5>
                        <p-dropdown [options]="statusDropdown" (onChange)="onStatusChange($event)" optionLabel="label"
                            optionValue="value" formControlName="status"></p-dropdown>
                    </div>
                </div>
                <div class="row">
                    <div *ngIf="isUserSuperAdmin" class="col-6 mb-6 lg:col-6 lg:mb-0 input-email">
                        <h5>User Type <span style="color: red;">*</span></h5>
                        <p-dropdown [options]="UserTypeDropdown" (onChange)="onUserTypeChange($event)"
                            optionLabel="label" optionValue="value" formControlName="userType" class="input-width">
                        </p-dropdown>
                    </div>

                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 dd-role">
                        <h5>Role <span style="color: red;">*</span></h5>
                        <p-multiSelect [options]="rolesByUserType" formControlName="roles" placeholder="Select a Role"
                            optionLabel="name" optionValue="id" class="multiselect-custom" display="chip"
                            [style]="{ 'width': '100%' }" scrollHeight="70px" >
                        </p-multiSelect>
                        <p-message severity="error" text="Role is required"
                            *ngIf="UserFormGroup.get('roles')?.hasError('required') && UserFormGroup.get('roles')?.touched">
                        </p-message>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0" [style]="{ 'height': '200px' }" *ngIf="isUserSuperAdmin">
                        <h5>Enterprise Name <span style="color: red;">*</span> </h5>
                        <div class="input-width">
                                <p-dropdown 
                                    [options]="companySuggestions" 
                                    formControlName="companyName"
                                    optionLabel="name" 
                                    optionValue="id"
                                    [filter]="true" 
                                    filterBy="label"                                    
                                    placeholder="Search for a company"
                                    (onChange)="onCompanySelect($event)">

                                    <ng-template pTemplate="filter" 
                                        let-options="options">
                                        <div class="flex gap-1" >
                                            <div class="p-inputgroup" (click)="$event.stopPropagation()">
                                                <span class="p-inputgroup-addon"><i class="pi pi-search"></i></span>
                                                <input type="text" pInputText placeholder="Filter" (keyup)="customFilterFunction($event, options)" />
                                            </div>
                                            <button pButton icon="pi pi-times" (click)="resetFunction(options)" severity="secondary"></button>
                                        </div>
                                    </ng-template>
                            </p-dropdown>
                        </div>
                        <p-message severity="error" text="Company is required"
                            *ngIf="UserFormGroup.get('companyName')?.hasError('required') && UserFormGroup.get('companyName')?.touched">
                        </p-message>
                        <p-message severity="error" text="Company is not in the suggestions"
                                    *ngIf="UserFormGroup.get('companyName')?.hasError('invalidPhoneNumber') && !companySuggestions.includes(UserFormGroup.get('companyName')?.value)">
                                </p-message>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <ng-template pTemplate="footer" class="footer">
        <button pButton (click)="closeAddDialog()" label="Cancel" class="p-button-outlined"></button>

        <button pButton (click)="saveUser()" class="p-button-outlined p-button-success" [disabled]="loading">
            <span *ngIf="!loading">Save</span>
            <p-progressSpinner *ngIf="loading" styleClass="w-3rem h-1rem" strokeWidth="6"></p-progressSpinner>
        </button>
        <p-toast key="toast"></p-toast>
    </ng-template>
</p-dialog>