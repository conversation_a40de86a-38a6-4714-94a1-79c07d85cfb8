import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ResponseAPI } from '../shared/models/ResponseAPI';
import { environment } from 'src/environments/environment';
import { UserDTO } from '../shared/models/user/UserDTO';
import { SalesOrder } from '../shared/models/salesOrder/salesOrder';
import { WalletType } from '../shared/enums/wallet-type';
import { SalesOrderStatus } from '../shared/enums/SalesOrderStatus';
import { FailedSalesOrder } from '../shared/models/salesOrder/FailedSalesOrder';
import { FilterMetadata } from 'primeng/api';
import { PagedParameters } from '../shared/models/PagedParameters';
import { SalesOrderStatusRequest } from '../shared/models/SalesOrderStatusRequest';

@Injectable({
    providedIn: 'root'
})
export class SalesOrderService {
    requestBody?: PagedParameters;
    salesOrderParams?: SalesOrderStatusRequest;
    constructor(private http: HttpClient) {

    }
    getAllSalesOrders(pageSize: number, pageNumber: number, sortBy: string, sortDirection: number) {
        // const params = new HttpParams().set('PageSize', pageSize.toString());
        // let simplifiedFilters: { [s: string]: string } = {};
        // if (filters) {
        //     for (const field in filters) {
        //         if (Object.prototype.hasOwnProperty.call(filters, field)) {
        //             const filterValues = filters[field];

        //             if (filterValues !== undefined && Array.isArray(filterValues) && filterValues.length > 0) {
        //                 const filterValue = filterValues[0]?.value;

        //                 if (filterValue !== undefined && filterValue !== null) {
        //                     simplifiedFilters[field] = filterValue.toString();
        //                 }
        //             } else {
        //                 const filterValue = filterValues?.value;

        //                 if (filterValue !== undefined && filterValue !== null) {
        //                     simplifiedFilters[field] = filterValue.toString();
        //                 }
        //             }
        //         }
        //     }
        // }

        const requestBody = {
            pageSize,
            pageNumber,
            sortBy: sortBy.toString(),
            sortDirection: sortDirection.toString(),
             filters: null,
        };

        return this.http.post<ResponseAPI<SalesOrder[]>>(`${environment.API}/SalesOrder/GetAllPaged`, requestBody );
    }

    getSalesOrdersByType(type: WalletType) {
        return this.http.get<ResponseAPI<SalesOrder[]>>(`${environment.API}/SalesOrder/GetSalesOrdersByType/${type}`);
    }
    // getAllSalesOrdersByStatus(status: SalesOrderStatus[], pageSize: number, pageNumber: number, sortBy: string, sortDirection: number, filters: { [s: string]: FilterMetadata } | undefined) {
    //     let simplifiedFilters: { [s: string]: string } = {};
    //     if (filters) {
    //         for (const field in filters) {
    //             if (Object.prototype.hasOwnProperty.call(filters, field)) {
    //                 const filterValues = filters[field];

    //                 if (filterValues !== undefined && Array.isArray(filterValues) && filterValues.length > 0) {
    //                     const filterValue = filterValues[0]?.value;

    //                     if (filterValue !== undefined && filterValue !== null) {
    //                         simplifiedFilters[field] = filterValue.toString();
    //                     }
    //                 } else {
    //                     const filterValue = filterValues?.value;

    //                     if (filterValue !== undefined && filterValue !== null) {
    //                         simplifiedFilters[field] = filterValue.toString();
    //                     }
    //                 }
    //             }
    //         }
    //     }

    //     const requestBody = {
    //         salesOrderStatus: status,
    //         pagedParameters: {
    //             pageNumber: pageNumber,
    //             pageSize: pageSize,
    //             sortBy: sortBy,
    //             sortDirection: sortDirection,
    //             filters: simplifiedFilters,
    //           }
    //     };
    //     return this.http.post<ResponseAPI<SalesOrder[]>>(`${environment.API}/SalesOrder/GetAllByStatus`,requestBody,{ observe: 'response', responseType: 'json' });
    // }
    getAllSalesOrdersByStatus(status: SalesOrderStatus[], pageSize?: number, pageNumber?: number, sortBy?: string, sortDirection?: number, filters?: { [s: string]: FilterMetadata } | undefined) {
        let simplifiedFilters: { [s: string]: string } = {};
        if (filters) {
            for (const field in filters) {
                if (Object.prototype.hasOwnProperty.call(filters, field)) {
                    const filterValues = filters[field];

                    if (filterValues !== undefined && Array.isArray(filterValues) && filterValues.length > 0) {
                        const filterValue = filterValues[0]?.value;

                        if (filterValue !== undefined && filterValue !== null) {
                            simplifiedFilters[field] = filterValue.toString();
                        }
                    } else {
                        const filterValue = filterValues?.value;

                        if (filterValue !== undefined && filterValue !== null) {
                            simplifiedFilters[field] = filterValue.toString();
                        }
                    }
                }
            }
        }

        this.requestBody = {
            PageSize: pageSize,
            PageNumber: pageNumber,
            SortBy: sortBy?.toString(),
            SortDirection: sortDirection?.toString(),
            Filters: simplifiedFilters,
        }
        this.salesOrderParams = {
            salesOrderStatus: status,
            pagedParameters: this.requestBody
        }

        return this.http.post<ResponseAPI<SalesOrder[]>>(`${environment.API}/SalesOrder/GetAllByStatus`, this.salesOrderParams, { observe: 'response', responseType: 'json' });
    }
    getAllFailedSalesOrders() {
        return this.http.get<ResponseAPI<FailedSalesOrder[]>>(`${environment.API}/FailedSalesOrder/GetAll`);
    }
    getSalesOrderById(id: string) {
        return this.http.get<ResponseAPI<SalesOrder>>(`${environment.API}/SalesOrder/Get/${id}`);
    }

    getAllPerPeriod() {

        return this.http.get<ResponseAPI<SalesOrder[]>>(`${environment.API}/SalesOrder/GetAllPerPeriod`);
    }

    addSalesOrder(salesOrder: SalesOrder) {
        return this.http.post<ResponseAPI<SalesOrder>>(`${environment.API}/SalesOrder/Create`, salesOrder);
    }
    validateSalesOrder(salesOrder: SalesOrder) {
        return this.http.put<ResponseAPI<SalesOrder>>(`${environment.API}/SalesOrder/ValidateTransfertRequest?salesOrderId=` + salesOrder.id, salesOrder);
    }

    updateSalesOrder(salesOrder: SalesOrder) {
        return this.http.put<ResponseAPI<SalesOrder>>(`${environment.API}/SalesOrder/Update`, salesOrder);
    }

    deleteSalesOrder(failedSalesOrder: FailedSalesOrder) {
        return this.http.post<ResponseAPI<FailedSalesOrder>>(`${environment.API}/FailedSalesOrder/Create`, failedSalesOrder);
    }
    reprintPdf(salesOrder: SalesOrder) {
        return this.http.get<ResponseAPI<SalesOrder>>(`${environment.API}/SalesOrder/ReprintPDF/${salesOrder.id}`);
    }
    resendMail(salesOrder: SalesOrder) {
        return this.http.get<ResponseAPI<SalesOrder>>(`${environment.API}/SalesOrder/ResendMail/${salesOrder.id}`);
    }
}
