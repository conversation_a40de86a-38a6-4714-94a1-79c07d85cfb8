<p-dialog header="Add Employee" [(visible)]="display" [modal]="true" showEffect="fade" [style]="{width: '60vw'}"
    [breakpoints]="{'960px': '75vw'}" (onHide)="closeAddDialog()">
    <form [formGroup]="associationFormGroup">
        <div class="col-12">
            <div class="card">
                <div class="row">
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 input-phonenumber ">       
                        <h5>Phone <span style="color: red;">*</span></h5>
                        <div class="flex items-center">
                            <div class="country-code-dropdown">
                                <p-dropdown [options]="countryCodes" (onChange)="onCountryCodeChange($event)"
                                    formControlName="countryCode" [showClear]="true" [filter]="true" placeholder="Code"
                                    appendTo="body" [disabled]="mode == 'edit'">
                                    <ng-template let-option pTemplate="selectedItem">
                                        <img [src]="option.flagUrl" alt="Flag" class="flag-icon">
                                        {{ option.label }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        <img [src]="option.flagUrl" alt="Flag" class="flag-icon">
                                        {{ option.label }}
                                    </ng-template>
                                </p-dropdown>
                            </div>
                            <p-autoComplete [suggestions]="userSuggestions" placeholder="Search for a phone number"
                                formControlName="phonenumber" (completeMethod)="searchUsers($event)" [minLength]="1"
                                class="input-width" (onSelect)="onUserSelect($event)" [disabled]="mode == 'edit'"
                                (keyup)="onPhoneNumberChange($event)" [style]="{'width': '100%'}">
                                <ng-template let-item pTemplate="item">
                                    {{ item }}
                                </ng-template>
                            </p-autoComplete>


                            <!-- Error messages for phone number -->
                            <div class="error-messages">
                                <p-message severity="error" text="Phone number is required"
                                    *ngIf="associationFormGroup.get('phonenumber')?.hasError('required') && associationFormGroup.get('phonenumber')?.touched">
                                </p-message>

                                <p-message severity="error" text="Phone number must be in correct form"
                                    *ngIf="associationFormGroup.get('phonenumber')?.hasError('invalidPhoneNumber') && associationFormGroup.get('phonenumber')?.touched && !associationFormGroup.get('phonenumber')?.errors?.['invalidPhoneNumber']">
                                </p-message>

                                <p-message severity="error" text="Phone number is not in the suggestions"
                                    *ngIf="associationFormGroup.get('phonenumber')?.hasError('invalidPhoneNumber') && !userSuggestions.includes(associationFormGroup.get('phonenumber')?.value)">
                                </p-message>
                                <p-message severity="error" text="Phone number must contain non-digit characters"
                                    *ngIf="associationFormGroup.get('phonenumber')?.hasError('invalidPhoneNumber') && isNotANumber(associationFormGroup.get('phonenumber')?.value)">
                                </p-message>
                                </div>
                        </div>
                    
                    </div>
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 dd-group">
                        <h5>Group</h5>
                        <p-dropdown formControlName="group" [options]="groupSuggestions" optionLabel="label"
                            placeholder="Search for a group" class="input-width" (onChange)="onGroupSelect($event)">
                        </p-dropdown>
                        <p-message *ngIf="associationFormGroup.controls['group'].errors?.['required'] &&
                                          associationFormGroup.controls['group'].touched" severity="error"
                            text="Group is required">
                        </p-message>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 dd-status ">
                        <h5>Status</h5>
                        <p-dropdown [options]="statusDropdown" (onChange)="onStatusChange($event)" optionLabel="label"
                            optionValue="value" formControlName="status"></p-dropdown>
                    </div>

                </div>
            </div>
        </div>
    </form>
    <ng-template pTemplate="footer" class="footer">
        <button pButton (click)="closeAddDialog()" label="Cancel" class="p-button-outlined "></button>
        <button pButton (click)="Associate()"  class="p-button-outlined p-button-success"
            [disabled]="loading">
            <span *ngIf="!loading"

            >Save</span>
           <p-progressSpinner *ngIf="loading"  styleClass="w-3rem h-1rem" strokeWidth="6"></p-progressSpinner>
        </button>
        <p-toast key="toast"></p-toast>
    </ng-template>
</p-dialog>
