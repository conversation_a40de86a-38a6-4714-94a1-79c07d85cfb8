import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from 'src/app/shared/Modules/shared-module/shared-module.module';
import { FormsModule } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { ToggleButtonModule } from 'primeng/togglebutton';
import { RippleModule } from 'primeng/ripple';
import { MultiSelectModule } from 'primeng/multiselect';
import { DropdownModule } from 'primeng/dropdown';
import { ProgressBarModule } from 'primeng/progressbar';
import { ToastModule } from 'primeng/toast';
import { SliderModule } from 'primeng/slider';
import { RatingModule } from 'primeng/rating';
import { ConfirmationDialogueModule } from 'src/app/shared/components/confirmation-dialog/confirmation-dialogue.module';
import { ReactiveFormsModule } from '@angular/forms';
import { DialogModule } from 'primeng/dialog';
import { MessageModule } from 'primeng/message';;
import { InputSwitchModule } from 'primeng/inputswitch';
import { CalendarModule } from "primeng/calendar";
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { ConfirmationService, MessageService } from 'primeng/api';
import { CashierListComponent } from './cashier-list/cashier-list.component';
import { CashierManagementRoutingModule } from './cashier-management-routing.module';
import { TooltipModule } from 'primeng/tooltip';


@NgModule({
    declarations: [
        CashierListComponent
  ],
    imports: [
        CommonModule,
        SharedModule,
        FormsModule,
        TableModule,
        RatingModule,
        ButtonModule,
        SliderModule,
        InputTextModule,
        ToggleButtonModule,
        RippleModule,
        MultiSelectModule,
        DropdownModule,
        ProgressBarModule,
        ToastModule,
        ConfirmationDialogueModule,
        ReactiveFormsModule,
        DialogModule,
        CommonModule,
        ReactiveFormsModule,
        TableModule,
        RatingModule,
        ButtonModule,
        SliderModule,
        InputTextModule,
        ToggleButtonModule,
        RippleModule,
        MultiSelectModule,
        DropdownModule,
        ProgressBarModule,
        DialogModule,
        MessageModule,
        InputSwitchModule,
        CalendarModule,
        OverlayPanelModule,
        ConfirmDialogModule,
        ConfirmPopupModule,
        CashierManagementRoutingModule,
        TooltipModule
    ],
    providers: [ConfirmationService, MessageService]
})
export class CashierManagementModule { }
