import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { ResponseAPI } from "../shared/models/ResponseAPI";
import { BlackListUser } from "../shared/models/user/BlackListUserDTO";
import { environment } from "src/environments/environment";

@Injectable({
    providedIn: 'root'
})
export class UserService {
    constructor(private http: HttpClient) {

    }
    getAllPagedUsers(pageSize: number, pageNumber: number, sortBy: string, sortDirection: number, filters: { [s: string]: FilterMetadata } | undefined) : Observable<HttpResponse<ResponseAPI<UserDTO[]>>>  {
        let simplifiedFilters: { [s: string]: string } = {};
        if (filters) {
            for (const field in filters) {
                if (Object.prototype.hasOwnProperty.call(filters, field)) {
                    const filterValues = filters[field];

                    if (filterValues !== undefined && Array.isArray(filterValues) && filterValues.length > 0) {
                        const filterValue = filterValues[0]?.value;

                        if (filterValue !== undefined && filterValue !== null) {
                            simplifiedFilters[field] = filterValue.toString();
                        }
                    } else {
                        const filterValue = filterValues?.value;

                        if (filterValue !== undefined && filterValue !== null) {
                            simplifiedFilters[field] = filterValue.toString();
                        }
                    }
                }
            }
        }

        const requestBody = {
            pageSize,
            pageNumber,
            sortBy: sortBy.toString(),
            sortDirection: sortDirection.toString(),
            filters: simplifiedFilters,
        };

        return this.http.post<ResponseAPI<BlackListUser[]>>(`${environment.API}/User/GetAllPagedBlacklistUsers` ,requestBody, {  observe: 'response', responseType: 'json' });
    }
}