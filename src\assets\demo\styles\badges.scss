.product-badge {
    border-radius: var(--border-radius);
    padding: .25em .5rem;
    text-transform: uppercase;
    font-weight: 700;
    font-size: 12px;
    letter-spacing: .3px;

    &.status-instock {
        background: var(--green-200);
        color: var(--green-800);
    }

    &.status-outofstock {
        background: var(--pink-200);
        color: var(--pink-800);
    }

    &.status-lowstock {
        background: var(--yellow-200);
        color: var(--yellow-800);
    }
}

.product-badge-text {
    font-weight: 700;
    font-size: 12px;
    letter-spacing: .3px;

    &.status-instock {
        color: var(--green-500);
    }

    &.status-outofstock {
        color: var(--pink-500);
    }

    &.status-lowstock {
        color: var(--yellow-500);
    }
}

.customer-badge {
    border-radius: var(--border-radius);
    padding: .25em .5rem;
    text-transform: uppercase;
    font-weight: 700;
    font-size: 12px;
    letter-spacing: .3px;

    &.status-qualified {
        background: var(--green-200);
        color: var(--green-800);
    }

    &.status-unqualified {
        background: var(--pink-200);
        color: var(--pink-800);
    }

    &.status-negotiation {
        background: var(--yellow-200);
        color: var(--yellow-800);
    }

    &.status-new {
        background: var(--blue-200);
        color: var(--blue-800);
    }

    &.status-renewal {
        background: var(--purple-200);
        color: var(--purple-800);
    }

    &.status-proposal {
        background: var(--orange-200);
        color: var(--orange-800);
    }
}

.order-badge {
    border-radius: var(--border-radius);
    padding: .25em .5rem;
    text-transform: uppercase;
    font-weight: 700;
    font-size: 12px;
    letter-spacing: .3px;

    &.order-delivered {
        background: #C8E6C9;
        color: #256029;
    }

    &.order-cancelled {
        background: #FFCDD2;
        color: #C63737;
    }

    &.order-pending {
        background: #FEEDAF;
        color: #8A5340;
    }

    &.order-returned {
        background: #ECCFFF;
        color: #694382;
    }
}

.component-badge {
    border-radius: var(--border-radius);
    padding: .25em .5rem;
    text-transform: uppercase;
    font-weight: 700;
    font-size: 12px;
    letter-spacing: .3px;

    &.status-Active {
        background: var(--green-200);
        color: var(--green-800);
    }

    &.status-Deleted {
        background: var(--pink-200);
        color: var(--pink-800);
    }

    &.status-Inactive {
        background: var(--yellow-200);
        color: var(--yellow-800);
    }

    &.status-Updated {
        background: #f2f21b;
        color: #0a0a0a;
    }

    &.status-BlackListed {
        background: #5f5e6c;
        color: #0a0a0a;
    }

    &.status-Default {
        background: var(--pink-200);
        color: var(--pink-800);
    }

    &.status-Expired {
        background: var(--purple-200);
        color: var(--purple-800);
    }

    &.status-Valid {
        background: var(--green-200);
        color: var(--green-800);
    }
    &.status-Validated {
        background: var(--green-200);
        color: var(--green-800);
    }

    &.status-Failed {
        background: var(--orange-200);
        color: var(--orange-800);
    }

    &.status-InProgress {
        background: var(--yellow-200);
        color: var(--yellow-800);
    }

    &.status-Rejected {
        background: var(--red-200);
        color: var(--red-800);
    }

    &.status-Cancelled {
        background: var(--orange-200);
        color: var(--orange-800);
    }

    &.status-Invalid {
        background: var(--grey-200);
        color: var(--grey-800);
    }
    &.status-TransactionFailed {
        background: var(--red-200);
        color: var(--red-800);
    }
    &.status-TransactionSucceeded {
        background: var(--green-200);
        color: var(--green-800);
    }
    &.status-EmailFailed {
        background: var(--red-200);
        color: var(--red-800);
    }
    &.status-EmailSent {
        background: var(--green-200);
        color: var(--green-800);
    }
    &.status-ValidationInProgress {
        background: var(--yellow-200);
        color: var(--yellow-800);
    }
    &.status-PDFFailed {
        background: var(--red-200);
        color: var(--red-800);
    }
    &.status-PDFGenerated {
        background: var(--green-200);
        color: var(--green-800);
    }
    &.status-Reprinting {
        background: var(--orange-200);
        color: var(--orange-800);
    }
    


}


.component-ErrorType {
    border-radius: var(--border-radius);
    padding: .25em .5rem;
    text-transform: uppercase;
    font-weight: 700;
    font-size: 12px;
    letter-spacing: .3px;

    &.type-DataBase {
        background: var(--green-200);
        color: var(--green-800);
    }

    &.type-Connection {
        background: var(--pink-200);
        color: var(--pink-800);
    }

    &.type-Emailing {
        background: var(--yellow-200);
        color: var(--yellow-800);
    }

    &.type-PDF {
        background: #f2f21b;
        color: #0a0a0a;
    }
}


.component-LogLevel {
    border-radius: var(--border-radius);
    padding: .25em .5rem;
    text-transform: uppercase;
    font-weight: 700;
    font-size: 12px;
    letter-spacing: .3px;

    &.level-Information {
        background: var(--green-200);
        color: var(--green-800);
    }

    &.level-Warning {
        background: var(--orange-200);
        color: var(--orange-800);
    }

    &.level-Error {
        background: var(--red-200);
        color: var(--red-800);
    }
}

.component-MicroserviceType {
    border-radius: var(--border-radius);
    padding: .25em .5rem;
    text-transform: uppercase;
    font-weight: 700;
    font-size: 12px;
    letter-spacing: .3px;

    &.microservice-AccessManagement {
        background: var(--green-200);
        color: var(--green-800);
    }

    &.microservice-Payment {
        background: var(--yellow-200);
        color: var(--yellow-800);
    }

    &.microservice-Notification {
        background: var(--pink-200);
        color: var(--pink-800);
    }
}


.component-gender {
    border-radius: var(--border-radius);
    padding: .25em .5rem;
    text-transform: uppercase;
    font-weight: 700;
    font-size: 12px;
    letter-spacing: .3px;

    &.gender-Male {
        background: var(--green-200);
        color: var(--green-800);
    }

    &.gender-Female {
        background: var(--pink-200);
        color: var(--pink-800);
    }

    &.gender-Other {
        background: var(--yellow-200);
        color: var(--yellow-800);
    }
}

.entreprise-badge {
    border-radius: var(--border-radius);
    padding: .25em .5rem;
    text-transform: uppercase;
    font-weight: 700;
    font-size: 12px;
    letter-spacing: .3px;

    &.entreprise-Enterprise {
        background: #8e88e1;
        color: #0a0a0a;
    }

    &.entreprise-Shop {
        background: #cb97b2;
        color: #0a0a0a;
    }
}

.service-badge {
    border-radius: var(--border-radius);
    padding: .25em .5rem;
    text-transform: uppercase;
    font-weight: 700;
    font-size: 12px;
    letter-spacing: .3px;

    &.service-Ticket {
        background: #f2e2ea;
        color: #0a0a0a;
    }

    &.service-Insurance {
        background: #d3a3f3;
        color: #0a0a0a;
    }

    &.service-Transport {
        background: #9673f6;
        color: #0a0a0a;
    }

    &.service-Teambuilding {
        background: #a834f5;
        color: #0a0a0a;
    }
}

.category-badge {
    border-radius: var(--border-radius);
    padding: .25em .5rem;
    text-transform: uppercase;
    font-weight: 700;
    font-size: 12px;
    letter-spacing: .3px;

    &.category-Restaurant {
        background: #99f3f7;
        color: #0a0a0a;
    }

    &.category-Market  {
        background: #e78181;
        color: #0a0a0a;
    }

    &.category-Gyms  {
        background: rgb(146, 246, 115);
        color: #0a0a0a;
    }

    &.category-Transportation  {
        background: #a834f5;
        color: #0a0a0a;
    }

    &.category-Healthcare  {
        background: #a834f5;
        color: #0a0a0a;
    }
    &.category-Beauty  {
        background: #a834f5;
        color: #0a0a0a;
    }
    &.category-Education  {
        background: #a834f5;
        color: #0a0a0a;
    }

    &.category-Entertainment  {
        background: #a834f5;
        color: #0a0a0a;
    }
}

.entreprise {
    &.-icon-Entreprise {
        background-image: url(../../entreprise-icon-Entreprise.png)
    }

    .entreprise.-icon-Entreprise::before {
        content: "";
        /* This is necessary to generate a box for the icon */
        display: inline-block;
        /* Make sure it's an inline block element */
        width: 16px;
        /* Set the width of the icon */
        height: 16px;
        /* Set the height of the icon */
        background-image: url(../../entreprise-icon-Entreprise.png);
        /* Set the icon image */
        background-size: cover;
        /* Adjust background size if needed */
        margin-right: 35px;
        /* Add spacing between the icon and the text */
    }

    // &.-icon-Shop {
    //     background: #a834f5;
    //     color: #0a0a0a;
    // }
}
