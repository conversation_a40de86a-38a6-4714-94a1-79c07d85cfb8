#!/bin/bash

# Script de test manuel automatisé pour l'application Angular Dyno
# Ce script lance l'environnement Docker et exécute une série de tests manuels

set -e

echo "🚀 Démarrage des tests manuels pour l'application Angular Dyno"
echo "================================================================"

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages colorés
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier que Docker est installé et en cours d'exécution
check_docker() {
    print_status "Vérification de Docker..."
    if ! command -v docker &> /dev/null; then
        print_error "Docker n'est pas installé. Veuillez installer Docker pour continuer."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker n'est pas en cours d'exécution. Veuillez démarrer Docker."
        exit 1
    fi
    
    print_success "Docker est disponible et en cours d'exécution"
}

# Nettoyer l'environnement précédent
cleanup_environment() {
    print_status "Nettoyage de l'environnement précédent..."
    docker-compose down -v --remove-orphans 2>/dev/null || true
    print_success "Environnement nettoyé"
}

# Construire les images Docker
build_images() {
    print_status "Construction des images Docker..."
    docker-compose build --no-cache
    print_success "Images Docker construites avec succès"
}

# Démarrer les services de test
start_test_services() {
    print_status "Démarrage des services de test..."
    
    # Démarrer les services de base (DB, Redis, Mock API)
    docker-compose --profile testing up -d postgres-test redis mock-api
    
    # Attendre que les services soient prêts
    print_status "Attente que les services soient prêts..."
    sleep 10
    
    # Vérifier que les services sont en cours d'exécution
    if docker-compose ps | grep -q "Up"; then
        print_success "Services de test démarrés avec succès"
    else
        print_error "Échec du démarrage des services de test"
        exit 1
    fi
}

# Démarrer l'application en mode développement
start_dev_app() {
    print_status "Démarrage de l'application en mode développement..."
    docker-compose up -d angular-dev
    
    # Attendre que l'application soit prête
    print_status "Attente que l'application soit prête (cela peut prendre quelques minutes)..."
    
    # Attendre que le port 4200 soit accessible
    timeout=300  # 5 minutes
    counter=0
    while ! curl -s http://localhost:4200 > /dev/null; do
        if [ $counter -ge $timeout ]; then
            print_error "Timeout: L'application n'a pas démarré dans les temps"
            exit 1
        fi
        sleep 5
        counter=$((counter + 5))
        echo -n "."
    done
    
    echo ""
    print_success "Application démarrée et accessible sur http://localhost:4200"
}

# Exécuter les tests automatisés
run_automated_tests() {
    print_status "Exécution des tests automatisés..."
    
    # Tests unitaires
    print_status "Exécution des tests unitaires..."
    docker-compose --profile testing run --rm angular-test npm run test:ci
    
    if [ $? -eq 0 ]; then
        print_success "Tests unitaires réussis"
    else
        print_warning "Certains tests unitaires ont échoué"
    fi
}

# Vérifier les endpoints de l'API mock
test_mock_api() {
    print_status "Test des endpoints de l'API mock..."
    
    # Test de l'endpoint de login
    response=$(curl -s -w "%{http_code}" -X POST http://localhost:1080/Api/Auth/login \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>","password":"password"}')
    
    if [[ $response == *"200" ]]; then
        print_success "Endpoint de login fonctionne"
    else
        print_warning "Problème avec l'endpoint de login"
    fi
    
    # Test de l'endpoint des entreprises
    response=$(curl -s -w "%{http_code}" -X POST http://localhost:1080/Api/Company/GetAllPaged \
        -H "Content-Type: application/json" \
        -d '{"pageSize":10,"pageNumber":1}')
    
    if [[ $response == *"200" ]]; then
        print_success "Endpoint des entreprises fonctionne"
    else
        print_warning "Problème avec l'endpoint des entreprises"
    fi
}

# Afficher les instructions pour les tests manuels
show_manual_test_instructions() {
    echo ""
    echo "🧪 INSTRUCTIONS POUR LES TESTS MANUELS"
    echo "======================================"
    echo ""
    echo "L'application est maintenant accessible sur: ${BLUE}http://localhost:4200${NC}"
    echo "API Mock disponible sur: ${BLUE}http://localhost:1080${NC}"
    echo ""
    echo "📋 CHECKLIST DES TESTS MANUELS:"
    echo ""
    echo "✅ Phase 1 - Corrections Prioritaires:"
    echo "   1. Tester la connexion utilisateur"
    echo "   2. Vérifier la gestion des erreurs (essayer une connexion invalide)"
    echo "   3. Tester le refresh automatique du token"
    echo "   4. Vérifier les messages d'erreur conviviaux"
    echo ""
    echo "✅ Phase 2 - Optimisations:"
    echo "   5. Tester la pagination sur la liste des entreprises"
    echo "   6. Vérifier le tri et les filtres"
    echo "   7. Tester les actions CRUD (créer, modifier, supprimer)"
    echo "   8. Vérifier la gestion d'état (navigation entre pages)"
    echo ""
    echo "✅ Phase 3 - Fonctionnalités Avancées:"
    echo "   9. Accéder aux logs d'audit (/dashboard/audit-logs)"
    echo "   10. Tester les filtres avancés des logs"
    echo "   11. Vérifier l'export des logs"
    echo "   12. Tester les notifications (si implémentées)"
    echo "   13. Vérifier la gestion des rôles (si accessible)"
    echo ""
    echo "🔍 POINTS D'ATTENTION:"
    echo "   - Performance de chargement des pages"
    echo "   - Réactivité de l'interface"
    echo "   - Messages d'erreur clairs"
    echo "   - Cohérence visuelle"
    echo "   - Fonctionnement sur différentes tailles d'écran"
    echo ""
    echo "📊 MONITORING:"
    echo "   - Logs de l'application: docker-compose logs angular-dev"
    echo "   - Logs de l'API mock: docker-compose logs mock-api"
    echo "   - État des services: docker-compose ps"
    echo ""
    echo "⏹️  ARRÊTER LES TESTS:"
    echo "   Exécutez: ${YELLOW}docker-compose down${NC}"
    echo ""
}

# Fonction principale
main() {
    echo "Début des tests manuels à $(date)"
    
    check_docker
    cleanup_environment
    build_images
    start_test_services
    test_mock_api
    start_dev_app
    
    # Optionnel: exécuter les tests automatisés
    read -p "Voulez-vous exécuter les tests automatisés maintenant? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        run_automated_tests
    fi
    
    show_manual_test_instructions
    
    echo ""
    print_success "Environnement de test prêt! Vous pouvez maintenant effectuer vos tests manuels."
    echo ""
    echo "Appuyez sur Ctrl+C pour arrêter les services quand vous avez terminé."
    
    # Garder le script en vie pour maintenir les services
    trap 'echo ""; print_status "Arrêt des services..."; docker-compose down; exit 0' INT
    
    # Afficher les logs en temps réel
    docker-compose logs -f angular-dev
}

# Exécuter la fonction principale
main "$@"
