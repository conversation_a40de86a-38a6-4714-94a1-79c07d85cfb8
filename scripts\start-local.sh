#!/bin/bash

# Script pour démarrer l'application Angular en local
# Plus rapide que Docker pour les tests de développement

set -e

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Paramètres
CLEAN=false
MOCK_API=false
SKIP_INSTALL=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --clean)
            CLEAN=true
            shift
            ;;
        --mock-api)
            MOCK_API=true
            shift
            ;;
        --skip-install)
            SKIP_INSTALL=true
            shift
            ;;
        *)
            echo "Usage: $0 [--clean] [--mock-api] [--skip-install]"
            exit 1
            ;;
    esac
done

echo -e "${CYAN}🚀 Démarrage de l'application Angular en local${NC}"
echo "============================================="

# Vérifier que Node.js est installé
if ! command -v node &> /dev/null; then
    print_error "Node.js n'est pas installé. Veuillez installer Node.js pour continuer."
    exit 1
fi

NODE_VERSION=$(node --version)
print_status "Version Node.js: $NODE_VERSION"

# Vérifier que npm est installé
if ! command -v npm &> /dev/null; then
    print_error "npm n'est pas installé. Veuillez installer npm pour continuer."
    exit 1
fi

NPM_VERSION=$(npm --version)
print_status "Version npm: $NPM_VERSION"

# Nettoyage si demandé
if [ "$CLEAN" = true ]; then
    print_status "Nettoyage des dépendances..."
    
    if [ -d "node_modules" ]; then
        rm -rf node_modules
        print_success "node_modules supprimé"
    fi
    
    if [ -f "package-lock.json" ]; then
        rm -f package-lock.json
        print_success "package-lock.json supprimé"
    fi
    
    # Nettoyer le cache npm
    npm cache clean --force
    print_success "Cache npm nettoyé"
fi

# Installation des dépendances
if [ "$SKIP_INSTALL" = false ]; then
    print_status "Installation des dépendances..."
    
    if [ ! -d "node_modules" ]; then
        print_status "Installation initiale des dépendances (cela peut prendre quelques minutes)..."
        npm install --legacy-peer-deps
        
        if [ $? -ne 0 ]; then
            print_error "Échec de l'installation des dépendances"
            print_status "Tentative avec cache nettoyé..."
            npm cache clean --force
            npm install --legacy-peer-deps --no-optional
            
            if [ $? -ne 0 ]; then
                print_error "Impossible d'installer les dépendances. Vérifiez votre connexion internet."
                exit 1
            fi
        fi
        
        print_success "Dépendances installées avec succès"
    else
        print_status "Dépendances déjà installées, vérification..."
        npm ls --depth=0 &> /dev/null
        if [ $? -ne 0 ]; then
            print_warning "Problème détecté avec les dépendances, réinstallation..."
            npm install --legacy-peer-deps
        fi
        print_success "Dépendances vérifiées"
    fi
fi

# Démarrer l'API mock si demandé
if [ "$MOCK_API" = true ]; then
    print_status "Démarrage de l'API mock..."
    
    # Vérifier si json-server est installé
    if ! command -v json-server &> /dev/null; then
        print_status "Installation de json-server..."
        npm install -g json-server
    fi
    
    # Démarrer json-server en arrière-plan
    json-server --watch mock-data.json --port 3001 &
    JSON_SERVER_PID=$!
    print_success "API mock démarrée sur http://localhost:3001 (PID: $JSON_SERVER_PID)"
    
    # Attendre que l'API soit prête
    timeout=30
    counter=0
    while ! curl -s http://localhost:3001/companies > /dev/null; do
        if [ $counter -ge $timeout ]; then
            print_warning "L'API mock n'a pas démarré dans les temps"
            break
        fi
        sleep 1
        counter=$((counter + 1))
    done
    
    if [ $counter -lt $timeout ]; then
        print_success "API mock prête et accessible"
    fi
fi

# Vérifier la compilation TypeScript
print_status "Vérification de la compilation TypeScript..."
npx tsc --noEmit

if [ $? -ne 0 ]; then
    print_warning "Erreurs TypeScript détectées, mais l'application peut quand même démarrer"
else
    print_success "Aucune erreur TypeScript détectée"
fi

# Afficher les informations de démarrage
echo ""
echo -e "🌐 L'application sera accessible sur: ${BLUE}http://localhost:4200${NC}"

if [ "$MOCK_API" = true ]; then
    echo -e "🔧 API Mock accessible sur: ${BLUE}http://localhost:3001${NC}"
fi

echo ""
echo -e "${YELLOW}📋 CHECKLIST DES TESTS MANUELS:${NC}"
echo ""
echo -e "${GREEN}✅ Phase 1 - Corrections Prioritaires:${NC}"
echo "   1. Tester la connexion utilisateur"
echo "   2. Vérifier la gestion des erreurs"
echo "   3. Tester le refresh automatique du token"
echo "   4. Vérifier les messages d'erreur conviviaux"
echo ""
echo -e "${GREEN}✅ Phase 2 - Optimisations:${NC}"
echo "   5. Tester la pagination sur la liste des entreprises"
echo "   6. Vérifier le tri et les filtres"
echo "   7. Tester les actions CRUD"
echo "   8. Vérifier la gestion d'état"
echo ""
echo -e "${GREEN}✅ Phase 3 - Fonctionnalités Avancées:${NC}"
echo "   9. Accéder aux logs d'audit"
echo "   10. Tester les filtres avancés"
echo "   11. Vérifier les notifications"
echo "   12. Tester la gestion des rôles"
echo ""
echo -e "${RED}⏹️  ARRÊTER L'APPLICATION:${NC}"
echo "   Appuyez sur Ctrl+C dans le terminal"
echo ""

# Fonction de nettoyage à l'arrêt
cleanup() {
    echo ""
    print_status "Arrêt de l'application..."
    if [ "$MOCK_API" = true ] && [ ! -z "$JSON_SERVER_PID" ]; then
        kill $JSON_SERVER_PID 2>/dev/null || true
        print_success "API mock arrêtée"
    fi
    exit 0
}

# Capturer Ctrl+C
trap cleanup INT

# Démarrer ng serve
print_status "Démarrage de l'application Angular..."
npm start
