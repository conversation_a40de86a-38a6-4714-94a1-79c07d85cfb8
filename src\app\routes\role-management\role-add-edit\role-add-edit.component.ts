import { AfterViewInit, ChangeDetectorRef, Component, EventEmitter, Input, NgZone, OnChanges, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, FormBuilder, FormControl, FormGroup, RequiredValidator, UntypedFormBuilder, ValidatorFn, Validators } from '@angular/forms';
import { TreeNode } from 'primeng/api/treenode';
import { RoleManagementService } from 'src/app/services/role-management.service';
import { Status } from 'src/app/shared/enums/status';
import { Permission, PermissionRequest } from 'src/app/shared/models/role/permission';
import { ReactiveFormsModule } from '@angular/forms';
import { Role, RoleRequest } from 'src/app/shared/models/role/role';
import { ActivatedRoute } from '@angular/router';
import { EnumToStringPipe } from 'src/app/shared/pipes/enum-to-string';
import { TreeNodeSelectEvent } from 'primeng/tree';
import { MessageService, SelectItem } from 'primeng/api';
import { LocalStoreService } from 'src/app/services/local-store.service';
import { tokenDTO } from 'src/app/shared/models/authentification/token';
import jwtDecode from 'jwt-decode';
import { UserType } from 'src/app/shared/enums/UserType';

export function permissionsSelectedValidator(): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } | null => {
      const permissions = control.value;
      if (!permissions || permissions.length === 0) {
        return { 'permissionsRequired': true }; // Validation failed
      }
      return null; // Validation passed
    };
  }
@Component({
    selector: 'app-role-add-edit',
    templateUrl: './role-add-edit.component.html',
    styleUrls: ['./role-add-edit.component.scss']
})
export class RoleAddEditComponent implements OnChanges {
    @Output() closeAddDialogEvent = new EventEmitter();
    @Output() UpdateOrAddConfirmedDialogEvent = new EventEmitter();
    @Input() roleId: string = "";
    @Input() display: boolean = false;
    isvalid: boolean = true;
    dropdownItems = [
        { status: Status[0], code: 0 },
        { status: Status[1], code: 1 }
    ];
    selectedStatus: Status = 0;
    permissions: TreeNode<any> | TreeNode<any>[] | any[] | any;
    selectedPermissions: TreeNode<any> | TreeNode<any>[] | any[] | any;
    roleFormGroup: FormGroup;
    mode: string = "";
    displayUpdateConfirmationDialog: boolean = false;
    availablePermissions: TreeNode<any>[] = [];
    isUserSuperAdmin = false;
    userType?: UserType;
    UserTypeDropdown: SelectItem[] = [];
    public isDisabledSave = false;
    public loading = false;
    constructor(private roleService: RoleManagementService, private formBuilder: FormBuilder, private route: ActivatedRoute,
        private messageService: MessageService, private localStorageService: LocalStoreService, private cdr: ChangeDetectorRef, private zone: NgZone) {
        this.roleFormGroup = formBuilder.group({
            name: ["", Validators.required],
            userType: [Validators.required],
            status: [Status.Active],
            permissions: ["", [Validators.required, permissionsSelectedValidator()]]
        })
    }


    ngOnChanges(): void {
        if (this.roleId != "" && this.roleId != null) {
            this.mode = 'edit';
            this.getRolebyId(this.roleId);
        } else {
            this.roleFormGroup.reset();
            this.getAvailablePermissionsByUser();
            this.mode = 'add';
        }
    }
    getAvailablePermissionsByUser() {
        this.roleService.getAvailablePermissionsByUser().subscribe(PermissionResponse => {
            let permissions = PermissionResponse.objectValue;
            if (permissions.length > 0) {
                this.permissions = this.convertAvailablePermissions(permissions);
                this.availablePermissions = this.permissions.slice(0);
            }
        });
    }

    ngOnInit() {
        let token = this.localStorageService.getData("Token");
        if (token != undefined) {
            let decodedToken: tokenDTO = jwtDecode(token);

            let userType = decodedToken.UserType;
            console.log("decodedToken", userType, UserType[UserType.SuperAdmin].toString().toLowerCase());
            if (userType == UserType[UserType.SuperAdmin].toString()) {
                this.isUserSuperAdmin = true;
                this.userType = UserType.SuperAdmin
            }
            if (userType == UserType[UserType.Company].toString()) {
                this.isUserSuperAdmin = false;
                this.userType = UserType.Company
            }
            if (userType == UserType[UserType.ShopOwner].toString()) {
                this.isUserSuperAdmin = false;
                this.userType = UserType.ShopOwner
            }
            this.getAvailableUserTypes();
        }
        this.getAvailablePermissionsByUser();
    }
    getAvailableUserTypes() {
        if (this.userType == UserType.SuperAdmin) {
            this.UserTypeDropdown = [
                {
                    label: UserType[UserType.Company].toString(),
                    value: UserType.Company
                },
                {
                    label: UserType[UserType.SuperAdmin].toString(),
                    value: UserType.SuperAdmin
                },
                {
                    label: UserType[UserType.ShopOwner].toString(),
                    value: UserType.ShopOwner
                }
            ]
        }
        if (this.userType == UserType.Company) {
            this.UserTypeDropdown = [
                {
                    label: UserType[UserType.Company].toString(),
                    value: UserType.Company
                }
            ]
        }
        if (this.userType == UserType.ShopOwner) {
            this.UserTypeDropdown = [
                {
                    label: UserType[UserType.ShopOwner].toString(),
                    value: UserType.ShopOwner
                }
            ]
        }
    }
    getRolebyId(id: string) {
        this.roleService.getRoleById(id).subscribe(roleResponse => {
            let role = roleResponse.objectValue;
            debugger
            if (role) {
                this.roleFormGroup.setValue({
                    name: role?.name,
                    userType: role.userType,
                    status: { status: Status[role.status], code: role?.status },
                    permissions : new FormControl([])
                });
                if (role?.permissions) {
                    this.permissions = this.convertPermissions(role?.permissions);
                    this.selectedPermissions = []
                    this.getSelectedPermissionsRecursive(this.permissions);
                }
            }
        });
    }

    convertPermissions(data: Permission[]): TreeNode[] {
        return data.map(item => ({
            label: item.name,
            data: { "id": item.id, "name": item.name },
            partialSelected: item.partialSelected,
            selectable: this.isSelectable(item),
            //expanded: item.permissions.length > 0,
            children: item.permissions.length > 0 ? this.convertPermissions(item.permissions) : []
        }));
    }
    isSelectable(item: Permission): boolean {
        let isSelectable: boolean | null = null;
        console.log("availablableper", this.availablePermissions)
        this.availablePermissions.forEach((availablePermission: TreeNode) => {
            if (availablePermission.label == item.name) {
                availablePermission.selectable == null ? isSelectable = false : isSelectable = availablePermission.selectable;
                console.log("**************************11111")
            }
            if (availablePermission.children != undefined && availablePermission.children.length > 0) {
                let partialAvailablePermissions = availablePermission.children;
                partialAvailablePermissions.forEach((partialAvailablePermission: TreeNode) => {
                    if (partialAvailablePermission.label == item.name) {
                        partialAvailablePermission.selectable == null ? isSelectable = false : isSelectable = partialAvailablePermission.selectable;
                        console.log("**************************22222")
                    }

                    if (partialAvailablePermission.children != undefined && partialAvailablePermission.children.length > 0) {
                        let leafAvailablePermissions = partialAvailablePermission.children;
                        leafAvailablePermissions.forEach((leafAvailablePermission: TreeNode) => {
                            if (leafAvailablePermission.label == item.name) {
                                leafAvailablePermission.selectable == null ? isSelectable = false : isSelectable = leafAvailablePermission.selectable;
                                console.log("**************************3333")
                            }
                        });
                    }
                });
            }

        });
        console.log("++++++++ isselectable", isSelectable == null ? false : isSelectable)
        return isSelectable == null ? false : isSelectable;

    }
    convertAvailablePermissions(data: Permission[]): TreeNode[] {
        this.roleId = ''
        return data.map(item => ({
            label: item.name,
            data: { "id": item.id, "name": item.name },
            partialSelected: item.partialSelected,
            selectable: item.isSelectable == true ? true : false,
            //expanded: item.permissions.length > 0,
            children: item.permissions.length > 0 ? this.convertAvailablePermissions(item.permissions) : []
        }));
    }
    saveRole() {
                // Mark all form controls as touched
        Object.values(this.roleFormGroup.controls).forEach(control => {
            control.markAsTouched();
        });
        if (this.isSaveDisabled()) {
            this.loading = false;
            return;
        }

        this.loading = true;
        if (this.mode == "edit") {
            this.displayUpdateConfirmationDialog = true;
        }
        else if (this.mode == "add") {
            this.CreateRole()
        }
    }

    CreateRole() {
        this.isDisabledSave = true;
        this.loading = true;
        let permissionsList = []
        try {
            permissionsList = this.getSelectedPermissionsList(this.selectedPermissions)

        }
        catch {
            this.messageService.add({ key: 'toast', severity: 'error', summary: 'no permission is selected' });
            console.log("iii")
            return;
        }

        let permissions: PermissionRequest[] = []
        if (permissionsList.length > 0) {
            permissionsList.forEach(permission => {
                permissions.push({
                    "id": permission.id,
                    "name": permission.name
                })
            });
        }
        let role: RoleRequest = {
            name: this.roleFormGroup.get('name')?.value,
            userType: this.roleFormGroup.get('userType')?.value,
            status: this.roleFormGroup.get('status')?.value.code,
            permissions: permissions
        };
        console.log("status", this.roleFormGroup.get('status')?.value.code)
        this.roleService.addRole(role).subscribe( {
            next : (value: any) => {
                this.messageService.add({ key: 'toast', severity: 'success', summary: 'Role added successfully', detail: value.exceptionMessage });
                this.UpdateOrAddConfirmedDialogEvent.emit(true);
                this.loading = false;
            },
            error: error => {
                this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error occured while adding role', detail: error.error.exceptionMessage });
                this.loading = false;
                () => {
                    console.warn('warning ........');
                }


            },
            complete: () => {
                this.roleFormGroup.reset();
                this.selectedPermissions = []
                this.roleId = ""
                this.closeAddDialogEvent.emit(false);
                this.isDisabledSave = false;
            }
        });
        // this.roleFormGroup.reset();
        // this.selectedPermissions = []
        // this.roleId = ""
        // this.closeAddDialogEvent.emit(false);
        // this.isDisabledSave = false;

    }
    updateRole() {
        this.loading = true;
        if (this.roleId != null) {
            let permissionsList = this.getSelectedPermissionsList(this.selectedPermissions)
            let permissions: PermissionRequest[] = []
            if (permissionsList.length > 0) {
                permissionsList.forEach(permission => {
                    permissions.push({
                        "id": permission.id,
                        "name": permission.name
                    })
                });
            }
            let role: RoleRequest = {
                id: this.roleId,
                name: this.roleFormGroup.get('name')?.value,
                userType: this.roleFormGroup.get('userType')?.value,
                status: this.roleFormGroup.get('status')?.value.code,
                permissions: permissions
            };
            this.roleService.updateRole(role).subscribe(
                (value: any) => {
                    this.messageService.add({ key: 'toast', severity: 'success', summary: 'Role updated successfully', detail: value.exceptionMessage });
                    this.UpdateOrAddConfirmedDialogEvent.emit(true);
                    this.loading = false;
                },
                error => {
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error occured while updating role', detail: error.error.exceptionMessage });
                    this.loading = false;
                    () => {
                        console.warn('warning ........');
                    }
                });

        }
        else {
            this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error occured while updating role', detail: "role id is null" });
        }
    }
    closeAddDialog() {
        this.closeAddDialogEvent.emit(false);
    }
    private getSelectedPermissionsList(nodes: TreeNode[]) {
        const permissionsList = []
        for (const node of this.selectedPermissions) {
            if (node.partialSelected == false) {
                permissionsList.push(node.data)

            }
        }
        console.log(permissionsList)
        return (permissionsList)
    }
    private getSelectedPermissionsRecursive(nodes: TreeNode[]) {
        for (const node of nodes) {
            if (node.partialSelected == false) {
                this.selectedPermissions.push(node);
            }
            if (node.children) {
                this.getSelectedPermissionsRecursive(node.children);
            }
        }
    }
    nodeSelect(e: TreeNodeSelectEvent) {
        console.log("--- select", this.selectedPermissions)
        const selectedNode = e.node;
        const existingNodeIndex = this.selectedPermissions.findIndex((node: TreeNode) => node.data === selectedNode.data);
        if (existingNodeIndex !== -1) {
            this.selectedPermissions.splice(existingNodeIndex, 1);
            this.selectedPermissions.push(selectedNode);
        } else {
            this.selectedPermissions.push(selectedNode);
        }
        console.log("+++ select", this.selectedPermissions)
    }
    NodeUnselect(e: TreeNodeSelectEvent) {
    }
    confirmUpdate() {
        this.updateRole()
        this.displayUpdateConfirmationDialog = false;
        console.log("deleted")

    }
    onElementUpdated() {
        //this.UpdateConfirmedDialogEvent.emit(true);
    }
    onCancelUpdate() {
        this.displayUpdateConfirmationDialog = false;
    }

    isSaveDisabled() {

        if (this.roleFormGroup.get("name")?.invalid || !this.selectedPermissions || this.selectedPermissions.length === 0) {
            return true ;
        }
        return false || this.isDisabledSave;
    }

}


