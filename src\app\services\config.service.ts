import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { Environment } from 'src/environments/environment.interface';

@Injectable({
  providedIn: 'root'
})
export class ConfigService {
  private readonly config: Environment = environment;

  constructor() {
    this.validateConfiguration();
  }

  // Getters pour la configuration API
  get apiUrl(): string {
    return this.config.API;
  }

  get notificationApiUrl(): string {
    return this.config.NotificationAPI;
  }

  get webApiUrl(): string {
    return this.config.WebAPI;
  }

  get s3Url(): string {
    return this.config.S3Url;
  }

  // Getters pour la configuration générale
  get isProduction(): boolean {
    return this.config.production;
  }

  get environmentName(): string {
    return this.config.environmentName;
  }

  get baseUrl(): string {
    return this.config.baseUrl;
  }

  get useHash(): boolean {
    return this.config.useHash;
  }

  // Getters pour la configuration de logging
  get isLoggingEnabled(): boolean {
    return this.config.enableLogging;
  }

  get logLevel(): string {
    return this.config.logLevel;
  }

  // Getters pour la configuration de cache
  get cacheTimeout(): number {
    return this.config.cacheTimeout;
  }

  // Getters pour la configuration de sécurité
  get tokenRefreshBuffer(): number {
    return this.config.tokenRefreshBuffer;
  }

  get maxRetryAttempts(): number {
    return this.config.maxRetryAttempts;
  }

  // Getters pour la configuration de l'interface
  get defaultPageSize(): number {
    return this.config.defaultPageSize;
  }

  get maxPageSize(): number {
    return this.config.maxPageSize;
  }

  // Getters pour les features
  get isAdvancedDashboardEnabled(): boolean {
    return this.config.features.enableAdvancedDashboard;
  }

  get isAuditLogsEnabled(): boolean {
    return this.config.features.enableAuditLogs;
  }

  get isNotificationsEnabled(): boolean {
    return this.config.features.enableNotifications;
  }

  get isExportsEnabled(): boolean {
    return this.config.features.enableExports;
  }

  // Méthodes utilitaires
  isFeatureEnabled(featureName: keyof Environment['features']): boolean {
    return this.config.features[featureName];
  }

  getFullApiUrl(endpoint: string): string {
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    return `${this.apiUrl}/${cleanEndpoint}`;
  }

  getFullNotificationUrl(endpoint: string): string {
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    return `${this.notificationApiUrl}/${cleanEndpoint}`;
  }

  getS3FileUrl(fileName: string): string {
    const cleanFileName = fileName.startsWith('/') ? fileName.slice(1) : fileName;
    return `${this.s3Url}${cleanFileName}`;
  }

  // Validation de la configuration
  private validateConfiguration(): void {
    const requiredFields = ['API', 'NotificationAPI', 'WebAPI', 'S3Url'];
    const missingFields = requiredFields.filter(field => !this.config[field as keyof Environment]);

    if (missingFields.length > 0) {
      console.error('Configuration manquante pour les champs:', missingFields);
      if (this.isProduction) {
        throw new Error(`Configuration de production incomplète: ${missingFields.join(', ')}`);
      }
    }

    // Log de la configuration en développement
    if (!this.isProduction && this.isLoggingEnabled) {
      console.group('🔧 Configuration de l\'application');
      console.log('Environnement:', this.environmentName);
      console.log('API URL:', this.apiUrl);
      console.log('Notification API:', this.notificationApiUrl);
      console.log('Features activées:', Object.entries(this.config.features)
        .filter(([, enabled]) => enabled)
        .map(([feature]) => feature));
      console.groupEnd();
    }
  }

  // Méthode pour obtenir toute la configuration (pour debugging)
  getFullConfig(): Environment {
    return { ...this.config };
  }
}
