# Script PowerShell pour démarrer l'application Angular en local
# Plus rapide que Docker pour les tests de développement

param(
    [switch]$Clean,
    [switch]$MockApi,
    [switch]$SkipInstall
)

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

Write-Host "🚀 Démarrage de l'application Angular en local" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan

# Vérifier que Node.js est installé
if (-not (Get-Command "node" -ErrorAction SilentlyContinue)) {
    Write-Error "Node.js n'est pas installé. Veuillez installer Node.js pour continuer."
    exit 1
}

$nodeVersion = node --version
Write-Status "Version Node.js: $nodeVersion"

# Vérifier que npm est installé
if (-not (Get-Command "npm" -ErrorAction SilentlyContinue)) {
    Write-Error "npm n'est pas installé. Veuillez installer npm pour continuer."
    exit 1
}

$npmVersion = npm --version
Write-Status "Version npm: $npmVersion"

# Nettoyage si demandé
if ($Clean) {
    Write-Status "Nettoyage des dépendances..."
    
    if (Test-Path "node_modules") {
        Remove-Item -Recurse -Force "node_modules"
        Write-Success "node_modules supprimé"
    }
    
    if (Test-Path "package-lock.json") {
        Remove-Item -Force "package-lock.json"
        Write-Success "package-lock.json supprimé"
    }
    
    # Nettoyer le cache npm
    npm cache clean --force
    Write-Success "Cache npm nettoyé"
}

# Installation des dépendances
if (-not $SkipInstall) {
    Write-Status "Installation des dépendances..."
    
    if (-not (Test-Path "node_modules")) {
        Write-Status "Installation initiale des dépendances (cela peut prendre quelques minutes)..."
        npm install --legacy-peer-deps
        
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Échec de l'installation des dépendances"
            Write-Status "Tentative avec cache nettoyé..."
            npm cache clean --force
            npm install --legacy-peer-deps --no-optional
            
            if ($LASTEXITCODE -ne 0) {
                Write-Error "Impossible d'installer les dépendances. Vérifiez votre connexion internet."
                exit 1
            }
        }
        
        Write-Success "Dépendances installées avec succès"
    } else {
        Write-Status "Dépendances déjà installées, vérification..."
        npm ls --depth=0 2>$null | Out-Null
        if ($LASTEXITCODE -ne 0) {
            Write-Warning "Problème détecté avec les dépendances, réinstallation..."
            npm install --legacy-peer-deps
        }
        Write-Success "Dépendances vérifiées"
    }
}

# Démarrer l'API mock si demandé
if ($MockApi) {
    Write-Status "Démarrage de l'API mock..."
    
    # Vérifier si json-server est installé
    if (-not (Get-Command "json-server" -ErrorAction SilentlyContinue)) {
        Write-Status "Installation de json-server..."
        npm install -g json-server
    }
    
    # Démarrer json-server en arrière-plan
    Start-Process -FilePath "json-server" -ArgumentList "--watch mock-data.json --port 3001 --routes routes.json" -WindowStyle Hidden
    Write-Success "API mock démarrée sur http://localhost:3001"
    
    # Attendre que l'API soit prête
    $timeout = 30
    $counter = 0
    do {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:3001/companies" -TimeoutSec 2 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                break
            }
        }
        catch {
            # Continuer à attendre
        }
        
        Start-Sleep -Seconds 1
        $counter++
        
        if ($counter -ge $timeout) {
            Write-Warning "L'API mock n'a pas démarré dans les temps"
            break
        }
    } while ($true)
    
    if ($counter -lt $timeout) {
        Write-Success "API mock prête et accessible"
    }
}

# Vérifier la compilation TypeScript
Write-Status "Vérification de la compilation TypeScript..."
npx tsc --noEmit

if ($LASTEXITCODE -ne 0) {
    Write-Warning "Erreurs TypeScript détectées, mais l'application peut quand même démarrer"
} else {
    Write-Success "Aucune erreur TypeScript détectée"
}

# Démarrer l'application Angular
Write-Status "Démarrage de l'application Angular..."
Write-Host ""
Write-Host "🌐 L'application sera accessible sur: " -NoNewline
Write-Host "http://localhost:4200" -ForegroundColor Blue

if ($MockApi) {
    Write-Host "🔧 API Mock accessible sur: " -NoNewline
    Write-Host "http://localhost:3001" -ForegroundColor Blue
}

Write-Host ""
Write-Host "📋 CHECKLIST DES TESTS MANUELS:" -ForegroundColor Yellow
Write-Host ""
Write-Host "✅ Phase 1 - Corrections Prioritaires:" -ForegroundColor Green
Write-Host "   1. Tester la connexion utilisateur"
Write-Host "   2. Vérifier la gestion des erreurs"
Write-Host "   3. Tester le refresh automatique du token"
Write-Host "   4. Vérifier les messages d'erreur conviviaux"
Write-Host ""
Write-Host "✅ Phase 2 - Optimisations:" -ForegroundColor Green  
Write-Host "   5. Tester la pagination sur la liste des entreprises"
Write-Host "   6. Vérifier le tri et les filtres"
Write-Host "   7. Tester les actions CRUD"
Write-Host "   8. Vérifier la gestion d'état"
Write-Host ""
Write-Host "✅ Phase 3 - Fonctionnalités Avancées:" -ForegroundColor Green
Write-Host "   9. Accéder aux logs d'audit"
Write-Host "   10. Tester les filtres avancés"
Write-Host "   11. Vérifier les notifications"
Write-Host "   12. Tester la gestion des rôles"
Write-Host ""
Write-Host "⏹️  ARRÊTER L'APPLICATION:" -ForegroundColor Red
Write-Host "   Appuyez sur Ctrl+C dans le terminal"
Write-Host ""

# Démarrer ng serve
npm start
