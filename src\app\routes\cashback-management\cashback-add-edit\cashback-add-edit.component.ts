import { Component, EventEmitter, Input, OnChanges, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { MessageService, SelectItem } from 'primeng/api';
import { CashbackService } from 'src/app/services/cashback.service';
import { Currency } from 'src/app/shared/enums/Currency';
import { PaymentMethod } from 'src/app/shared/enums/PaymentMethod';
import { ProductType } from 'src/app/shared/enums/ProductType';
import { StatusCode } from 'src/app/shared/enums/StatusCode';
import { Cashback } from 'src/app/shared/models/cashback/cashback';
import { LocalStoreService } from 'src/app/services/local-store.service';
import { CompanyService } from 'src/app/services/company.service';
import jwtDecode from 'jwt-decode';
import { tokenDTO } from 'src/app/shared/models/authentification/token';
import { Company } from 'src/app/shared/models/company/company';
import { PaymentDetails } from 'src/app/shared/models/company/paymentDetails';
import { WalletService } from 'src/app/services/wallet.service';

@Component({
    selector: 'app-cashback-add-edit',
    templateUrl: './cashback-add-edit.component.html',
    styleUrls: ['./cashback-add-edit.component.scss']
})
export class CashbackAddEditComponent {
    @Output() closeAddDialogEvent = new EventEmitter();
    @Output() cashbackAdded = new EventEmitter<void>();
    @Input() display: boolean = false;
    isvalid: boolean = true;
    productTypeDropdown: SelectItem[] = [];
    PaymentDetailsDropdown: SelectItem[] = [];
    selectedPaymentDetails: PaymentDetails | undefined;
    cashbackFormGroup: FormGroup;
    validForm = false;
    totalAmount: number = 0;
    company: Company | null = null;
    netAmount: number = 0;
    vatPercentage: number = 19;
    vatAmount: number = 0;
    public isDisabledSave = false;
    CompanyAdress: string = "";
    walletAmount: number = 0;
    loading = false;
    feeAmount: number = 0;

    constructor(private cashbackService: CashbackService,
        private walletService: WalletService,
        private localStorageService: LocalStoreService,
        private companyService: CompanyService,
        private formBuilder: FormBuilder, private route: ActivatedRoute,
        private messageService: MessageService) {
        this.productTypeDropdown = this.enumToArray(ProductType);

        this.cashbackFormGroup = this.formBuilder.group({
            dynoAmount: ["", [Validators.required, Validators.pattern(/^(?!0+(\.0*)?$)\d+(\.\d{1,3})?$/), Validators.max(this.walletAmount)]],
            shopFeePercentage: [, Validators.pattern(/^(?!0+(\.0*)?$)\d+(\.\d{1,3})?$/)],
            feeAmount: [, Validators.pattern(/^(?!0+(\.0*)?$)\d+(\.\d{1,4})?$/)],
            paymentDetailsId: [null, Validators.required]
        });
        this.cashbackFormGroup.get('shopFeePercentage')?.disable();
        this.cashbackFormGroup.get('feeAmount')?.disable();
    }

    enumToArray(enumerable: any): SelectItem[] {
        return Object.keys(enumerable)
            .filter(key => !isNaN(Number(enumerable[key])))
            .map(key => ({ label: key, value: enumerable[key] }));
    }

    valSwitch = true;

    ngOnInit() {
        let token = this.localStorageService.getData("Token");
        if (token != undefined) {
            let decodedToken: tokenDTO = jwtDecode(token);
            this.companyService.getCompanyById(decodedToken.Company).subscribe((response) => {
                if (response.statusCode == StatusCode.Ok) {
                    this.company = response.objectValue;
                    this.CompanyAdress = this.company?.addresses[0]?.fullAddress
                    this.getCompanyPaymentDetails(this.company);
                }
                else {
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error getting Company by id =' + decodedToken.Company, detail: response.exceptionMessage });
                }
            });;
        }
        this.walletService.GetUserWallets().toPromise().then(response => {
            if (response?.statusCode === 200 && response != undefined) {
                this.walletAmount = response.objectValue[0].balance;
                this.cashbackFormGroup.get("dynoAmount")?.setValidators([Validators.required, Validators.pattern(/^(?!0+(\.0*)?$)\d+(\.\d{1,3})?$/), Validators.max(this.walletAmount)])
            } else {
                console.error('Failed to fetch user wallets:', response?.exceptionMessage);
            }
        });

    }

    getPaymentMethodString(paymentMethod: number | undefined) {
        if (paymentMethod != undefined) {
            return PaymentMethod[paymentMethod];
        }
        else {
            return null;
        }
    }

    getCompanyPaymentDetails(company: Company) {
        let paymentDetailsList = company.paymentDetails;
        paymentDetailsList.forEach(paymentDetails => {
            this.PaymentDetailsDropdown.push({ 'label': paymentDetails.code, 'value': paymentDetails })
        });
        if (paymentDetailsList.length == 1) {
            this.cashbackFormGroup.get('paymentDetailsId')?.setValue(paymentDetailsList[0]);
            this.selectedPaymentDetails = paymentDetailsList[0];
            this.updateAmounts();
        }
    }

    isSaveDisabled(): boolean {
        return this.cashbackFormGroup.invalid;
    }

    saveCashback() {

        // Mark all form controls as touched
        Object.values(this.cashbackFormGroup.controls).forEach(control => {
            control.markAsTouched();
        });
        if (this.isSaveDisabled()) {
            this.loading = false;
            return;
        }
        this.loading = true;

        let cashbackToSave: Cashback = {
            dynoAmount: this.cashbackFormGroup.get("dynoAmount")?.value,
            feeAmount: this.cashbackFormGroup.get("feeAmount")?.value,
            netAmount: Number(this.netAmount.toFixed(3)),
            vatAmount: Number(this.vatAmount.toFixed(3)),
            totalAmount: Number(this.totalAmount.toFixed(3)),
            paymentDetailsId: this.cashbackFormGroup.get("paymentDetailsId")?.value?.id,
            walletBallance: 0
        }

        this.cashbackService.addCashback(cashbackToSave).subscribe({
            next: (response) => {
                if (response.statusCode == StatusCode.Created) {
                    this.messageService.add({ key: 'toast', severity: 'success', summary: 'Cashback added successfully', detail: response.exceptionMessage });
                    this.cashbackAdded.emit();
                    this.closeAddDialogEvent.emit(false); // Close the dialog
                }
                else {
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Add Cashback', detail: response.exceptionMessage });
                }
                this.isDisabledSave = false;
                this.loading = false;
            },
            error: (error) => {
                this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Add Cashback', detail: error.exceptionMessage });
                this.isDisabledSave = false;
                this.loading = false;
            }
        }
        );


    }

    onDynoAmountChange(event: any) {
        let dynoAmount = this.cashbackFormGroup.get('dynoAmount')?.value;
        let shopFeePercentage = this.cashbackFormGroup.get('shopFeePercentage')?.value;
        this.feeAmount = dynoAmount * shopFeePercentage / 100;

        this.cashbackFormGroup.get('feeAmount')?.setValue(this.feeAmount);
        this.netAmount = dynoAmount - this.feeAmount;
        this.vatAmount = this.netAmount * (this.vatPercentage / 100);
        this.totalAmount = this.netAmount * (1 + this.vatPercentage / 100);
        this.validForm = this.cashbackFormGroup.valid
    }

    onPaymentDetailsChange(event: any) {
        this.selectedPaymentDetails = event.value
        if (this.selectedPaymentDetails != null) {
            this.updateAmounts()
        }
        else {
            this.cashbackFormGroup.get('paymentDetailsId')?.setValue(null);
            this.resetAmounts();
        }

    }

    updateAmounts() {
        this.cashbackFormGroup.get('shopFeePercentage')?.setValue(this.selectedPaymentDetails?.shopFeePercentage)
        let dynoAmount = this.cashbackFormGroup.get('dynoAmount')?.value;
        let shopFeePercentage = this.cashbackFormGroup.get('shopFeePercentage')?.value;
        let feeAmount = dynoAmount * shopFeePercentage / 100;
        console.log("fee amount", feeAmount, "feeper", shopFeePercentage, "dynoamount", dynoAmount)
        this.cashbackFormGroup.get('feeAmount')?.setValue(feeAmount);
        this.netAmount = dynoAmount - feeAmount;
        this.vatAmount = this.netAmount * (this.vatPercentage / 100);
        this.totalAmount = this.netAmount * (1 + this.vatPercentage / 100);
        this.validForm = this.cashbackFormGroup.valid
    }

    closeAddDialog() {
        this.selectedPaymentDetails = undefined;
        this.resetData();
        this.closeAddDialogEvent.emit(false);
    }

    resetAmounts() {
        this.netAmount = 0;
        this.vatAmount = 0;
        this.totalAmount = 0;
        this.cashbackFormGroup.get('shopFeePercentage')?.setValue(0);
        this.cashbackFormGroup.get('feeAmount')?.setValue(0)
    }

    resetData() {
        this.netAmount = 0;
        this.vatAmount = 0;
        this.totalAmount = 0;
        let shopFeePercentage = this.cashbackFormGroup.get('shopFeePercentage')?.value;
        let paymentDetails = this.cashbackFormGroup.get('paymentDetailsId')?.value;
        this.cashbackFormGroup.reset();
        this.cashbackFormGroup.get('shopFeePercentage')?.setValue(shopFeePercentage);
        this.cashbackFormGroup.get('paymentDetailsId')?.setValue(paymentDetails);
    }
}

