import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ValidInvoicesComponent } from './valid-invoices/valid-invoices.component';
import { RoleGuard } from 'src/app/RoleGuard';

@NgModule({
  imports: [RouterModule.forChild([
    { path: '', component: ValidInvoicesComponent,canActivate:[RoleGuard],data:{role:["superadmin"]as string[]} }
  ])],
  exports: [RouterModule]
})
export class InvoiceManagementRoutingModule { }
