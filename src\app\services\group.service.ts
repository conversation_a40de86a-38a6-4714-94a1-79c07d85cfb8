import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ResponseAPI } from '../shared/models/ResponseAPI';
import { Group } from '../shared/models/Group/group';
import { environment } from 'src/environments/environment';
import { FilterMetadata } from 'primeng/api';
import { Ticket } from '../shared/models/ticket/ticket';

@Injectable({
    providedIn: 'root'
})
export class GroupService {

    constructor(private _http: HttpClient) { }



    getAllGroups() {
        return this._http.get<ResponseAPI<Group[]>>(`${environment.API}/Group/GetAll`);
    }
    // getAllPaginationGroups(pageSize: number, pageNumber: number, sortBy: string, sortDirection: number, filters: { [s: string]: FilterMetadata } | undefined) {
    //     let params = new HttpParams()
    //         .set('pageSize', pageSize.toString())
    //         .set('pageNumber', pageNumber.toString())
    //         .set('sortBy', sortBy.toString())
    //         .set('sortDirection', sortDirection.toString());
    //     if (filters) {
    //         const filterParams: { [key: string]: string } = {};
    //         for (const field in filters) {
    //             if (Object.prototype.hasOwnProperty.call(filters, field)) {
    //                 const filterValue = filters[field];
    //                 if (filterValue != undefined && Array.isArray(filterValue)) {

    //                     filterValue.forEach((filter: FilterMetadata) => {
    //                         if (filter.value !== undefined && filter.value !== null) {

    //                             params = params.append(`filters.${field}`, filter.value.toString());

    //                         }
    //                     });
    //                 } else {
    //                     if (filterValue.value !== undefined && filterValue.value !== null) {
    //                         params = params.append(`filters.${field}`, filterValue.toString());
    //                     }
    //                 }
    //             }
    //         }
    //     }


    //     return this._http.get<ResponseAPI<Group[]>>(`${environment.API}/Group/GetAllPaged`, { params, observe: 'response', responseType: 'json' });
    // }


      getAllPaginationGroups(pageSize: number, pageNumber: number, sortBy: string, sortDirection: number, filters: { [s: string]: FilterMetadata } | undefined) {
        let simplifiedFilters: { [s: string]: string } = {};
        if (filters) {
            for (const field in filters) {
                if (Object.prototype.hasOwnProperty.call(filters, field)) {
                    const filterValues = filters[field];

                    if (filterValues !== undefined && Array.isArray(filterValues) && filterValues.length > 0) {
                        const filterValue = filterValues[0]?.value;

                        if (filterValue !== undefined && filterValue !== null) {
                            simplifiedFilters[field] = filterValue.toString();
                        }
                    } else {
                        const filterValue = filterValues?.value;

                        if (filterValue !== undefined && filterValue !== null) {
                            simplifiedFilters[field] = filterValue.toString();
                        }
                    }
                }
            }
        }

        const requestBody = {
            pageSize,
            pageNumber,
            sortBy: sortBy.toString(),
            sortDirection: sortDirection.toString(),
            filters: simplifiedFilters,
        };

        return this._http.post<ResponseAPI<Group[]>>(`${environment.API}/Group/GetAllPaged`, requestBody, { observe: 'response', responseType: 'json' });
    }

    getGroupById(id: string) {
        return this._http.get<ResponseAPI<Group>>(`${environment.API}/Group/Get/${id}`);
    }

    addGroup(group: Group) {
        return this._http.post<ResponseAPI<Group>>(`${environment.API}/Group/Create`, group);
    }

    updateGroup(group: Group) {
        return this._http.put<ResponseAPI<Group>>(`${environment.API}/Group/Update`, group);
    }

    deleteGroup(id: string) {
        return this._http.delete<ResponseAPI<void>>(`${environment.API}/Group/Delete/${id}`);
    }
    sendTicketsToGroup(groupId: string, tickets: Ticket[]) {
        console.log("groupId", groupId, "tickets:", tickets)
        return this._http.get<ResponseAPI<Group[]>>(`${environment.API}/Group/GetAll`);
        // return this._http.post<ResponseAPI<Group>>(`${environment.API}/Group/Create`, group);
    }


}
