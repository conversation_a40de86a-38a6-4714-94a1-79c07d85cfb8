import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ProfilePictureService {

  private profilePictureUrlSubject: BehaviorSubject<string> = new BehaviorSubject<string>('');

  constructor() { }

  getProfilePictureUrl(): Observable<string> {
    return this.profilePictureUrlSubject.asObservable();
  }

  setProfilePictureUrl(url: string): void {
    this.profilePictureUrlSubject.next(url);
  }
}