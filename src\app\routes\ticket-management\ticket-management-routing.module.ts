import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { TicketListComponent } from './ticket-list/ticket-list.component';
import { RoleGuard } from 'src/app/RoleGuard';

@NgModule({
    imports: [RouterModule.forChild([
        { path: '', component:TicketListComponent ,canActivate:[RoleGuard],data:{role:["company"]as string[]} }
    ])],
    exports: [RouterModule]
})
export class TicketManagementRoutingModule { }
