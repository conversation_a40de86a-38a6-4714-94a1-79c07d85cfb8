import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { combineLatest } from 'rxjs';
import { CashierListComponent } from './cashier-list/cashier-list.component';
import { RoleGuard } from 'src/app/RoleGuard';
@NgModule({
    imports: [RouterModule.forChild([
        { path: '' , component: CashierListComponent,canActivate:[RoleGuard],data:{role:["shopowner"]as string[]}}
    ])],
    exports: [RouterModule]
})
export class CashierManagementRoutingModule { }
