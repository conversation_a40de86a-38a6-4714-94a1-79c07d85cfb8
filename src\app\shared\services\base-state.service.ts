import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, map, distinctUntilChanged, shareReplay } from 'rxjs';

export interface EntityState<T> {
  items: T[];
  selectedItem: T | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

export interface CacheConfig {
  ttl: number; // Time to live en millisecondes
  maxSize: number; // Taille maximale du cache
}

@Injectable()
export abstract class BaseStateService<T> {
  private readonly defaultState: EntityState<T> = {
    items: [],
    selectedItem: null,
    loading: false,
    error: null,
    lastUpdated: null
  };

  private state$ = new BehaviorSubject<EntityState<T>>(this.defaultState);
  private cache = new Map<string, { data: any; timestamp: number }>();
  
  protected abstract getItemId(item: T): string;
  
  protected cacheConfig: CacheConfig = {
    ttl: 5 * 60 * 1000, // 5 minutes par défaut
    maxSize: 100
  };

  constructor() {}

  // Sélecteurs d'état
  get state(): EntityState<T> {
    return this.state$.value;
  }

  get items$(): Observable<T[]> {
    return this.state$.pipe(
      map(state => state.items),
      distinctUntilChanged(),
      shareReplay(1)
    );
  }

  get selectedItem$(): Observable<T | null> {
    return this.state$.pipe(
      map(state => state.selectedItem),
      distinctUntilChanged(),
      shareReplay(1)
    );
  }

  get loading$(): Observable<boolean> {
    return this.state$.pipe(
      map(state => state.loading),
      distinctUntilChanged(),
      shareReplay(1)
    );
  }

  get error$(): Observable<string | null> {
    return this.state$.pipe(
      map(state => state.error),
      distinctUntilChanged(),
      shareReplay(1)
    );
  }

  get lastUpdated$(): Observable<Date | null> {
    return this.state$.pipe(
      map(state => state.lastUpdated),
      distinctUntilChanged(),
      shareReplay(1)
    );
  }

  // Actions d'état
  setLoading(loading: boolean): void {
    this.updateState({ loading });
  }

  setError(error: string | null): void {
    this.updateState({ error, loading: false });
  }

  setItems(items: T[]): void {
    this.updateState({ 
      items, 
      loading: false, 
      error: null, 
      lastUpdated: new Date() 
    });
  }

  setSelectedItem(item: T | null): void {
    this.updateState({ selectedItem: item });
  }

  addItem(item: T): void {
    const currentItems = this.state.items;
    const newItems = [...currentItems, item];
    this.setItems(newItems);
  }

  updateItem(updatedItem: T): void {
    const currentItems = this.state.items;
    const itemId = this.getItemId(updatedItem);
    
    const newItems = currentItems.map(item => 
      this.getItemId(item) === itemId ? updatedItem : item
    );
    
    this.setItems(newItems);
    
    // Mettre à jour l'élément sélectionné si c'est le même
    if (this.state.selectedItem && this.getItemId(this.state.selectedItem) === itemId) {
      this.setSelectedItem(updatedItem);
    }
  }

  removeItem(itemId: string): void {
    const currentItems = this.state.items;
    const newItems = currentItems.filter(item => this.getItemId(item) !== itemId);
    this.setItems(newItems);
    
    // Désélectionner l'élément si c'est celui qui est supprimé
    if (this.state.selectedItem && this.getItemId(this.state.selectedItem) === itemId) {
      this.setSelectedItem(null);
    }
  }

  clearState(): void {
    this.state$.next(this.defaultState);
    this.clearCache();
  }

  // Gestion du cache
  setCache<K>(key: string, data: K): void {
    // Nettoyer le cache si nécessaire
    if (this.cache.size >= this.cacheConfig.maxSize) {
      this.cleanExpiredCache();
      
      // Si toujours plein, supprimer les plus anciens
      if (this.cache.size >= this.cacheConfig.maxSize) {
        const oldestKey = Array.from(this.cache.keys())[0];
        this.cache.delete(oldestKey);
      }
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  getCache<K>(key: string): K | null {
    const cached = this.cache.get(key);
    
    if (!cached) return null;
    
    // Vérifier si le cache est expiré
    if (Date.now() - cached.timestamp > this.cacheConfig.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.data as K;
  }

  clearCache(): void {
    this.cache.clear();
  }

  private cleanExpiredCache(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];
    
    this.cache.forEach((value, key) => {
      if (now - value.timestamp > this.cacheConfig.ttl) {
        expiredKeys.push(key);
      }
    });
    
    expiredKeys.forEach(key => this.cache.delete(key));
  }

  // Méthodes utilitaires
  getItemById(id: string): T | null {
    return this.state.items.find(item => this.getItemId(item) === id) || null;
  }

  hasItem(id: string): boolean {
    return this.state.items.some(item => this.getItemId(item) === id);
  }

  getItemsCount(): number {
    return this.state.items.length;
  }

  isDataStale(maxAge: number = this.cacheConfig.ttl): boolean {
    if (!this.state.lastUpdated) return true;
    return Date.now() - this.state.lastUpdated.getTime() > maxAge;
  }

  // Méthode privée pour mettre à jour l'état
  private updateState(partialState: Partial<EntityState<T>>): void {
    const currentState = this.state$.value;
    const newState = { ...currentState, ...partialState };
    this.state$.next(newState);
  }

  // Méthodes de debugging
  getStateSnapshot(): EntityState<T> {
    return { ...this.state };
  }

  getCacheInfo(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}
