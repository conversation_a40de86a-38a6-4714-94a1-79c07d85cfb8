import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit  } from '@angular/core';
import { Subscription } from 'rxjs';
import { NotificationService } from 'src/app/services/notification.service';
import { SignalRService } from 'src/app/services/signalR.service';
import { Notification } from 'src/app/shared/models/notification/notification';

@Component({
  selector: 'app-notification',
  templateUrl: './notification.component.html',
  styleUrls: ['./notification.component.scss']
})

export class NotificationComponent implements OnInit, OnDestroy {
  notifications: Notification[] = [];
  private subscription!: Subscription;
  recentNotifications: Notification[] = [];

  constructor(private signalRService: SignalRService,
    private notificationService: NotificationService, 
    private ngZone: NgZone) {}

  ngOnInit() {
    this.getNotifications();
    this.notifications = this.signalRService.notifications; 
    this.subscription = this.signalRService.notificationReceived.subscribe((notification) => {
      this.ngZone.run(() => {
        this.addNotification(notification);
      });
      
    });
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  getNotifications () {
    this.notificationService.getAll().subscribe(
      (response) => {
          if (response.objectValue) {
              this.notifications = response.objectValue.slice(-5); // Keep only the last 5 notifications
          }
      });
  }

  closeNotification(notification: Notification): void {
    this.notificationService.updateIsSeenTrue(notification.id).subscribe(
      (response) => {
        if(response.statusCode === 200) {
          // Remove the closed notification from the array
          const index = this.notifications.indexOf(notification);
          if (index !== -1) {
            this.notifications.splice(index, 1);
          }
          // Fetch new notifications if the list is not full
          if (this.notifications.length < 5) {
            this.getNotifications();
          }
        }
      }
    );
  }

  addNotification(notification: Notification): void {
    if (this.notifications.length >= 5) {
      // Remove the oldest notification if the list already has 5 notifications
      this.notifications.shift();
    }
    // Add the new notification
    this.notifications.push(notification);
  }
}

