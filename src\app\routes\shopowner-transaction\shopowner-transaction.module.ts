import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';

import { ShopownerTransactionRoutingModule } from './shopowner-transaction-routing.module';
import { ListTransactionComponent } from './list-transaction/list-transaction.component';
import { CancelTransactionComponent } from './cancel-transaction/cancel-transaction.component';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { RadioButtonModule } from 'primeng/radiobutton';
import { RatingModule } from 'primeng/rating';
import { RippleModule } from 'primeng/ripple';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { ToolbarModule } from 'primeng/toolbar';
import { ConfirmationService, MessageService } from 'primeng/api';
import { TooltipModule } from 'primeng/tooltip';
@NgModule({
    declarations: [
        ListTransactionComponent,
        CancelTransactionComponent
    ],
    imports: [
        CommonModule,
        ShopownerTransactionRoutingModule,
        TableModule,
        FormsModule,
        ButtonModule,
        RippleModule,
        ToastModule,
        ToolbarModule,
        RatingModule,
        InputTextModule,
        InputTextareaModule,
        DropdownModule,
        RadioButtonModule,
        InputNumberModule,
        DialogModule,
        TooltipModule
    ],
    providers: [ConfirmationService, MessageService, DatePipe]
})
export class ShopownerTransactionModule { }
