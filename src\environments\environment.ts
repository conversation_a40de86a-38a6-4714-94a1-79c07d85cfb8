// Environment de développement
export const environment = {
  production: false,
  environmentName: 'development',

  // Configuration API
  API: 'https://localhost:7274/Api',
  NotificationAPI: 'http://localhost:7038',
  WebAPI: 'http://localhost:80',

  // Configuration de stockage
  S3Url: 'https://dynofiles.s3.amazonaws.com/',

  // Configuration de routage
  baseUrl: '',
  useHash: false,

  // Configuration de logging
  enableLogging: true,
  logLevel: 'debug',

  // Configuration de cache
  cacheTimeout: 300000, // 5 minutes

  // Configuration de sécurité
  tokenRefreshBuffer: 60000, // 1 minute avant expiration
  maxRetryAttempts: 3,

  // Configuration de l'interface
  defaultPageSize: 10,
  maxPageSize: 100,

  // Configuration des features
  features: {
    enableAdvancedDashboard: true,
    enableAuditLogs: true,
    enableNotifications: true,
    enableExports: true
  }
};
