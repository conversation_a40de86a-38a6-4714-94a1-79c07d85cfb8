.card {
    background-color: #fff;
    border-radius: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.185);
    overflow: hidden;
    padding: 1rem;
}

.card-header {
    text-align: center;
    font-size: 16px;
    font-style: bold;
    font-family: 'Franklin Gothic Medium', '<PERSON><PERSON>rrow', Aria<PERSON>, sans-serif;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-content {
    
}

.card-footer {
    background-color: #f2f2f2;
    padding: 15px;
    text-align: center;
}

.close-button {
    background-color: transparent;
    border: none;
    cursor: pointer;
    font-size: 16px;
    color: green;
    transition: color 0.3s, border-color 0.3s;
}

.close-button:hover {
    color: #27ae60; /* Change to your desired green color */
    border: 2px solid #27ae60; /* Add a green border on hover */
    border-radius: 50%; /* Create a circular border */
}
  