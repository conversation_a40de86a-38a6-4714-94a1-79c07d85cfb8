import { Group } from 'src/app/shared/models/Group/group';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, Output } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Status } from 'src/app/shared/enums/status';
import { ActivatedRoute } from '@angular/router';
import { MessageService, SelectItem } from 'primeng/api';
import { StatusCode } from 'src/app/shared/enums/StatusCode';
import { GroupService } from 'src/app/services/group.service';
import { TicketService } from 'src/app/services/ticket.service';
import { Ticket } from 'src/app/shared/models/ticket/ticket';
import { WalletType } from 'src/app/shared/enums/wallet-type';
import { CompanyService } from 'src/app/services/company.service';
import { GroupTicket } from 'src/app/shared/models/Group/groupTicket';
@Component({
    selector: 'app-group-add-edit',
    templateUrl: './group-add-edit.component.html',
    styleUrls: ['./group-add-edit.component.scss']
})
export class GroupAddEditComponent implements OnChanges {
    @Output() closeAddDialogEvent = new EventEmitter();
    @Output() groupAdded = new EventEmitter<void>();
    @Input() groupId: string = "";
    @Input() selectedGroup: Group | null = null;
    @Input() display: boolean = false;
    isvalid: boolean = true;
    statusDropdown: SelectItem[] = [];
    ticketTypeDropdown: SelectItem[] = [];
    selectedTicketType: WalletType = WalletType['Restaurant Ticket'];
    selectedStatus: Status = Status.Active;
    groupFormGroup: FormGroup;
    mode: string = "";
    ticketSuggestions: any[] = [];
    selectedTicket: Ticket | null = null;
    ticketToUpdate: Ticket | null = null;
    loading: boolean = false;
    isValidTicket: boolean = false;

    constructor(private groupService: GroupService, private ticketService: TicketService,
        private formBuilder: FormBuilder, private companyService: CompanyService, private cdRef: ChangeDetectorRef,
        private messageService: MessageService) {
        this.statusDropdown = this.enumToArray(Status);
        this.ticketTypeDropdown = this.enumToArray(WalletType);
        this.groupFormGroup = this.formBuilder.group({
            name: ["", Validators.required],
            ticketsArray: this.formBuilder.array([]),
            status: ["", Validators.required]
        });

    }
    enumToArray(enumerable: any): SelectItem[] {
        return Object.keys(enumerable)
            .filter(key => !isNaN(Number(enumerable[key])))
            .map(key => ({ label: key, value: enumerable[key] }));
    }
    ngOnInit() {
    }

    ngOnChanges() {

        Promise.resolve().then(() => {
            if (this.selectedGroup) {
                this.mode = 'edit';
                this.populateFormFromGroup(this.selectedGroup);
            } else {
                this.mode = 'add';

                this.ticketsArray.clear();
                this.groupFormGroup.reset();
                this.addTicketGroup();
            }
            this.cdRef.detectChanges();
        });
    }

    get ticketsArray(): FormArray {
        return this.groupFormGroup.get('ticketsArray') as FormArray;
    }

    addTicketGroup() {
        const ticketGroup = this.formBuilder.group({
            ticketType: [""],
            ticket: ["", [Validators.required, this.ticketValidator]]
        });

        this.ticketsArray.push(ticketGroup);
    }
    isSaveDisabled(): boolean {
        const ticketsArray = this.groupFormGroup.get('ticketsArray') as FormArray;
        return this.groupFormGroup.invalid || ticketsArray.controls.some(control => !control.get('ticket')?.value);
    }
    deleteTicketGroup(index: number) {
        this.ticketsArray.removeAt(index);
    }
    ticketValidator(control: AbstractControl): { [key: string]: boolean } | null {
        const ticket = control.value;
        if (typeof ticket !== 'object' || !ticket.hasOwnProperty('value')) {
            return { 'invalidTicket': true };
        }
        return null;
    }

    populateFormFromGroup(group: Group) {
        this.groupFormGroup.patchValue({
            name: group.name,
            status: group.status,
        });

        const ticketsArray = this.groupFormGroup.get('ticketsArray') as FormArray;
        ticketsArray.clear();

        if (group.groupTickets && Array.isArray(group.groupTickets)) {
            group.groupTickets.forEach((groupTicket) => {
                const ticketId = groupTicket.ticketId;
                this.ticketService.getTicketById(ticketId).subscribe(response => {
                    if (response.statusCode === StatusCode.Ok) {
                        const ticket = response.objectValue;
                        const ticketValue = ticket ? { label: ticket.name, value: ticket } : ticketId;
                        const ticketGroup = this.formBuilder.group({
                            ticketType: ticket ? ticket.type : '',
                            ticket: [ticketValue, Validators.required],
                        });
                        ticketsArray.push(ticketGroup);
                        this.cdRef.detectChanges();
                    } else {
                        console.error("Ticket retrieval failed");
                    }
                }, error => {
                    console.error("Error fetching ticket details", error);
                });
            });
        } else {
            console.error('GroupTickets is undefined or not an array');
        }
    }
    getTicketById(id: string) {
        this.ticketService.getTicketById(id).subscribe(response => {
            if (response.statusCode === StatusCode.Ok) {
                this.ticketToUpdate = response.objectValue;
                console.log("response ok", this.ticketToUpdate);
            } else {
                console.log("response null");


            }
        }, (error) => {

        });

    }

    getGroupbyId(id: string) {
        this.groupService.getGroupById(id).subscribe(group => {
            this.groupFormGroup.setValue({
                name: group.objectValue.name,
                Status: group.objectValue.status
            });
        });
    }

    searchTickets(event: any) {
        this.ticketService.getTicketsByType(this.selectedTicketType).subscribe((response) => {
            if (response.statusCode == StatusCode.Ok) {
                const query = event.query.toLowerCase();
                this.ticketSuggestions = response.objectValue
                    .filter(ticket => ticket.name && ticket.name.toLowerCase().includes(query))
                    .map(ticket => ({
                        label: ticket.name,
                        value: ticket
                    }));
            }
            else {
                this.messageService.add({
                    key: 'toast',
                    severity: 'error',
                    summary: 'Tickets not Found',
                    detail: 'You must add tickets before adding group.'
                });
                this.closeAddDialogEvent.emit(false);
            }

        });
    }

    markFormGroupTouched(formGroup: FormGroup) {
        Object.values(formGroup.controls).forEach(control => {
            if (control instanceof FormGroup) {
                this.markFormGroupTouched(control);
            } else if (control instanceof FormArray) {
                control.controls.forEach((formArrayControl: AbstractControl) => {
                    if (formArrayControl instanceof FormGroup) {
                        this.markFormGroupTouched(formArrayControl);
                        formArrayControl.get('ticketType')?.markAsTouched();
                        formArrayControl.get('ticket')?.markAsTouched();
                    } else {
                        formArrayControl.markAsTouched();
                    }
                });
            } else {
                control.markAsTouched();
            }
        });
    }

    markTicketTypesTouched(): void {
        const ticketsArray: FormArray = this.groupFormGroup.get('ticketsArray') as FormArray;
        for (let index = 0; index < ticketsArray.length; index++) {
            const ticketGroup: FormGroup = ticketsArray.at(index) as FormGroup;
            const ticketTypeControl = ticketGroup.get('ticketType');
            ticketTypeControl?.markAsTouched();
            const ticketNameControl = ticketGroup.get('ticket');
            ticketNameControl?.markAsTouched();
        }
        this.cdRef.detectChanges();
    }
    async saveGroup() {
        // Mark all form controls as touched
        this.markTicketTypesTouched();
        Object.values(this.groupFormGroup.controls).forEach(control => {
            control.markAsTouched();
        });
      
  if(this.isSaveDisabled())
    {
        this.loading=false;
        return;
    }
    this.loading = true;
        if (this.mode === 'add') {
              const hasDuplicates = this.hasDuplicateTickets();
            const nameExist = await this.nameAlreadyExist(this.groupFormGroup.get('name')?.value);
            if (hasDuplicates) {
                this.messageService.add({
                    key: 'toast',
                    severity: 'error',
                    summary: 'Duplicate Tickets',
                    detail: 'You cannot add tickets with the same value.'
                });
                this.loading = false;
                this.closeAddDialogEvent.emit(false);

            }

            else if (nameExist) {
                this.messageService.add({
                    key: 'toast',
                    severity: 'error',
                    summary: 'Duplicate Name',
                    detail: 'You cannot add groups with the same name.'
                });
                this.loading = false;
                this.closeAddDialogEvent.emit(false);
            }
            else {
                let groupTickets = this.ticketsArray.controls.map(control => {
                    const ticketValue = control.value.ticket;
                    return ticketValue && ticketValue.value.ticketId
                        ? {
                            ticketId: ticketValue.value.ticketId
                        }
                        : null;
                })
                    .filter((ticket): ticket is GroupTicket => ticket !== null);

                const groupToAdd: Group = {
                    name: this.groupFormGroup.get('name')?.value,
                    employees: [],
                    status: this.groupFormGroup.get('status')?.value,
                    groupTickets
                };
                this.groupService.addGroup(groupToAdd).subscribe((response) => {
                    if (response.statusCode === StatusCode.Created) {
                        this.messageService.add({ key: 'toast', severity: 'success', summary: 'Group added successfully', detail: response.exceptionMessage });
                        this.groupAdded.emit();
                        this.loading = false;
                        this.closeAddDialogEvent.emit(false);
                    } else {
                        this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Add Group', detail: response.exceptionMessage });
                        this.loading = false;
                        this.closeAddDialogEvent.emit(false);
                    }
                });
            }
        } else if (this.mode === 'edit') {

            const nameExist = await this.nameAlreadyExist(this.groupFormGroup.get('name')?.value);
            const hasDuplicates = this.hasDuplicateTickets();
            if (hasDuplicates) {
                this.messageService.add({
                    key: 'toast',
                    severity: 'error',
                    summary: 'Duplicate Tickets',
                    detail: 'You cannot add tickets with the same value.'
                });
                this.loading = false;
                this.closeAddDialogEvent.emit(false);


            } else if (nameExist) {

                this.messageService.add({
                    key: 'toast',
                    severity: 'error',
                    summary: 'Duplicate Name',
                    detail: 'You cannot add groups with the same name.'
                });
                this.loading = false;
                this.closeAddDialogEvent.emit(false);
            }
            else {

                const formValues = this.groupFormGroup.value;
                let groupTickets: GroupTicket[] = formValues.ticketsArray
                    .filter((t: any) => t.ticket && t.ticket.value)
                    .map((t: any) => ({
                        ticketId: typeof t.ticket.value === 'object' ? t.ticket.value.ticketId : t.ticket.value,
                        groupId: this.selectedGroup?.groupId,
                    }));

                const groupToUpdate: Group = {
                    groupId: this.selectedGroup?.groupId,
                    name: this.groupFormGroup.get('name')?.value,
                    employees: [],
                    status: this.selectedStatus,
                    groupTickets,
                };

                this.groupService.updateGroup(groupToUpdate).subscribe((response) => {
                    if (response.statusCode === StatusCode.Ok) {
                        this.messageService.add({ key: 'toast', severity: 'success', summary: 'Group updated successfully', detail: response.exceptionMessage });
                        this.groupAdded.emit();
                        this.loading = false;
                        this.closeAddDialogEvent.emit(false);
                    } else {
                        this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Update Group', detail: response.exceptionMessage });
                        this.loading = false;
                    }
                });
            }
        }
    }

    nameAlreadyExist(name: string): Promise<boolean> {
        return new Promise<boolean>((resolve, reject) => {
            this.groupService.getAllGroups().subscribe((response) => {
                if (response.statusCode === StatusCode.Ok) {
                    debugger;
                    const groups = response.objectValue;
                    let exists: boolean;
                    if (this.mode === 'edit') {
                        exists = groups
                            .filter(group => group.groupId !== this.selectedGroup?.groupId)
                            .some(group => group.name.toLowerCase() === name.toLowerCase());
                    } else {
                        // Check for name existence without excluding any group
                        exists = groups.some(group => group.name.toLowerCase() === name.toLowerCase());
                    }
                    resolve(exists);
                } else {
                    reject('Failed to fetch groups');
                }
            }, (error) => {
                reject(error);
            });
        });
    }


    hasDuplicateTickets(): boolean {
        const ticketsArray = this.groupFormGroup.get('ticketsArray') as FormArray;
        const ticketIds: string[] = [];

        for (const control of ticketsArray.controls) {
            const ticketValue = control.get('ticket')?.value;

            if (ticketValue && typeof ticketValue === 'object' && ticketValue.hasOwnProperty('value') && ticketValue.value) {
                const ticketId = ticketValue.value.ticketId;
                if (ticketId) {
                    ticketIds.push(ticketId);
                }
            } else if (typeof ticketValue === 'string') {
                ticketIds.push(ticketValue);
            }
        }

        const uniqueIds = new Set<string>();
        for (const id of ticketIds) {
            if (uniqueIds.has(id)) {
                return true; // Duplicate found
            }
            uniqueIds.add(id);
        }

        return false; // No duplicates found
    }

    onStatusChange(event: any) {
        this.selectedStatus = event.value;
    }


    onTicketTypeChange(event: any, groupIndex: number) {
        this.selectedTicketType = event.value;
        this.getTicketsByType(this.selectedTicketType);

        const ticketsArray = this.groupFormGroup.get('ticketsArray') as FormArray;
        const ticketControl = ticketsArray.at(groupIndex)?.get('ticket');
        if (ticketControl) {
            ticketControl.reset();
        } else {
        }
    }

    onTicketChange(event: any) {
        debugger
        const ticketsArray = this.groupFormGroup.get('ticketsArray') as FormArray;
        const ticketControl = ticketsArray?.get('ticket');
        ticketControl?.markAsTouched();
        
        if (ticketControl?.value &&!this.isValidTicket) {
            
            if (ticketControl && !this.ticketSuggestions.includes(ticketControl.value)) {
                // If ticket is not included in ticket suggestions
                ticketControl.setErrors({ 'invalidTicket': true });
            }
        }

    }

    getTicketsByType(ticketType: WalletType) {
        this.ticketService.getTicketsByType(ticketType).subscribe((response) => {
            if (response.statusCode === StatusCode.Ok) {
                this.ticketSuggestions = response.objectValue.map((ticket: Ticket) => ({
                    label: ticket.name,
                    value: ticket
                }));
            } else {
                this.ticketSuggestions = [];
            }
        }, (error) => {
            this.ticketSuggestions = [];
        });
    }
    onTicketSelect(event: any) {
        this.selectedTicket = event.value;
        console.log("Selected Ticket:", this.selectedTicket);
    }

    closeAddDialog() {
        this.closeAddDialogEvent.emit(false);
    }
}

function uuidv4(): string | undefined {
    throw new Error('Function not implemented.');
}

