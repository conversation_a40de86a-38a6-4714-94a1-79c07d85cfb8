<div class="grid">
    <div class="col-12 md:col-6 xl:col-6">
        <div class="card h-full">
            <i class="h-6 w-1 pi pi-wallet" style="font-size: 1.5rem"></i>
            <span class="font-semibold text-lg">Wallet Balance</span>
            <div class="flex justify-content-between align-items-start mt-3">
                <div class="w-6">
                    <span class="text-4xl font-bold text-900">{{ wallet.balance }} </span>

                </div>
                <div class="w-6">
                    <!-- <svg width="100%" viewBox="0 0 258 96" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 93.9506L4.5641 94.3162C8.12821 94.6817 15.2564 95.4128 22.3846 89.6451C29.5128 83.8774 36.641 71.6109 43.7692 64.4063C50.8974 57.2018 58.0256 55.0592 65.1538 58.9268C72.2821 62.7945 79.4103 72.6725 86.5385 73.5441C93.6667 74.4157 100.795 66.2809 107.923 65.9287C115.051 65.5765 122.179 73.0068 129.308 66.8232C136.436 60.6396 143.564 40.8422 150.692 27.9257C157.821 15.0093 164.949 8.97393 172.077 6.43766C179.205 3.9014 186.333 4.86425 193.462 12.0629C200.59 19.2616 207.718 32.696 214.846 31.0487C221.974 29.4014 229.103 12.6723 236.231 5.64525C243.359 -1.38178 250.487 1.29325 254.051 2.63076L257.615 3.96827" style="stroke-width:2px;stroke:var(--primary-color)" stroke="10"/>
                    </svg> -->
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 md:col-6 xl:col-6" *ngIf="usertype !== 'ShopOwner'">
        <div class="card h-full">
            <i class="h-6 w-1 pi pi-wallet" style="font-size: 1.5rem"></i>
            <span class="font-semibold text-lg">Wallet Balance Next Month</span>
            <div class="flex justify-content-between align-items-start mt-3">
                <div class="w-6">
                    <span class="text-4xl font-bold text-900">{{ wallet!.balance-100 }} </span>
                    <div class="text-green-500">
                        <!-- <span class="font-medium">+20%</span>
                        <i class="pi pi-arrow-up text-xs ml-2"></i> -->
                    </div>
                </div>
                <div class="w-6">
                    <!-- <svg width="100%" viewBox="0 0 115 41" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 35.6498L2.24444 32.4319C3.48889 29.214 5.97778 22.7782 8.46667 20.3627C10.9556 17.9473 13.4444 19.5522 15.9333 21.7663C18.4222 23.9803 20.9111 26.8035 23.4 30.6606C25.8889 34.5176 28.3778 39.4085 30.8667 37.2137C33.3556 35.0189 35.8444 25.7383 38.3333 26.3765C40.8222 27.0146 43.3111 37.5714 45.8 38.9013C48.2889 40.2311 50.7778 32.3341 53.2667 31.692C55.7556 31.0499 58.2444 37.6628 60.7333 39.4617C63.2222 41.2607 65.7111 38.2458 68.2 34.9205C70.6889 31.5953 73.1778 27.9597 75.6667 23.5955C78.1556 19.2313 80.6444 14.1385 83.1333 13.8875C85.6222 13.6365 88.1111 18.2272 90.6 20.2425C93.0889 22.2578 95.5778 21.6977 98.0667 18.8159C100.556 15.9341 103.044 10.7306 105.533 7.37432C108.022 4.01806 110.511 2.50903 111.756 1.75451L113 1" style="stroke-width:1px;stroke:var(--primary-color)"/>
                    </svg> -->
                </div>
            </div>
        </div>
    </div>


    <div class="col-12 xl:col-12 ">
        <div class="card h-full">
            <div class="text-900 text-xl font-semibold mb-3">
                <span class="fas fa-bell fa-m" style="color: #f22c2c;"></span>
                Notifications
            </div>
            <app-notification></app-notification>
        </div>
    </div>

    <div class="col-12 " *ngIf="usertype == 'ShopOwner'">
        <div class="card">
            <h5>List Of Cashers</h5>
            <p-table #dt1 [value]="users" dataKey="id" [rows]="pageSize" [loading]="loadingusers" [rowHover]="true"
                styleClass="p-datatable-gridlines" [paginator]="true"
                [globalFilterFields]="['name','id','email','phonenumber']" responsiveLayout="scroll" 
                [totalRecords]="totalRecords" [first]="first" 
                [lazy]="true" (onLazyLoad)="lazyLoadCashiers($event)">
                <ng-template pTemplate="caption">
                    <div>
                        <p-button label="" icon="pi pi-filter-slash" outlined="true" [pTooltip]="'Clear Filters'"
                            (click)="clear(dt1)" [style]="{'margin-right.px': '10'}"
                            styleClass="p-button-outlined "><span class="tooltip"></span></p-button>

                    </div>
                </ng-template>

                <ng-template pTemplate="header">
                    <tr>
                        <th style="min-width: 12rem">
                            
                            <div class="flex justify-content-between align-items-center">
                                Badget
                            </div>
                        </th>
                        <th style="min-width: 12rem">
                            
                            <div class="flex justify-content-between align-items-center">
                                Name
                                <!-- <p-columnFilter type="text" field="name" display="menu"
                                    placeholder="Search by name"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem">
                            <div class="flex justify-content-between align-items-center">
                                Email
                                <!-- <p-columnFilter type="text" field="email" display="menu"
                                    placeholder="Search by email"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem">
                            <div class="flex justify-content-between align-items-center">
                                Gender
                            </div>
                        </th>
                        <th style="min-width: 10rem">
                            <div class="flex justify-content-between align-items-center">
                                Phone Number
                            </div>
                        </th>
                        <th style="min-width: 10rem">
                            <div class="flex justify-content-between align-items-center">
                                Status
                            </div>
                        </th>


                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-User>
                    <tr>
                        <td>
                            {{User.userName}}
                        </td>
                        <td>
                            {{User.fullName}}
                        </td>
                        <td>
                            {{User.email}}
                        </td>
                        <td>{{ User.gender === 0 ? 'Male' : User.gender === 1 ? 'Female' : 'other' }}</td>
                        <td>{{ User.phoneNumber }}</td>
                        <!-- <td>
                            <span *ngFor="let role of User.roles;let last = last">
                                <p-chip class="mr-2">{{ role.name }}</p-chip>
                                <span *ngIf="!last">,</span>
                            </span>
                        </td> -->
                        <td>
                            <span [class]="'component-badge status-' + getStatusString(User.status)">
                                {{ getStatusString(User.status) }}
                            </span>
                        </td>


                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="8">No Users found.</td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="loadingbody">
                    <tr>
                        <td colspan="8">Loading Users data. Please wait.</td>
                    </tr>
                </ng-template>
            </p-table>
        </div>
    </div>
    <div class="col-12 " *ngIf="usertype !== 'ShopOwner'">
        <div class="card">
            <h5>List of employees</h5>
            <!-- <p-toast></p-toast> -->
            <p-table [value]="groups" dataKey="name" [expandedRowKeys]="expandedRows" responsiveLayout="scroll">
                <ng-template pTemplate="caption">
                    <p-button icon="pi pi-fw {{isExpanded ? 'pi-minus' : 'pi-plus'}}" outlined="true"
                        [pTooltip]="'Expand Or Collapse All Employees'"
                        label="{{isExpanded ? 'Collapse All' : 'Expand All'}}" (click)="expandAll()"
                        [style]="{'margin-right.px': '10'}" styleClass="p-button-outlined "><span
                            class="tooltip"></span></p-button>

                    <div class="flex table-header">
                    </div>
                </ng-template>
                <ng-template pTemplate="header">
                    <tr>
                        <th style="width: 3rem"></th>
                        <th pSortableColumn="name">Group Name </th>
                        <!-- <th>Image</th> -->
                        <th pSortableColumn="price">Number of employees </th>
                        <!-- <th pSortableColumn="category">Category <p-sortIcon field="category"></p-sortIcon></th> -->
                        <!-- <th pSortableColumn="rating">Reviews <p-sortIcon field="rating"></p-sortIcon></th> -->
                        <th pSortableColumn="inventoryStatus">Status
                        </th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-group let-expanded="expanded">
                    <tr>
                        <td>
                            <button type="button" pButton pRipple [pRowToggler]="group"
                                class="p-button-text p-button-rounded p-button-plain"
                                [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"></button>
                        </td>
                        <td style="min-width: 12rem;">{{group.name}}</td>
                        <!-- <td><img [src]="'assets/demo/images/product/' + product.image" [alt]="product.name" width="100" class="shadow-4" /></td> -->
                        <td style="min-width: 8rem;">{{group.employees.length}}</td>
                        <!-- <td style="min-width: 10rem;">{{product.category}}</td> -->
                        <!-- <td style="min-width: 10rem;"><p-rating [ngModel]="product.rating" [readonly]="true" [cancel]="false"></p-rating></td> -->
                        <td><span
                                [class]="'component-badge status-' + getStatusString(group.status)">{{getStatusString(group.status)}}</span>
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="rowexpansion" let-group>
                    <tr>
                        <td colspan="7">
                            <div class="p-3">
                                <p-table [value]="group.employees" dataKey="id" responsiveLayout="scroll">
                                    <ng-template pTemplate="header">
                    <tr>
                        <th pSortableColumn="customer">Employee </th>
                        <th pSortableColumn="date">Email </th>
                        <th pSortableColumn="amount">Phone Number </th>
                        <th pSortableColumn="status">Status </th>

                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-employe>
                    <tr>
                        <td>{{employe.user.fullName}}
                        </td>
                        <td>{{employe.user.email}}</td>
                        <td>{{employe.user.phoneNumber }}</td>
                        <td><span
                                [class]="'component-badge status-' + getStatusString(employe.status)">{{getStatusString(employe.status)}}</span>
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="6">There are no employees for Group.</td>
                    </tr>
                </ng-template>
            </p-table>
        </div>
        </td>
        </tr>
        </ng-template>
        </p-table>
    </div>
</div>



</div>