import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LoginComponent } from './login.component';
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { FormsModule } from '@angular/forms';
import { AppConfigModule } from 'src/app/layout/config/app.config.module';
import { AuthentificationRoutingModule } from '../authentification-routing.module';
import { MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';

import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { PasswordModule } from 'primeng/password';
import { TermsAndConditionsDialogComponent } from 'src/app/shared/components/terms-and-conditions/terms-and-conditions-dialog.component';
@NgModule({
    imports: [
        CommonModule,
        AuthentificationRoutingModule,
        ButtonModule,
        InputTextModule,
        CheckboxModule,
        FormsModule,
        AppConfigModule,
        ToastModule,
        ProgressSpinnerModule,
        PasswordModule,

    ],
    declarations: [LoginComponent,  TermsAndConditionsDialogComponent],
    providers:[MessageService]
})
export class LoginModule { }
