<p-dialog [header]="mode =='add'? 'Add New Ticket' : 'Edit Ticket'" [(visible)]="display" [modal]="true"
    showEffect="fade" [style]="{width: '60vw'}" [breakpoints]="{'960px': '75vw'}" (onHide)="closeAddDialog()">
    <form [formGroup]="ticketFormGroup">
        <div class="col-12">
            <div class="card">
                <div class="row">
                    <div class="col-4 mb-4 lg:col-4 lg:mb-0 input-name">
                        <h5>Name<span style="color: red;">*</span></h5>
                        <input type="text" class="input-width" pInputText placeholder="Name" formControlName="name"
                            (change)="onNameChange($event)">
                        <p-message severity="error" text="Name is required"
                            *ngIf="ticketFormGroup.get('name')?.hasError('required') && ticketFormGroup.get('name')?.touched">
                        </p-message>
                    </div>
                    <div class="col-4 mb-4 lg:col-4 lg:mb-0 input-padding-left input-padding-right">
                        <h5>Ticket Type</h5>
                        <p-dropdown [options]="ticketTypeDropdown" (onChange)="onTicketTypeChange($event)"
                            optionLabel="label" optionValue="value" formControlName="ticketType"></p-dropdown>
                    </div>
                    <div class="col-4 mb-4 lg:col-4 lg:mb-0 input-padding-left">
                        <h5>Status</h5>
                        <p-dropdown [options]="statusDropdown" (onChange)="onStatusChange($event)" optionLabel="label"
                            optionValue="value" formControlName="status"></p-dropdown>
                    </div>

                </div>

                <div class="row">
                    <div class="col-4 mb-4 lg:col-4 lg:mb-0">
                        <h5>Ticket Amount<span style="color: red;">*</span></h5>
                        <input type="number" class="input-width text-right" pInputText placeholder="Amount"
                            formControlName="amount" (change)="onAmountChange($event)">
                        <p-message severity="error" text="Amount is required"
                            *ngIf="ticketFormGroup.get('amount')?.hasError('required') && ticketFormGroup.get('amount')?.touched">
                        </p-message>
                        <p-message severity="error" text="Amount should be a positive number"
                            *ngIf="ticketFormGroup.get('amount')?.hasError('pattern') && ticketFormGroup.get('amount')?.touched">
                        </p-message>
                    </div>
                    <div class="col-4 mb-4 lg:col-4 lg:mb-0 input-padding-left input-padding-right">
                        <h5>Quantity<span style="color: red;">*</span></h5>
                        <input type="number" class="input-width text-right" pInputText placeholder="Quantity"
                            formControlName="quantity" (change)="onQuantityChange($event)">
                        <p-message severity="error" text="Quantity is required"
                            *ngIf="ticketFormGroup.get('quantity')?.hasError('required') && ticketFormGroup.get('quantity')?.touched">
                        </p-message>
                        <p-message severity="error" text="Quantity should be a positive integer"
                            *ngIf="ticketFormGroup.get('quantity')?.hasError('pattern') && ticketFormGroup.get('quantity')?.touched">
                        </p-message>
                    </div>
                    <div class="col-4 mb-4 lg:col-4 lg:mb-0 input-padding-left">
                        <h5>Total Amount</h5>
                        <input type="number" class="input-width text-right" pInputText placeholder="Total Amount"
                            formControlName="totalAmount" [value]="totalAmount">
                    </div>
                </div>
                <div class="row display-none">
                    <div class="col-4 mb-4 lg:col-4 lg:mb-0">
                        <h5>Period</h5>
                        <p-dropdown [options]="PeriodDropdown" (onChange)="onPeriodChange($event)" optionLabel="label"
                            optionValue="value" formControlName="periodType"></p-dropdown>
                    </div>
                    <div class="col-3 mb-3 lg:col-3 lg:mb-0 input-padding-left">
                        <h5>Automatic</h5>
                        <div class="isAutomaticSwitch">
                            <p-inputSwitch formControlName="isAutomatic"
                                (onChange)="onIsAutomaticChange($event)"></p-inputSwitch>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-4 mb-4 lg:col-4 lg:mb-0" *ngIf="displayDates">
                        <h5>Start Date</h5>
                        <p-calendar [showIcon]="true" inputId="icon" placeholder="dd/mm/yyyy" [minDate]="startDateMin"
                            [maxDate]="startDateMax" (onInput)="onStartDateChange($event)"
                            (onSelect)="onStartDateChange($event)" formControlName="startDate"
                            dateFormat="dd M yy"></p-calendar>
                        <p-message severity="error" text="Start Date is required"
                            *ngIf="ticketFormGroup.get('startDate')?.value==null && ticketFormGroup.get('startDate')?.touched">
                        </p-message>
                    </div>
                    <div class="col-4 mb-4 lg:col-4 lg:mb-0 input-padding-left" *ngIf="displayDates">
                        <h5>End Date</h5>
                        <p-calendar [showIcon]="true" inputId="icon" placeholder="dd/mm/yyyy" dateFormat="dd M yy"
                            [minDate]="endDateMin" (onInput)="onEndDateChange()" (onSelect)="onEndDateChange()"
                            formControlName="endDate"></p-calendar>
                    </div>

                </div>
            </div>
        </div>
    </form>
    <ng-template pTemplate="footer" class="footer">
        <button pButton (click)="closeAddDialog()" label="Cancel" class="p-button-outlined "></button>
        <button pButton (click)="saveTicket()" class="p-button-outlined p-button-success"
            [disabled]="loading">
            <span *ngIf="!loading"

            >Save</span>
           <p-progressSpinner *ngIf="loading"  styleClass="w-3rem h-1rem" strokeWidth="6"></p-progressSpinner>
        </button>
        <p-toast key="toast"></p-toast>
    </ng-template>
</p-dialog>
