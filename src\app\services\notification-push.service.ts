import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, Subject, map, catchError, tap, filter } from 'rxjs';
import { 
  AppNotification, 
  NotificationFilter, 
  NotificationPreferences, 
  NotificationStats,
  BulkNotificationRequest,
  NotificationTemplate,
  NotificationType,
  NotificationPriority,
  NotificationCategory
} from '../shared/models/notification/notification';
import { BaseCrudService } from '../shared/services/base-crud.service';
import { ConfigService } from './config.service';
import { ErrorHandlerService } from './error-handler.service';
import { LocalStoreService } from './local-store.service';
import { ResponseAPI } from '../shared/models/ResponseAPI';
import { PaginationRequest, PaginationResponse } from '../shared/models/pagination/pagination-request';

@Injectable({
  providedIn: 'root'
})
export class NotificationPushService extends BaseCrudService<AppNotification> {
  protected readonly endpoint = 'Notification';

  // État des notifications
  private notificationsSubject = new BehaviorSubject<AppNotification[]>([]);
  private unreadCountSubject = new BehaviorSubject<number>(0);
  private newNotificationSubject = new Subject<AppNotification>();

  // Configuration WebSocket/SignalR
  private connection: any;
  private isConnected = false;

  // Cache des préférences utilisateur
  private userPreferences: NotificationPreferences | null = null;

  constructor(
    http: HttpClient,
    config: ConfigService,
    errorHandler: ErrorHandlerService,
    private localStore: LocalStoreService
  ) {
    super(http, config, errorHandler);
    this.initializeRealTimeConnection();
    this.loadUserPreferences();
  }

  // Observables publics
  get notifications$(): Observable<AppNotification[]> {
    return this.notificationsSubject.asObservable();
  }

  get unreadCount$(): Observable<number> {
    return this.unreadCountSubject.asObservable();
  }

  get newNotification$(): Observable<AppNotification> {
    return this.newNotificationSubject.asObservable();
  }

  /**
   * Récupère les notifications de l'utilisateur avec pagination
   */
  getUserNotifications(filter: NotificationFilter, pagination: PaginationRequest): Observable<PaginationResponse<AppNotification>> {
    const requestBody = {
      ...pagination,
      filter: filter
    };

    return this.http.post<ResponseAPI<AppNotification[]>>(
      this.getFullUrl('GetUserNotifications'),
      requestBody,
      { observe: 'response', responseType: 'json' }
    ).pipe(
      map(response => {
        const data = response.body?.objectValue || [];
        const paginationInfo = this.extractPaginationFromHeaders(response.headers);
        
        return {
          data,
          totalCount: paginationInfo.totalCount || 0,
          pageNumber: paginationInfo.pageNumber || pagination.pageNumber,
          pageSize: paginationInfo.pageSize || pagination.pageSize,
          totalPages: paginationInfo.totalPages || 1,
          hasNextPage: paginationInfo.hasNextPage || false,
          hasPreviousPage: paginationInfo.hasPreviousPage || false
        } as PaginationResponse<AppNotification>;
      }),
      tap(response => {
        // Mettre à jour le cache local
        this.notificationsSubject.next(response.data);
        this.updateUnreadCount();
      }),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Marque une notification comme lue
   */
  markAsRead(notificationId: string): Observable<void> {
    return this.http.put<ResponseAPI<void>>(
      this.getFullUrl(`MarkAsRead/${notificationId}`),
      {}
    ).pipe(
      map(() => {
        this.updateNotificationInCache(notificationId, { isRead: true, readAt: new Date() });
        this.updateUnreadCount();
        return void 0;
      }),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Marque toutes les notifications comme lues
   */
  markAllAsRead(): Observable<void> {
    return this.http.put<ResponseAPI<void>>(
      this.getFullUrl('MarkAllAsRead'),
      {}
    ).pipe(
      map(() => {
        const notifications = this.notificationsSubject.value;
        const updatedNotifications = notifications.map(n => ({
          ...n,
          isRead: true,
          readAt: new Date()
        }));
        this.notificationsSubject.next(updatedNotifications);
        this.unreadCountSubject.next(0);
        return void 0;
      }),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Archive une notification
   */
  archiveNotification(notificationId: string): Observable<void> {
    return this.http.put<ResponseAPI<void>>(
      this.getFullUrl(`Archive/${notificationId}`),
      {}
    ).pipe(
      map(() => {
        this.updateNotificationInCache(notificationId, { isArchived: true });
        return void 0;
      }),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Supprime une notification
   */
  deleteNotification(notificationId: string): Observable<void> {
    return this.http.delete<ResponseAPI<void>>(
      this.getFullUrl(`Delete/${notificationId}`)
    ).pipe(
      map(() => {
        this.removeNotificationFromCache(notificationId);
        this.updateUnreadCount();
        return void 0;
      }),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Envoie une notification à un utilisateur spécifique
   */
  sendToUser(userId: string, notification: Partial<AppNotification>): Observable<AppNotification> {
    const requestBody = {
      userId,
      ...notification
    };

    return this.http.post<ResponseAPI<AppNotification>>(
      this.getFullUrl('SendToUser'),
      requestBody
    ).pipe(
      map(response => response.objectValue!),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Envoie des notifications en masse
   */
  sendBulkNotification(request: BulkNotificationRequest): Observable<{ sent: number; failed: number }> {
    return this.http.post<ResponseAPI<{ sent: number; failed: number }>>(
      this.getFullUrl('SendBulk'),
      request
    ).pipe(
      map(response => response.objectValue!),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Récupère les préférences de notification de l'utilisateur
   */
  getUserPreferences(): Observable<NotificationPreferences> {
    return this.http.get<ResponseAPI<NotificationPreferences>>(
      this.getFullUrl('GetUserPreferences')
    ).pipe(
      map(response => {
        this.userPreferences = response.objectValue!;
        return this.userPreferences;
      }),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Met à jour les préférences de notification
   */
  updateUserPreferences(preferences: NotificationPreferences): Observable<NotificationPreferences> {
    return this.http.put<ResponseAPI<NotificationPreferences>>(
      this.getFullUrl('UpdateUserPreferences'),
      preferences
    ).pipe(
      map(response => {
        this.userPreferences = response.objectValue!;
        this.localStore.saveData('NotificationPreferences', JSON.stringify(this.userPreferences));
        return this.userPreferences;
      }),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Récupère les statistiques de notifications
   */
  getNotificationStats(): Observable<NotificationStats> {
    return this.http.get<ResponseAPI<NotificationStats>>(
      this.getFullUrl('GetStats')
    ).pipe(
      map(response => response.objectValue!),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Récupère les templates de notifications
   */
  getTemplates(): Observable<NotificationTemplate[]> {
    return this.http.get<ResponseAPI<NotificationTemplate[]>>(
      this.getFullUrl('GetTemplates')
    ).pipe(
      map(response => response.objectValue || []),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Teste l'envoi d'une notification
   */
  testNotification(notification: Partial<AppNotification>): Observable<void> {
    return this.http.post<ResponseAPI<void>>(
      this.getFullUrl('Test'),
      notification
    ).pipe(
      map(() => void 0),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  // Méthodes de connexion temps réel
  private async initializeRealTimeConnection(): Promise<void> {
    if (!this.config.isNotificationsEnabled) {
      return;
    }

    try {
      // Ici vous pourriez utiliser SignalR ou WebSocket
      // Exemple avec SignalR:
      // const { HubConnectionBuilder } = await import('@microsoft/signalr');
      // this.connection = new HubConnectionBuilder()
      //   .withUrl(this.config.getFullNotificationUrl('notificationHub'))
      //   .build();

      // this.connection.on('ReceiveNotification', (notification: AppNotification) => {
      //   this.handleNewNotification(notification);
      // });

      // await this.connection.start();
      // this.isConnected = true;

      console.log('Connexion temps réel pour les notifications initialisée');
    } catch (error) {
      console.error('Erreur lors de l\'initialisation de la connexion temps réel:', error);
    }
  }

  private handleNewNotification(notification: AppNotification): void {
    // Vérifier les préférences utilisateur
    if (!this.shouldShowNotification(notification)) {
      return;
    }

    // Ajouter à la liste des notifications
    const currentNotifications = this.notificationsSubject.value;
    const updatedNotifications = [notification, ...currentNotifications];
    this.notificationsSubject.next(updatedNotifications);

    // Mettre à jour le compteur non lues
    this.updateUnreadCount();

    // Émettre la nouvelle notification
    this.newNotificationSubject.next(notification);

    // Afficher une notification native si supportée
    this.showNativeNotification(notification);
  }

  private shouldShowNotification(notification: AppNotification): boolean {
    if (!this.userPreferences) {
      return true; // Afficher par défaut si pas de préférences
    }

    // Vérifier les préférences générales
    if (!this.userPreferences.inAppNotifications) {
      return false;
    }

    // Vérifier les préférences par catégorie
    if (this.userPreferences.categories[notification.category] === false) {
      return false;
    }

    // Vérifier les préférences par priorité
    if (this.userPreferences.priorities[notification.priority] === false) {
      return false;
    }

    // Vérifier les heures de silence
    if (this.userPreferences.quietHours?.enabled) {
      const now = new Date();
      const currentTime = now.getHours() * 60 + now.getMinutes();
      const startTime = this.parseTime(this.userPreferences.quietHours.startTime);
      const endTime = this.parseTime(this.userPreferences.quietHours.endTime);

      if (startTime <= endTime) {
        // Même jour
        if (currentTime >= startTime && currentTime <= endTime) {
          return false;
        }
      } else {
        // Traverse minuit
        if (currentTime >= startTime || currentTime <= endTime) {
          return false;
        }
      }
    }

    return true;
  }

  private showNativeNotification(notification: AppNotification): void {
    if ('Notification' in window && Notification.permission === 'granted') {
      const nativeNotification = new Notification(notification.title, {
        body: notification.message,
        icon: notification.imageUrl || '/assets/icons/notification-icon.png',
        tag: notification.id,
        requireInteraction: notification.priority === NotificationPriority.URGENT || notification.priority === NotificationPriority.CRITICAL
      });

      nativeNotification.onclick = () => {
        if (notification.actionUrl) {
          window.open(notification.actionUrl, '_blank');
        }
        nativeNotification.close();
      };

      // Auto-fermer après 5 secondes sauf pour les notifications urgentes
      if (notification.priority !== NotificationPriority.URGENT && notification.priority !== NotificationPriority.CRITICAL) {
        setTimeout(() => nativeNotification.close(), 5000);
      }
    }
  }

  // Méthodes utilitaires
  private updateNotificationInCache(notificationId: string, updates: Partial<AppNotification>): void {
    const notifications = this.notificationsSubject.value;
    const updatedNotifications = notifications.map(n => 
      n.id === notificationId ? { ...n, ...updates } : n
    );
    this.notificationsSubject.next(updatedNotifications);
  }

  private removeNotificationFromCache(notificationId: string): void {
    const notifications = this.notificationsSubject.value;
    const filteredNotifications = notifications.filter(n => n.id !== notificationId);
    this.notificationsSubject.next(filteredNotifications);
  }

  private updateUnreadCount(): void {
    const notifications = this.notificationsSubject.value;
    const unreadCount = notifications.filter(n => !n.isRead && !n.isArchived).length;
    this.unreadCountSubject.next(unreadCount);
  }

  private loadUserPreferences(): void {
    const savedPreferences = this.localStore.getData('NotificationPreferences');
    if (savedPreferences) {
      try {
        this.userPreferences = JSON.parse(savedPreferences);
      } catch {
        this.userPreferences = null;
      }
    }
  }

  private parseTime(timeString: string): number {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
  }

  private extractPaginationFromHeaders(headers: any): any {
    const xPaginationHeader = headers.get('x-pagination');
    if (xPaginationHeader) {
      try {
        return JSON.parse(xPaginationHeader);
      } catch {
        return {};
      }
    }
    return {};
  }

  // Méthodes de nettoyage
  disconnect(): void {
    if (this.connection && this.isConnected) {
      this.connection.stop();
      this.isConnected = false;
    }
  }

  /**
   * Demande la permission pour les notifications natives
   */
  async requestNotificationPermission(): Promise<NotificationPermission> {
    if ('Notification' in window) {
      return await Notification.requestPermission();
    }
    return 'denied';
  }
}
