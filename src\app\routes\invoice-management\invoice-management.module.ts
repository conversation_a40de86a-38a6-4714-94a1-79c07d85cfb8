import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from 'src/app/shared/Modules/shared-module/shared-module.module';
import { InvoiceManagementRoutingModule } from './invoice-management-routing.module';
import { ValidInvoicesComponent } from './valid-invoices/valid-invoices.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ConfirmationService, MessageService} from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { InputSwitchModule } from 'primeng/inputswitch';
import { InputTextModule } from 'primeng/inputtext';
import { MessageModule } from 'primeng/message';
import { MultiSelectModule } from 'primeng/multiselect';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { ProgressBarModule } from 'primeng/progressbar';
import { RatingModule } from 'primeng/rating';
import { RippleModule } from 'primeng/ripple';
import { SliderModule } from 'primeng/slider';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { ToggleButtonModule } from 'primeng/togglebutton';
import { ConfirmationDialogueModule } from 'src/app/shared/components/confirmation-dialog/confirmation-dialogue.module';
import { InvoiceService } from 'src/app/services/invoice.service';
import { InvoicePdfService } from 'src/app/services/invoice-pdf.service';
import { TooltipModule } from 'primeng/tooltip';


@NgModule({
  declarations: [
    ValidInvoicesComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
        FormsModule,
        TableModule,
        RatingModule,
        ButtonModule,
        SliderModule,
        InputTextModule,
        ToggleButtonModule,
        RippleModule,
        MultiSelectModule,
        DropdownModule,
        ProgressBarModule,
        ToastModule,
        ConfirmationDialogueModule,
        ReactiveFormsModule,
        DialogModule,
        CommonModule,
        ReactiveFormsModule,
        TableModule,
        RatingModule,
        ButtonModule,
        SliderModule,
        InputTextModule,
        ToggleButtonModule,
        RippleModule,
        MultiSelectModule,
        DropdownModule,
        ProgressBarModule,
        DialogModule,
        MessageModule,
        InputSwitchModule,
        CalendarModule,
        OverlayPanelModule,
        ConfirmDialogModule,
        ConfirmPopupModule,
        InvoiceManagementRoutingModule,
        TooltipModule
 
  ],
  providers: [ConfirmationService, MessageService, InvoiceService, InvoicePdfService]
})
export class InvoiceManagementModule { }
