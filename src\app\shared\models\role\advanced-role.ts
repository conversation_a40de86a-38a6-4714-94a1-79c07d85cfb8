export interface AdvancedRole {
  id?: string;
  name: string;
  displayName: string;
  description: string;
  isSystemRole: boolean;
  isActive: boolean;
  permissions: Permission[];
  userCount?: number;
  createdBy?: string;
  createdAt?: Date;
  updatedBy?: string;
  updatedAt?: Date;
  hierarchy?: number; // Niveau hiérarchique (0 = plus élevé)
  parentRoleId?: string;
  childRoles?: AdvancedRole[];
  restrictions?: RoleRestriction[];
}

export interface Permission {
  id?: string;
  name: string;
  displayName: string;
  description: string;
  module: string;
  action: PermissionAction;
  resource: string;
  conditions?: PermissionCondition[];
  isGranted: boolean;
  grantedBy?: string;
  grantedAt?: Date;
}

export enum PermissionAction {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  APPROVE = 'approve',
  REJECT = 'reject',
  EXPORT = 'export',
  IMPORT = 'import',
  MANAGE = 'manage',
  EXECUTE = 'execute'
}

export interface PermissionCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than' | 'in' | 'not_in';
  value: any;
  description?: string;
}

export interface RoleRestriction {
  type: RestrictionType;
  value: any;
  description: string;
}

export enum RestrictionType {
  TIME_BASED = 'time_based',
  IP_BASED = 'ip_based',
  LOCATION_BASED = 'location_based',
  DEVICE_BASED = 'device_based',
  DATA_SCOPE = 'data_scope'
}

export interface RoleTemplate {
  id: string;
  name: string;
  description: string;
  permissions: string[]; // IDs des permissions
  restrictions?: RoleRestriction[];
  targetUserTypes: string[];
}

export interface RoleAssignment {
  id?: string;
  userId: string;
  roleId: string;
  assignedBy: string;
  assignedAt: Date;
  expiresAt?: Date;
  isActive: boolean;
  conditions?: AssignmentCondition[];
}

export interface AssignmentCondition {
  type: 'temporary' | 'conditional' | 'delegated';
  parameters: { [key: string]: any };
  description: string;
}

export interface RoleHierarchy {
  roleId: string;
  roleName: string;
  level: number;
  parentId?: string;
  children: RoleHierarchy[];
  permissions: Permission[];
  inheritedPermissions: Permission[];
}

export interface RoleAnalytics {
  roleId: string;
  roleName: string;
  userCount: number;
  activeUserCount: number;
  permissionCount: number;
  lastUsed?: Date;
  usageFrequency: number;
  riskScore: number; // Score de risque basé sur les permissions
  complianceStatus: 'compliant' | 'warning' | 'violation';
}

export interface RoleConflict {
  type: 'permission_overlap' | 'hierarchy_violation' | 'segregation_violation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedRoles: string[];
  affectedPermissions?: string[];
  recommendation: string;
}

export class RoleBuilder {
  private role: Partial<AdvancedRole> = {
    permissions: [],
    restrictions: [],
    isActive: true,
    isSystemRole: false
  };

  static create(): RoleBuilder {
    return new RoleBuilder();
  }

  name(name: string, displayName?: string): RoleBuilder {
    this.role.name = name;
    this.role.displayName = displayName || name;
    return this;
  }

  description(description: string): RoleBuilder {
    this.role.description = description;
    return this;
  }

  systemRole(isSystem: boolean = true): RoleBuilder {
    this.role.isSystemRole = isSystem;
    return this;
  }

  hierarchy(level: number, parentRoleId?: string): RoleBuilder {
    this.role.hierarchy = level;
    this.role.parentRoleId = parentRoleId;
    return this;
  }

  addPermission(permission: Permission): RoleBuilder {
    this.role.permissions!.push(permission);
    return this;
  }

  addPermissions(permissions: Permission[]): RoleBuilder {
    this.role.permissions!.push(...permissions);
    return this;
  }

  addRestriction(restriction: RoleRestriction): RoleBuilder {
    this.role.restrictions!.push(restriction);
    return this;
  }

  active(isActive: boolean = true): RoleBuilder {
    this.role.isActive = isActive;
    return this;
  }

  build(): AdvancedRole {
    if (!this.role.name || !this.role.displayName) {
      throw new Error('Le nom et le nom d\'affichage sont requis');
    }

    this.role.createdAt = new Date();
    return this.role as AdvancedRole;
  }
}

export class PermissionBuilder {
  private permission: Partial<Permission> = {
    conditions: [],
    isGranted: true
  };

  static create(): PermissionBuilder {
    return new PermissionBuilder();
  }

  name(name: string, displayName?: string): PermissionBuilder {
    this.permission.name = name;
    this.permission.displayName = displayName || name;
    return this;
  }

  description(description: string): PermissionBuilder {
    this.permission.description = description;
    return this;
  }

  module(module: string): PermissionBuilder {
    this.permission.module = module;
    return this;
  }

  action(action: PermissionAction): PermissionBuilder {
    this.permission.action = action;
    return this;
  }

  resource(resource: string): PermissionBuilder {
    this.permission.resource = resource;
    return this;
  }

  addCondition(condition: PermissionCondition): PermissionBuilder {
    this.permission.conditions!.push(condition);
    return this;
  }

  granted(isGranted: boolean = true, grantedBy?: string): PermissionBuilder {
    this.permission.isGranted = isGranted;
    this.permission.grantedBy = grantedBy;
    this.permission.grantedAt = new Date();
    return this;
  }

  build(): Permission {
    if (!this.permission.name || !this.permission.action || !this.permission.resource) {
      throw new Error('Le nom, l\'action et la ressource sont requis');
    }

    return this.permission as Permission;
  }
}
