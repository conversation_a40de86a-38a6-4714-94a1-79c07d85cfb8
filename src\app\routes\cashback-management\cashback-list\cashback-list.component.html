<div class="grid">
    <div class="col-12">
        <div class="card">
            <p-table #dt1 [value]="tableData" selectionMode="single" dataKey="id" [resizableColumns]="true" [rows]="10"
                [loading]="loading" [rowHover]="true" styleClass="p-datatable-gridlines" [paginator]="true"
                [globalFilterFields]="['code','companyName','walletBalance','dynoAmount','shopFeePercentage','feeAmount','netAmount','vatAmount','totalAmount','paymentMethod','date', 'status','creatorUserEmail']"
                responsiveLayout="scroll">
                <ng-template pTemplate="caption">
                    <div>

                        <p-button  pRipple type="button" icon="pi pi-filter-slash" label="Clear" outlined="true"
                        [pTooltip]="'Clear Filters'" (click)="clear(dt1)" [style]="{'margin-right.px': '10'}"
                        styleClass="p-button-outlined " >
                        <span class="tooltip"></span></p-button>

                        <p-button *ngIf="requestType=='New'&&!isUserSuperAdmin" pRipple type="button" icon="pi pi-plus" label="Add" outlined="true"
                        [pTooltip]="'Add Cashback'" (click)="displayCashbackDialog(null)" [style]="{'margin-right.px': '10'}"
                        styleClass="p-button-outlined " >
                        <span class="tooltip"></span></p-button>

                        <app-cashback-add-edit [display]="displayAddEditDialog" (cashbackAdded)="onCashbackAdded()"
                            (closeAddDialogEvent)="closeAddEditDialogEvent($event)"></app-cashback-add-edit>

                    </div>
                </ng-template>

                <ng-template pTemplate="header">
                    <tr>
                        <th style="min-width: 10rem" pSortableColumn="code">
                            <div class="flex justify-content-between align-items-center">
                                Id <!--<p-sortIcon field="code"></p-sortIcon>
                                <p-columnFilter type="text" field="code" display="menu"
                                    placeholder="Search by id"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 12rem" pSortableColumn="companyName" *ngIf="isUserSuperAdmin">
                            <div class="flex justify-content-between align-items-center">
                                Company Name <!--<p-sortIcon field="companyName"></p-sortIcon>
                                <p-columnFilter type="text" field="companyName" display="menu"
                                    placeholder="Search by name"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="dynoAmount">
                            <div class="flex justify-content-between align-items-center">
                                Dyno Amount <!--<p-sortIcon field="dynoAmount"></p-sortIcon>
                                <p-columnFilter type="numeric" field="dynoAmount" display="menu"
                                    placeholder="Search by amount"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="shopFeePercentage">
                            <div class="flex justify-content-between align-items-center">

                                Dyno commission % <!--<p-sortIcon field="shopFeePercentage"></p-sortIcon>
                                <p-columnFilter type="numeric" field="shopFeePercentage" display="menu"
                                    placeholder="Search by shopFeePercentage"></p-columnFilter>-->

                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="feeAmount">
                            <div class="flex justify-content-between align-items-center">
                                Dyno commission Amount <!--<p-sortIcon field="feeAmount"></p-sortIcon>
                                <p-columnFilter type="numeric" field="feeAmount" display="menu"
                                    placeholder="Search by feeAmount"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="netAmount">
                            <div class="flex justify-content-between align-items-center">
                                Net Amount <!--<p-sortIcon field="netAmount"></p-sortIcon>
                                <p-columnFilter type="numeric" field="netAmount" display="menu"
                                    placeholder="Search by net Amount"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="vatAmount">
                            <div class="flex justify-content-between align-items-center">
                                VAT Amount <!--<p-sortIcon field="vatAmount"></p-sortIcon>
                                <p-columnFilter type="numeric" field="vatAmount" display="menu"
                                    placeholder="Search by vat Amount"></p-columnFilter> -->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="totalAmount">
                            <div class="flex justify-content-between align-items-center">
                                Total Amount <!--<p-sortIcon field="totalAmount"></p-sortIcon>
                                <p-columnFilter type="numeric" field="totalAmount" display="menu"
                                    placeholder="Search by total Amount" currency="TND"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="paymentMethod">
                            <div class="flex justify-content-between align-items-center">
                                Payment Method <!--<p-sortIcon field="paymentMethod"></p-sortIcon>
                                <p-columnFilter field="paymentMethod" matchMode="equals" display="menu">
                                    <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                        <p-dropdown [ngModel]="value" [options]="PaymentMethodList"
                                            (onChange)="filter($event.value)" placeholder="Search by payment method"
                                            [style]="{'min-width': '12rem'}">
                                            <ng-template let-option pTemplate="item">
                                                <span
                                                    [class]="'customer-badge status-' + option.value">{{option.label}}</span>
                                            </ng-template>
                                        </p-dropdown>
                                    </ng-template>
                                </p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="date">
                            <div class="flex justify-content-between align-items-center">
                                Request Date <!--<p-sortIcon field="date"></p-sortIcon>
                                <p-columnFilter type="date" field="date" display="menu"
                                    placeholder="mm/dd/yyyy"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="date" *ngIf="requestType=='Valid'">
                            <div class="flex justify-content-between align-items-center">
                                Validation Date <!--<p-sortIcon field="date"></p-sortIcon>
                                <p-columnFilter type="date" field="date" display="menu"
                                    placeholder="mm/dd/yyyy"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem" pSortableColumn="status">
                            <div class="flex justify-content-between align-items-center">
                                Status<!-- <p-sortIcon field="status"></p-sortIcon>
                                <p-columnFilter field="status" matchMode="equals" display="menu">
                                    <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                        <p-dropdown [ngModel]="value" [options]="cashbackStatusList"
                                            (onChange)="filter($event.value)" placeholder="Any"
                                            [style]="{'min-width': '12rem'}">
                                            <ng-template let-option pTemplate="item">
                                                <span
                                                    [class]="'customer-badge status-' + option.value">{{option.label}}</span>
                                            </ng-template>
                                        </p-dropdown>
                                    </ng-template>
                                </p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 12rem" pSortableColumn="creatorUserEmail">
                            <div class="flex justify-content-between align-items-center">
                                Created by <!--<p-sortIcon field="creatorUserEmail"></p-sortIcon>
                                <p-columnFilter type="text" field="creatorUserEmail" display="menu"
                                    placeholder="Search by user email"></p-columnFilter> -->
                            </div>
                        </th>
                        <th style="min-width: 12rem" pSortableColumn="creatorUserEmail" *ngIf="requestType=='Failed'">
                            <div class="flex justify-content-between align-items-center">
                                Reason <!--<p-sortIcon field="Reason"></p-sortIcon>
                                <p-columnFilter type="text" field="Reason" display="menu"
                                    placeholder="Search by Reason"></p-columnFilter>-->
                            </div>
                        </th>
                        <th style="min-width: 10rem" *ngIf="requestType=='New'">
                            <div class="flex justify-content-between align-items-center">
                                Actions
                            </div>
                        </th>

                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-cashback *ngIf="requestType!='Failed'">
                    <tr [pSelectableRow]="cashback">
                        <td>
                            {{cashback.code}}
                        </td>
                        <td *ngIf="isUserSuperAdmin">
                            {{cashback.company.name}}
                        </td>
                        <td class="text-right">
                            {{cashback.dynoAmount| number:'1.3-3'}}
                        </td>
                        <td class="text-right">
                            {{cashback.paymentDetails.shopFeePercentage| number:'1.3-3'}}
                        </td>
                        <td class="text-right">
                            <div style="display: inline-block; padding-right: 5px;">
                                {{ 'TND' }}
                            </div>
                            <div style="display: inline-block;">
                                {{cashback.feeAmount| number:'1.3-3'}}
                            </div>
                        </td>
                        <td class="text-right">
                            <div style="display: inline-block; padding-right: 5px;">
                                {{ 'TND' }}
                            </div>
                            <div style="display: inline-block;">
                                {{cashback.netAmount| number:'1.3-3'}}
                            </div>
                        </td>
                        <td class="text-right">
                            <div style="display: inline-block; padding-right: 5px;">
                                {{ 'TND' }}
                            </div>
                            <div style="display: inline-block;">
                                {{cashback.vatAmount| number:'1.3-3'}}
                            </div>
                        </td>
                        <td class="text-right">
                            <div style="display: inline-block; padding-right: 5px;">
                                {{ 'TND' }}
                            </div>
                            <div style="display: inline-block;">
                                {{ cashback.totalAmount | number:'1.3-3'}}
                            </div>
                        </td>
                        <td>
                            <span>{{ getPaymentMethodString(cashback.paymentDetails.paymentMethod) }}</span>
                        </td>
                        <td>
                            <span>{{ cashback.creationTime| dateTimeToDate }}</span>
                        </td>
                        <td *ngIf="requestType=='Valid'">
                            <span>{{ cashback.validationDate| dateTimeToDate }}</span>
                        </td>
                        <td>
                            <span [class]="'component-badge status-' + getStatusString(cashback.status)">
                                {{ getStatusString(cashback.status) }}
                            </span>
                        </td>
                        <td>
                            {{cashback.creatorUserEmail}}
                        </td>

                        <td *ngIf="requestType=='New'">

                            <p-button *ngIf="isUserSuperAdmin" pRipple type="button" icon="pi pi-check" label="" outlined="true"
                            [pTooltip]="'Validate Cashback'" (click)="displayValidateCashbackDialog(cashback)" [style]="{'margin-right.px': '10'}"
                            styleClass="p-button-outlined " >
                            <span class="tooltip"></span></p-button>

                            <p-button *ngIf="!isUserSuperAdmin" pRipple type="button" icon="pi pi-times" label="" outlined="true"
                            [pTooltip]="'Cancel Cashback'" (click)="showCancelConfirmation(cashback)" [style]="{'margin-right.px': '10'}"
                            styleClass="p-button-outlined " >
                            <span class="tooltip"></span></p-button>

                            <p-button *ngIf="isUserSuperAdmin" pRipple type="button" icon="pi pi-times" label="" outlined="true"
                            [pTooltip]="'Reject Cashback'" (click)="showCancelConfirmation(cashback)" [style]="{'margin-right.px': '10'}"
                            styleClass="p-button-outlined " >
                            <span class="tooltip"></span></p-button>

                            <!-- <button pButton pRipple type="button" class="p-button-outlined" icon="pi pi-pencil"
                  style="margin-right: 10px;" (click)="displayCashbackDialog(cashback)"></button>
                <button pButton pRipple type="button" class="p-button-outlined" icon="pi pi-trash"
                  (click)="showDeleteConfirmation(cashback)"></button> -->
                            <p-confirmDialog header="Confirmation" key="displayValidateCashbackDialog"
                                icon="pi pi-exclamation-triangle" [style]="{'confirmation-message' : true}"
                                acceptButtonStyleClass="p-button-outlined confirmation-button-success"
                                rejectButtonStyleClass="p-button-outlined  p-button-text p-button-danger"></p-confirmDialog>
                        </td>

                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-failedCashback *ngIf="requestType=='Failed'">
                    <tr [pSelectableRow]="failedCashback">
                        <td>
                            {{failedCashback.cashback.code}}
                        </td>
                        <td *ngIf="isUserSuperAdmin">
                            {{failedCashback.cashback.company.name}}
                        </td>
                        <td class="text-right">
                            {{failedCashback.cashback.dynoAmount| number:'1.3-3'}}
                        </td>
                        <td class="text-right">
                            {{failedCashback.cashback.paymentDetails.shopFeePercentage| number:'1.3-3'}}
                        </td>
                        <td class="text-right">
                            <div style="display: inline-block; padding-right: 5px;">
                                {{ 'TND' }}
                            </div>
                            <div style="display: inline-block;">
                                {{failedCashback.cashback.feeAmount| number:'1.3-3'}}
                            </div>
                        </td>
                        <td class="text-right">
                            <div style="display: inline-block; padding-right: 5px;">
                                {{ 'TND' }}
                            </div>
                            <div style="display: inline-block;">
                                {{failedCashback.cashback.netAmount| number:'1.3-3'}}
                            </div>
                        </td>
                        <td class="text-right">
                            <div style="display: inline-block; padding-right: 5px;">
                                {{ 'TND' }}
                            </div>
                            <div style="display: inline-block;">
                                {{failedCashback.cashback.vatAmount| number:'1.3-3'}}
                            </div>
                        </td>
                        <td class="text-right">
                            <div style="display: inline-block; padding-right: 5px;">
                                {{ 'TND' }}
                            </div>
                            <div style="display: inline-block;">
                                {{ failedCashback.cashback.totalAmount | number:'1.3-3'}}
                            </div>
                        </td>
                        <td>
                            <span>{{ getPaymentMethodString(failedCashback.cashback.paymentDetails.paymentMethod)
                                }}</span>
                        </td>
                        <td>
                            <span>{{ failedCashback.cashback.creationTime| dateTimeToDate }}</span>
                        </td>
                        <td>
                            <span [class]="'component-badge status-' + getStatusString(failedCashback.status)">
                                {{ getStatusString(failedCashback.status) }}
                            </span>
                        </td>
                        <td>
                            {{failedCashback.creatorUserEmail}}
                        </td>
                        <td>
                            {{failedCashback.reason}}
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="8">No transfer request found.</td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="loadingbody">
                    <tr>
                        <td colspan="8">Loading transfer request data. Please wait.</td>
                    </tr>
                </ng-template>
            </p-table>
            <!-- Delete Confirmation Component -->
            <app-cancel-confirmation [display]="displayDeleteDialog" (confirm)="confirmDelete($event)"
                (cancelDelete)="onCancelDelete()" (elementDeleted)="onElementDeleted()"></app-cancel-confirmation>

        </div>
    </div>
    <p-toast key="toast"></p-toast>
</div>