{"auth": {"login": {"success": true, "objectValue": {"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IlRlc3QgVXNlciIsImlhdCI6MTUxNjIzOTAyMiwiZXhwIjoxNzM1NjU5NjAwfQ.mock-signature", "refreshToken": "mock-refresh-token-12345", "expiredDate": "2024-12-31T23:59:59Z", "user": {"id": "user-123", "name": "Test User", "email": "<EMAIL>", "role": "admin"}}, "errorMessage": null}}, "companies": [{"id": "company-1", "name": "Test Company 1", "email": "<EMAIL>", "phoneNumber": "+1234567890", "status": 1, "entrepriseType": 1, "serviceType": 1, "picture": "assets/demo/images/company/company1.png", "employees": []}, {"id": "company-2", "name": "Test Company 2", "email": "<EMAIL>", "phoneNumber": "+1234567891", "status": 1, "entrepriseType": 2, "serviceType": 2, "picture": "assets/demo/images/company/company2.png", "employees": []}, {"id": "company-3", "name": "Test Company 3", "email": "<EMAIL>", "phoneNumber": "+1234567892", "status": 2, "entrepriseType": 1, "serviceType": 3, "picture": "assets/demo/images/company/company3.png", "employees": []}], "users": [{"id": "user-1", "name": "<PERSON>", "email": "<EMAIL>", "role": "admin", "status": 1, "datebirth": "1990-01-15", "phoneNumber": "+1234567890"}, {"id": "user-2", "name": "<PERSON>", "email": "<EMAIL>", "role": "user", "status": 1, "datebirth": "1985-05-20", "phoneNumber": "+1234567891"}], "roles": [{"id": "role-1", "name": "admin", "displayName": "Administrateur", "description": "Accès complet au système", "isSystemRole": true, "isActive": true, "permissions": [{"id": "perm-1", "name": "company.create", "displayName": "Créer des entreprises", "action": "create", "resource": "company", "isGranted": true}, {"id": "perm-2", "name": "company.read", "displayName": "Voir les entreprises", "action": "read", "resource": "company", "isGranted": true}], "userCount": 5, "hierarchy": 0}], "auditLogs": [{"id": "audit-1", "userId": "user-123", "userName": "Test User", "userRole": "admin", "action": "CREATE", "entityType": "Company", "entityId": "company-1", "entityName": "Test Company 1", "ipAddress": "*************", "userAgent": "Mozilla/5.0...", "timestamp": "2024-01-15T10:30:00Z", "success": true, "sessionId": "session-123", "correlationId": "corr-456"}, {"id": "audit-2", "userId": "user-123", "userName": "Test User", "userRole": "admin", "action": "UPDATE", "entityType": "User", "entityId": "user-2", "entityName": "<PERSON>", "ipAddress": "*************", "userAgent": "Mozilla/5.0...", "timestamp": "2024-01-15T11:15:00Z", "success": true, "sessionId": "session-123", "correlationId": "corr-457"}], "notifications": [{"id": "notif-1", "title": "Nouvelle entreprise créée", "message": "L'entreprise Test Company a été créée avec succès", "type": "success", "priority": "normal", "category": "business", "isRead": false, "isArchived": false, "createdAt": "2024-01-15T14:30:00Z", "source": "system"}, {"id": "notif-2", "title": "Utilisateur mis à jour", "message": "Le profil utilisateur a été modifié", "type": "info", "priority": "low", "category": "user", "isRead": true, "isArchived": false, "createdAt": "2024-01-15T13:20:00Z", "source": "system"}]}