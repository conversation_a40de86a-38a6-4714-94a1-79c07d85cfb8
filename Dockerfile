# Multi-stage build pour optimiser la taille de l'image

# Stage 1: Build de l'application Angular
FROM node:18-alpine AS builder

# Définir le répertoire de travail
WORKDIR /app

# Copier les fichiers de dépendances
COPY package*.json ./

# Installer les dépendances avec résolution forcée des conflits
RUN npm ci --only=production --legacy-peer-deps

# Copier le code source
COPY . .

# Build de l'application pour production
RUN npm run build --prod

# Stage 2: Serveur Nginx pour servir l'application
FROM nginx:alpine AS production

# Copier la configuration Nginx personnalisée
COPY nginx.conf /etc/nginx/nginx.conf

# Copier les fichiers buildés depuis le stage précédent
COPY --from=builder /app/dist/apollo-ng /usr/share/nginx/html

# Exposer le port 80
EXPOSE 80

# Commande par défaut
CMD ["nginx", "-g", "daemon off;"]

# Stage 3: Image de développement avec hot reload
FROM node:18-alpine AS development

WORKDIR /app

# Installer Angular CLI globalement
RUN npm install -g @angular/cli

# Copier les fichiers de dépendances
COPY package*.json ./

# Installer toutes les dépendances (dev + prod) avec résolution forcée
RUN npm install --legacy-peer-deps

# Copier le code source
COPY . .

# Exposer le port 4200 pour le serveur de développement
EXPOSE 4200

# Commande par défaut pour le développement
CMD ["ng", "serve", "--host", "0.0.0.0", "--port", "4200"]

# Stage 4: Image pour les tests
FROM node:18-alpine AS testing

WORKDIR /app

# Installer les outils de test
RUN npm install -g @angular/cli
RUN apk add --no-cache chromium

# Variables d'environnement pour Puppeteer
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV CHROMIUM_PATH=/usr/bin/chromium-browser

# Copier les fichiers de dépendances
COPY package*.json ./

# Installer toutes les dépendances avec résolution forcée
RUN npm install --legacy-peer-deps

# Copier le code source
COPY . .

# Exposer le port pour les tests e2e
EXPOSE 4200

# Commande par défaut pour les tests
CMD ["npm", "run", "test:ci"]
