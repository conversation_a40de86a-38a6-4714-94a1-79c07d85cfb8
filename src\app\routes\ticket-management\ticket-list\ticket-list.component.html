<div class="grid">
  <div class="col-12">
    <div class="card">
      <p-table #dt1 [value]="tickets" dataKey="id" [rows]="pageSize" [loading]="loading" [rowHover]="true"
        styleClass="p-datatable-gridlines" [paginator]="true" [lazy]="true" (onLazyLoad)="lazyLoadtickets($event)"
        [totalRecords]="totalRecords" [first]="first" rowGroupMode="subheader" (onSort)="onSort()"
        responsiveLayout="scroll" styleClass="p-datatable-gridlines"
        [globalFilterFields]="['name','type','amount','quantity','totalAmount','period','isAutomatic','sstartDate','endDate', 'status']"
        responsiveLayout="scroll">

        <ng-template pTemplate="caption">
          <div>
            <p-button pRipple type="button" icon="pi pi-filter-slash" label="Clear" [outlined]="true"
              [pTooltip]="'Clear Filters'" (click)="clear(dt1)" [style]="{'margin-right.px': '10'}"
              styleClass="p-button-outlined ">
              <span class="tooltip"></span></p-button>

            <p-button pRipple type="button" icon="pi pi-plus" label="Add" outlined="true" [pTooltip]="'Add Ticket'"
              (click)="displayTicketDialog(null)" [style]="{'margin-right.px': '10'}" styleClass="p-button-outlined ">
              <span class="tooltip"></span></p-button>

            <app-ticket-add-edit [display]="displayAddEditDialog" [selectedTicket]="selectedTicket"
              (ticketAdded)="onTicketAdded()"
              (closeAddDialogEvent)="closeAddEditDialogEvent($event)"></app-ticket-add-edit>
          </div>
        </ng-template>

        <ng-template pTemplate="header">
          <tr>

            <th style="min-width: 12rem" pSortableColumn="name">
              <div class="flex justify-content-between align-items-center">
                  <span>Name</span>
                  <div class="flex align-items-center">
                    <p-sortIcon field="Name" pTooltip="Sort Data" pTooltipPosition="right" pTooltipStyleClass="custom-tooltip"></p-sortIcon>
                    <p-columnFilter pTooltip="Filter Data" type="text" field="Name" display="menu" placeholder="Search by name"></p-columnFilter>
                </div>
            </div>
          </th>  
            <th style="min-width: 10rem" pSortableColumn="type">
              <div class="flex justify-content-between align-items-center">
                Type <!--<p-sortIcon field="type"></p-sortIcon>
                <p-columnFilter field="type" matchMode="equals" display="menu">
                  <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                    <p-dropdown [ngModel]="value" [options]="ticketTypeList" (onChange)="filter($event.value)"
                      placeholder="Any" [style]="{'min-width': '12rem'}">
                      <ng-template let-option pTemplate="item">
                        <span [class]="'customer-badge status-' + option.value">{{option.label}}</span>
                      </ng-template>
                    </p-dropdown>
                  </ng-template>
                </p-columnFilter>-->
              </div>
            </th>
            <th style="min-width: 10rem" pSortableColumn="amount">
              <div class="flex justify-content-between align-items-center">
                Amount <!--<p-sortIcon field="amount"></p-sortIcon>
                <p-columnFilter type="numeric" field="amount" display="menu"
                  placeholder="Search by amount"></p-columnFilter>-->
              </div>
            </th>
            <th style="min-width: 10rem" pSortableColumn="amount">
              <div class="flex justify-content-between align-items-center">
                Quantity <!--<p-sortIcon field="quantity"></p-sortIcon>
                <p-columnFilter type="numeric" field="quantity" display="menu"
                  placeholder="Search by quantity"></p-columnFilter>-->
              </div>
            </th>
            <th style="min-width: 10rem" pSortableColumn="amount">
              <div class="flex justify-content-between align-items-center">
                Total Amount <!--<p-sortIcon field="totalAmount"></p-sortIcon>
                <p-columnFilter type="numeric" field="totalAmount" display="menu"
                  placeholder="Search by total Amount"></p-columnFilter>-->
              </div>
            </th>
            <th style="min-width: 10rem" pSortableColumn="isAutomatic">
              <div class="flex justify-content-between align-items-center">
                Is Automatic <!--<p-sortIcon field="isAutomatic"></p-sortIcon>
                <p-columnFilter type="boolean" field="isAutomatic" display="menu"
                  placeholder="Search by isAutomatic"></p-columnFilter>-->
              </div>
            </th>
            <th style="min-width: 10rem" pSortableColumn="PeriodType">
              <div class="flex justify-content-between align-items-center">
                Period <!--<p-sortIcon field="PeriodType"></p-sortIcon>
                <p-columnFilter field="PeriodType" matchMode="equals" display="menu">
                  <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                    <p-dropdown [ngModel]="value" [options]="PeriodTypeList" (onChange)="filter($event.value)"
                      placeholder="Search by period" [style]="{'min-width': '12rem'}">
                      <ng-template let-option pTemplate="item">
                        <span [class]="'customer-badge status-' + option.value">{{option.label}}</span>
                      </ng-template>
                    </p-dropdown>
                  </ng-template>
                </p-columnFilter>-->
              </div>
            </th>
            <!--<th style="min-width: 10rem" pSortableColumn="startDate">
                <div class="flex justify-content-between align-items-center">
                  Start Date <p-sortIcon field="startDate"></p-sortIcon>
                  <p-columnFilter type="date" field="startDate" display="menu" placeholder="mm/dd/yyyy"></p-columnFilter>
                </div>
              </th>
              <th style="min-width: 10rem" pSortableColumn="endDate">
                <div class="flex justify-content-between align-items-center">
                  End Date <p-sortIcon field="endDate"></p-sortIcon>
                  <p-columnFilter type="date" field="endDate" display="menu" placeholder="mm/dd/yyyy"></p-columnFilter>
                </div>
              </th> -->
            <th style="min-width: 10rem" pSortableColumn="status">
              <div class="flex justify-content-between align-items-center">
                Status <!--<p-sortIcon field="Status"></p-sortIcon>
                <p-columnFilter field="Status" matchMode="equals" display="menu">
                  <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                    <p-dropdown [ngModel]="value" [options]="StatusList" (onChange)="filter($event.value)"
                      placeholder="Any" [style]="{'min-width': '12rem'}">
                      <ng-template let-option pTemplate="item">
                        <span [class]="'customer-badge status-' + option.value">{{option.label}}</span>
                      </ng-template>
                    </p-dropdown>
                  </ng-template>
                </p-columnFilter>-->
              </div>
            </th>
            <th style="min-width: 10rem">
              <div class="flex justify-content-between align-items-center">
                Actions
              </div>
            </th>

          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-ticket>
          <tr>
            <td>
              {{ticket.name}}
            </td>
            <td>
              <span>{{ getTicketTypeString(ticket.type) }}</span>
            </td>
            <td class="text-right">
              {{ticket.amount| number:'1.3-3'}}
            </td>
            <td class="text-right">
              {{ticket.quantity| number:'1.3-3'}}
            </td>
            <td class="text-right">
              {{ticket.totalAmount| number:'1.3-3'}}
            </td>
            <td>
              <span>{{ticket.isAutomatic}}</span>
            </td>
            <td>
              <span>{{ getPeriodTypeString(ticket.periodType) }}</span>
            </td>
            <!--<td>
              <span>{{ ticket.startDate| dateTimeToDate }}</span>
            </td>
            <td>
              <span>{{ ticket.endDate |dateTimeToDate }}</span>
            </td>-->
            <td>
              <span [class]="'component-badge status-' + getStatusString(ticket.status)">
                {{ getStatusString(ticket.status) }}
              </span>
            </td>

            <td>

              <p-button pRipple type="button" icon="pi pi-pencil" label="" outlined="true" [pTooltip]="'Edit Ticket'"
                (click)="displayTicketDialog(ticket)" [style]="{'margin-right.px': '10'}"
                styleClass="p-button-outlined ">
                <span class="tooltip"></span></p-button>

              <p-button pRipple type="button" icon="pi pi-trash" label="" outlined="true" [pTooltip]="'Delete Ticket'"
                (click)="showDeleteConfirmation(ticket)" [style]="{'margin-right.px': '10'}"
                styleClass="p-button-outlined ">
                <span class="tooltip"></span></p-button>

            </td>

          </tr>
        </ng-template>
        <ng-template pTemplate="emptymessage">
          <tr>
            <td colspan="8">No tickets found.</td>
          </tr>
        </ng-template>
        <ng-template pTemplate="loadingbody">
          <tr>
            <td colspan="8">Loading tickets data. Please wait.</td>
          </tr>
        </ng-template>
      </p-table>
      <!-- Delete Confirmation Component -->
        <app-add-confirmation [display]="displayDeleteDialog" (confirm)="confirmDelete()" (cancelDelete)="onCancelDelete()"
        (elementDeleted)="onElementDeleted()"></app-add-confirmation>

    </div>
  </div>
  <p-toast key="toast"></p-toast>
</div>