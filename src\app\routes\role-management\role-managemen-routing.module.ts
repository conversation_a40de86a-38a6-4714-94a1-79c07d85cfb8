import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { RoleListComponent } from './role-list/role-list.component';
import { RoleGuard } from 'src/app/RoleGuard';

@NgModule({
    imports: [RouterModule.forChild([
        { path: '', component:RoleListComponent,canActivate:[RoleGuard],data:{role:["company", "shopowner","superadmin"]as string[]} }
    ])],
    exports: [RouterModule]
})
export class RoleManagementRoutingModule { }
