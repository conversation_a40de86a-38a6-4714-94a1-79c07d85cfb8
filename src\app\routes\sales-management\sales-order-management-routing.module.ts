import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SalesOrderListComponent } from './sales-order-list/sales-order-list.component';
import { RoleGuard } from 'src/app/RoleGuard';
@NgModule({
    imports: [RouterModule.forChild([
        { path: '', component:SalesOrderListComponent ,canActivate:[RoleGuard],data:{role:["company","superadmin"]as string[]}}
    ])],
    exports: [RouterModule]
})
export class SalesOrderManagementRoutingModule { }
