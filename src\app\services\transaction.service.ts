import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ResponseAPI } from '../shared/models/ResponseAPI';
import { environment } from 'src/environments/environment';
import { TransactionDTO } from '../shared/models/transaction/TransactionDTO';
import { Ticket } from '../shared/models/ticket/ticket';
import { GroupTicket } from '../shared/models/Group/groupTicket';
import { EmployeeTicketDTO } from '../shared/models/employee/employeeTicketDTO';
@Injectable({
    providedIn: 'root',
})
export class Transaction {
    constructor(private _http: HttpClient) { }

    getAllTransaction(pageNumber : number, pageSize : number){
        return this._http.get<ResponseAPI<TransactionDTO[]>>(`${environment.API}/Transaction/GetCashiersTransactions?PageNumber=${pageNumber}&PageSize=${pageSize}`);

    }

    sendTicketsToGroup(groupId : string, ticketsId : Ticket[]) {
        return this._http.post<ResponseAPI<TransactionDTO[]>>(`${environment.API}/Transaction/SendTicketToGroup?groupId=${groupId}`, ticketsId);
    }

    SendMultipleTicketToGroups(groupTickets : GroupTicket[]) {
        return this._http.post<ResponseAPI<TransactionDTO[]>>(`${environment.API}/Transaction/SendMultipleTicketToGroups`, groupTickets);
    }

    sendTicketsToEmployee(employeeId: string, tickets: Ticket[]) {
        const employeeTickets: EmployeeTicketDTO[] = [];
        tickets.forEach(ticket => {
            const employeeTicket: EmployeeTicketDTO = {
                employeeId: employeeId,
                ticketId: ticket.ticketId 
            };
            employeeTickets.push(employeeTicket);
        });
        return this._http.post<ResponseAPI<TransactionDTO[]>>(`${environment.API}/Transaction/SendTicketsToEmployees`, employeeTickets);
    }
}


