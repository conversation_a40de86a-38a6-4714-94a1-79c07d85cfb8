import { Component, OnInit, Output, EventEmitter, HostListener } from '@angular/core';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { AuthService } from '../../../../services/auth.service';
import { LoginDTO } from 'src/app/shared/models/authentification/authRequest/LoginDTO';
import { emailFormatValidator } from 'src/app/shared/components/validator';
import { DeviceIdentifierService } from 'src/app/services/device-indentifier.service';
import { MessageService } from 'primeng/api';
import { Router } from '@angular/router';
import { LocalStoreService } from 'src/app/services/local-store.service';
import { ToastService } from 'src/app/services/ToastService.service';
import { AuthDataService } from 'src/app/services/authDataService.service';
import { UserType } from 'src/app/shared/enums/UserType';


@Component({
    templateUrl: './login.component.html',
})
export class LoginComponent implements OnInit {
    rememberMe: boolean = false;
    email: string = '';
    password: string = '';
    macAddress?: string | null;
    errorMessage?: string;
    isEmailValid: boolean = true;
    showPassword: boolean = false;
    loading = false;
    showTermsPopup: boolean = false;

    constructor(
        private layoutService: LayoutService,
        private authService: AuthService,
        private messageService: MessageService,
        private toastService: ToastService,
        private route: Router,
        private authDataService: AuthDataService,
        private localStore: LocalStoreService
    ) { }

    ngOnInit(): void {
        this.toastService.getToastSubject().subscribe(() => {
            console.log('Toast subject received');
            const toastMessage = this.toastService.getToastMessage();
            console.log('Toast message:', toastMessage);
            if (toastMessage) {
                this.messageService.add(toastMessage);
            }
        });
    }

    togglePasswordVisibility(): void {
        this.showPassword = !this.showPassword;
    }

    get dark(): boolean {
        return this.layoutService.config.colorScheme !== 'light';
    }

    login(): void {

        this.isEmailValid = emailFormatValidator(this.email);
        this.macAddress = new DeviceIdentifierService().getDeviceIdentifier();
        if (this.email && this.password) {
            this.loading = true;
            const loginDto: LoginDTO = {
                email: this.email,
                password: this.password,
                macAddress: this.macAddress,
            };

            this.authService.login(loginDto).subscribe((response) => {
                if (response.statusCode == 200) {
                    this.handleSuccessfulLogin(response);
                } else if (response.statusCode == 302) {
                    this.handleOtpRedirect(response);
                } else if (response.statusCode == 401) {
                    this.handleAuthenticationError(response);
                } else if (response.statusCode == 400) {
                    this.showTermsPopup = true;
                } else {
                    this.loading = false;
                }
            });
        } else {
            this.handleEmptyInputsError();
        }
    }

    


    private handleSuccessfulLogin(response: any): void {
        this.localStore.saveData('IsDeviceVerified', 'true');
        this.messageService.add({
            key: 'toast',
            severity: 'success',
            summary: 'Success Message',
            detail: response.exceptionMessage,
        });

        this.localStore.saveData('Token', response.objectValue.token);
        this.localStore.saveData('RefreshToken', response.objectValue.refreshToken);
        this.localStore.saveData('ExpiredDate', response.objectValue.expiredDate);
        this.localStore.saveData('RememberMe', this.rememberMe);

        try {
            const decodedToken = JSON.parse(atob(response.objectValue.token.split('.')[1]));
            const entrepriseType = decodedToken.EntrepriseType;
            const userType = decodedToken.UserType;

            this.localStore.saveData('EntrepriseType', entrepriseType);
            this.localStore.saveData('UserType', userType);

            if (userType == UserType[UserType.SuperAdmin].toString()) {
                this.route.navigate(['Dashboard/OverviewS']);
            } else if (userType == UserType[UserType.Company].toString() || userType == UserType[UserType.ShopOwner].toString()) {
                this.route.navigate(['Dashboard/Overview']);
            }
        } catch (error) {
            console.error('Error decoding token:', error);
        }
    }

    private handleOtpRedirect(response: any): void {
        this.localStore.saveData('IsDeviceVerified', 'false');
        this.localStore.saveData('Token', response.objectValue.token);
        this.localStore.saveData('RefreshToken', response.objectValue.refreshToken);
        this.localStore.saveData('ExpiredDate', response.objectValue.expiredDate);

        try {
            const decodedToken = JSON.parse(atob(response.objectValue.token.split('.')[1]));
            const entrepriseType = decodedToken.EntrepriseType;
            const userType = decodedToken.UserType;

            this.localStore.saveData('EntrepriseType', entrepriseType);
            this.localStore.saveData('UserType', userType);
        } catch (error) {
            console.error('Error decoding token:', error);
        }

        const userEmail = response.objectValue.userProfile.email;
        this.authDataService.setUserEmail(userEmail);

        this.route.navigate(['auth/login/verif'], { queryParams: { userEmail } });
    }

    private handleAuthenticationError(response: any): void {
        this.loading = false;
        this.messageService.add({
            key: 'toast',
            severity: 'error',
            summary: 'Error Message',
            detail: response.exceptionMessage,
        });
    }

    private handleEmptyInputsError(): void {
        this.loading = false;
        this.messageService.add({
            key: 'toast',
            severity: 'error',
            summary: 'Error Message',
            detail: 'Please enter email and password',
        });
    }

    performLogin(acceptedTerms: boolean): void {
        // Implement your login logic here
        console.log('Login performed with terms acceptance:', acceptedTerms);

        // Check if the user accepted terms
        if (acceptedTerms) {
            const loginDto: LoginDTO = {
                email: this.email,
                password: this.password,
                macAddress: this.macAddress,
            };
            console.log('login dto===',loginDto);
            this.showTermsPopup = false;
            this.authService.login(loginDto,acceptedTerms).subscribe((response) => {
                if (response.statusCode == 200) {
                    this.handleSuccessfulLogin(response);
                } else if (response.statusCode == 302) {
                    this.handleOtpRedirect(response);
                } else if (response.statusCode == 401) {
                    this.handleAuthenticationError(response);
                } else if (response.statusCode == 400) {
                    this.showTermsPopup = true;
                } else {
                    this.loading = false;
                }
            });

        } else {
            this.loading = false;
            this.showTermsPopup = false;
            // Handle case where user refuses terms
        }
    }
}
