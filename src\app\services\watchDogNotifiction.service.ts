// signalr.service.ts
import { Injectable, EventEmitter, InjectionToken } from '@angular/core';
import { Subject } from 'rxjs';
import * as signalR from '@microsoft/signalr';
import { IHttpConnectionOptions } from '@microsoft/signalr';
import { LogNotification } from '../shared/models/notification/logNotification';
import { environment } from 'src/environments/environment';

export const SIGNALR_SERVICE = new InjectionToken<WatchDogNotificationService>('SIGNALR_SERVICE');

@Injectable({
  providedIn: 'root',
  useFactory: () => new WatchDogNotificationService(),
  deps: [],
})

export class WatchDogNotificationService {
  private hubConnection!: signalR.HubConnection;
  public notifications: LogNotification[] = [];
  //public notificationReceived = new EventEmitter<LogNotification>();

  private notificationSource = new Subject<LogNotification>();
  public notificationReceived$ = this.notificationSource.asObservable();



  constructor() {
    // Check if SignalR is available
    if (signalR) {
      // Create a new instance of HubConnection
      const accessToken = localStorage.getItem('Token');
      
      const options: IHttpConnectionOptions = {
        accessTokenFactory: () => {
          return accessToken ? accessToken : "Token is null";
        }
      };

      this.hubConnection = new signalR.HubConnectionBuilder()
        .withUrl(`${environment.NotificationAPI}/WatchNotify`, options)
        .build();

      this.hubConnection.on('ReceiveNotification', (message: LogNotification) => {
        //this.notificationReceived$.emit(message);
        this.notificationSource.next(message);
        this.notifications.push(message);
      });

      // Start the connection
      this.hubConnection.start()
        .then(() => console.log('SignalR connection started'))
        .catch(err => console.error('Error while starting SignalR connection: ', err));
    } else {
      console.error('SignalR library not available.');
    }
  }
}