import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'dateTimeToDate'
})
export class DateTimeToDatePipe implements PipeTransform {
  transform(value: string | Date): string {
    if (!value) return '';

    const date = new Date(value);
    if (isNaN(date.getTime())) {
      return value.toString(); // Return the original value if it's not a valid date.
    }
    return date.toLocaleDateString(); // Extract the date part.
  }
}
