import { ChangeDetector<PERSON><PERSON>, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { SalesOrder } from 'src/app/shared/models/salesOrder/salesOrder';
import { SalesOrderService } from 'src/app/services/sales-order.service';
import { WalletService } from 'src/app/services/wallet.service';
import { UserType } from 'src/app/shared/enums/UserType';
import { SalesOrderStatus, SalesOrderStatusString } from 'src/app/shared/enums/SalesOrderStatus';
import { Company } from 'src/app/shared/models/company/company';
import { CompanyService } from 'src/app/services/company.service';
import { EntrepriseType } from 'src/app/shared/enums/entreprise-type';
import { ServiceType } from 'src/app/shared/enums/service-type';
import { PaymentMethod } from 'src/app/shared/enums/PaymentMethod';
import { CashbackService } from 'src/app/services/cashback.service';
import { Cashback } from 'src/app/shared/models/cashback/cashback';
import { CashbackStatus, CashbackStatusString } from 'src/app/shared/enums/cashbackStatus';
import { FilterMetadata } from 'primeng/api';

interface InventoryStatus {
    label: string;
    value: string;
}

@Component({
    selector: 'app-superadmin-overview',
    templateUrl: './superadmin-overview.component.html',
    styleUrls: ['./superadmin-overview.component.scss']
})

export class SuperadminOverviewComponent implements OnInit, OnDestroy {

    knobValue: number = 90;
    selectedMonth: any;
    months: any[] = [];
    barData: any;
    barOptions: any;
    pieData: any;
    pieOptions: any;
    salesOrders: SalesOrder[] = [];
    cashBacks: Cashback[] = [];
    companiesBalance: number = 0;
    companiesBalancePM: number = 2852;
    shopsBalance: number = 0;
    shopsBalancePM: number = 15952;
    companyDemandsToValidate: number = 0;
    shopDemandsToValidate: number = 0;
    revenuesData: any;
    salesData: any;
    recentCompanies: Company[] = [];
    subscription: Subscription;
    cols: any[] = [];
    companyRate: number = 0;
    shopRate: number = 0;
    sortBy?: string | string[] | null | undefined;
    sortDirection: number = 1;
    pageSize: number = 5;
    pageNumber: number = 0;
    first!: number;
    totalRecords: number = 1;
    currency: string = 'TND' ;
    constructor(private companyService: CompanyService, private walletService: WalletService, private cdr: ChangeDetectorRef,
        private salesOrderService: SalesOrderService, private cashBackService: CashbackService, private layoutService: LayoutService) {
        this.subscription = this.layoutService.configUpdate$.subscribe(config => {
        });
    }

    ngOnInit(): void {

        this.initCharts();
        this.getSalesAndRevenues();
        this.getAllCompanyDemandsToValidate();
        this.getAllShopDemandsToValidate();
        this.getBalanceSalesOnCompanies();
        this.getBalanceSalesOnShops();
        this.getRecentClients();
       // this.getCompanyTransactions();
        this.getShopTransactions();

    }

    initCharts() {
        const documentStyle = getComputedStyle(document.documentElement);
        const textColor = documentStyle.getPropertyValue('--text-color');
        const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');
        const surfaceBorder = documentStyle.getPropertyValue('--surface-border');

        this.barData = {
            labels: ['JAN', 'FEV', 'MAR', 'APR', 'MAI', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'],
            datasets: [
                {
                    label: 'Sales',
                    backgroundColor: documentStyle.getPropertyValue('--primary-200'),
                    barThickness: 12,
                    borderRadius: 12,
                    data: []
                },
                {
                    label: 'Revenues',
                    backgroundColor: documentStyle.getPropertyValue('--primary-500'),
                    barThickness: 12,
                    borderRadius: 12,
                    data: []
                }
            ]
        };


        this.pieData = {
            labels: ['Electronics', 'Fashion', 'Household'],
            datasets: [
                {
                    data: [300, 50, 100],
                    backgroundColor: [

                        documentStyle.getPropertyValue('--primary-700'),
                        documentStyle.getPropertyValue('--primary-400'),
                        documentStyle.getPropertyValue('--primary-100')
                    ],
                    hoverBackgroundColor: [
                        documentStyle.getPropertyValue('--primary-600'),
                        documentStyle.getPropertyValue('--primary-300'),
                        documentStyle.getPropertyValue('--primary-200')

                    ]
                }
            ]
        };

        this.barOptions = {
            animation: {
                duration: 0
            },
            plugins: {
                legend: {
                    labels: {
                        color: textColor,
                        usePointStyle: true,
                        font: {
                            weight: 700,
                        },
                        padding: 28
                    },
                    position: 'bottom'
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: textColorSecondary,
                        font: {
                            weight: 500
                        }
                    },
                    grid: {
                        display: false,
                        drawBorder: false
                    }
                },
                y: {
                    ticks: {
                        color: textColorSecondary
                    },
                    grid: {
                        color: surfaceBorder,
                        drawBorder: false
                    }
                }
            }
        };

        this.pieOptions = {
            animation: {
                duration: 0
            },
            plugins: {
                legend: {
                    labels: {
                        color: textColor,
                        usePointStyle: true,
                        font: {
                            weight: 700,
                        },
                        padding: 28
                    },
                    position: 'bottom'
                }
            }
        };
    }

    initColumns() {
        this.cols = [
            { header: 'companyName', field: 'companyName' },
            { header: 'dynoAmount', field: 'dynoAmount' },
            { header: 'totalAmount', field: 'totalAmount' },
            { header: 'paymentMethod', field: 'paymentMethod' },
            { header: 'status', field: 'status' }
        ]
    }
    lazyCompanyTransactions(event: TableLazyLoadEvent) {
        this.first = event.first || 0;
        this.sortBy = event.sortField ?? '';
        this.sortDirection = event.sortOrder as number;
        this.getCompanyTransactions(event.filters as { [s: string]: FilterMetadata } | undefined);

    }
    getCompanyTransactions(filters?: { [s: string]: FilterMetadata } | undefined) {

        this.pageNumber = (this.first / this.pageSize) + 1;
        const sortBy = this.sortBy as string;
        const sortDirection = this.sortDirection;
        // filters = filters || {};
        this.salesOrderService.getAllSalesOrders(this.pageSize, this.pageNumber,sortBy, sortDirection).subscribe(
            (response) => {
                if (response.objectValue) {
                    this.salesOrders = response.objectValue;
                }
            });
    }
    getShopTransactions() {
        this.cashBackService.getCashbacks(this.pageSize).subscribe(
            (response) => {
                if (response.objectValue) {
                    this.cashBacks = response.objectValue;
                }
            });
    }



    getRecentClients() {
        this.companyService.getRecents(this.pageSize).subscribe(
            (response) => {
                if (response.objectValue) {
                    this.recentCompanies = response.objectValue;
                }
            });
    }

    getSalesAndRevenues() {

        this.salesOrderService.getAllPerPeriod().subscribe(
            (response) => {
                if (response.objectValue) {
                    this.salesData = response.objectValue;
                    this.barData.datasets[0].data = this.salesData;
                    this.updateChart();
                }

            },
            (error) => {
                console.error('Error fetching data:', error);
            }
        );
        //Waiting for Cashback endpoint
        this.cashBackService.getAllCashbacksPerPeriod().subscribe(
            (response) => {
                if (response.objectValue) {
                    this.revenuesData = response.objectValue;
                    this.barData.datasets[1].data = this.revenuesData;
                    this.updateChart();
                }
            },
            (error) => {
                console.error('Error fetching data:', error);
            }
        );
    }

    getBalanceSalesOnCompanies() {
        this.walletService.GetTotalBalanceByUserType(UserType.Company).subscribe(
            (response) => {
                if (response.objectValue) {
                    this.companiesBalance = response.objectValue;
                    this.getCompanySalesRate();

                }
            },
            (error) => {
                console.error('Error fetching balance sales data:', error);
            }
        );
    }
    getBalanceSalesOnCompaniesPreviousMonth() {
        this.walletService.GetCompaniesTotalBalancePreviousMonth().subscribe(
            (response) => {
                if (response.objectValue) {
                    this.companiesBalancePM = response.objectValue;
                }
            },
            (error) => {
                console.error('Error fetching balance sales data:', error);
            }
        );
    }

    getCompanySalesRate(): void {
        if (this.companiesBalance !== undefined && this.companiesBalancePM !== undefined) {
            this.companyRate = parseFloat((((this.companiesBalance - this.companiesBalancePM) / this.companiesBalancePM) * 100).toFixed(0));
        } else {

            this.companyRate = 0;
        }
    }

    getShopSalesRate() {
        if (this.shopsBalance !== undefined && this.shopsBalancePM !== undefined) {
            this.shopRate = parseFloat((((this.shopsBalance - this.shopsBalancePM) / this.shopsBalancePM) * 100).toFixed(0));
        } else {

            this.shopRate = 0;
        }
    }

    getBalanceSalesOnShops() {
        this.walletService.GetTotalBalanceByUserType(UserType.ShopOwner).subscribe(
            (response) => {
                if (response.objectValue) {
                    this.shopsBalance = response.objectValue;
                    this.getShopSalesRate();
                }
            },
            (error) => {
                console.error('Error fetching balance sales data:', error);
            }
        );
    }
    getBalanceSalesOnShopsPreviousMonth() {
        this.walletService.GetShopsTotalBalancePreviousMonth().subscribe(
            (response) => {
                if (response.objectValue) {
                    this.shopsBalancePM = response.objectValue;
                }
            },
            (error) => {
                console.error('Error fetching balance sales data:', error);
            }
        );
    }

    getAllCompanyDemandsToValidate() {

        this.salesOrderService.getAllSalesOrdersByStatus([SalesOrderStatus.InProgress, SalesOrderStatus.PDFFailed, SalesOrderStatus.PDFGenerated, SalesOrderStatus.EmailSent, SalesOrderStatus.EmailFailed]).subscribe(
            (response) => {
                if (response.body && response.body.objectValue) {
                    this.companyDemandsToValidate = response.body.objectValue.length;
                }
            },
            (error) => {
                console.error('Error fetching balance sales data:', error);
            }
        );
    }

    getAllShopDemandsToValidate() {

        this.cashBackService.getAllCashbacksByStatus(CashbackStatus.InProgress).subscribe(
            (response) => {
                if (response.objectValue) {
                    this.shopDemandsToValidate = response.objectValue.length;
                }
            },
            (error) => {
                console.error('Error fetching balance sales data:', error);
            }
        );
    }

    private updateChart() {
        this.cdr.detectChanges();
        const documentStyle = getComputedStyle(document.documentElement);
        const textColor = documentStyle.getPropertyValue('--text-color');
        const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');
        const surfaceBorder = documentStyle.getPropertyValue('--surface-border');

        this.barData = {
            labels: ['JAN', 'FEV', 'MAR', 'APR', 'MAI', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'],
            datasets: [
                {
                    label: 'Sales',
                    backgroundColor: documentStyle.getPropertyValue('--primary-200'),
                    barThickness: 12,
                    borderRadius: 12,
                    data: this.salesData
                },
                {
                    label: 'Revenues',
                    backgroundColor: documentStyle.getPropertyValue('--primary-500'),
                    barThickness: 12,
                    borderRadius: 12,
                    data: this.revenuesData
                }
            ]
        };

    }

    onGlobalFilter(table: Table, event: Event) {
        table.filterGlobal((event.target as HTMLInputElement).value, 'contains');
    }

    ngOnDestroy(): void {
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }

    getEntrepriseTypeString(entrepriseValue: number): string {
        return EntrepriseType[entrepriseValue];
    }
    getServiceTypeString(serviceValue: number): string {
        return ServiceType[serviceValue];
    }

    getCashbackStatusString(statusValue: number): string {
        return CashbackStatusString[statusValue];
    }
    getSalesOrderStatusString(statusValue: number): string {
        return SalesOrderStatusString[statusValue];
    }
    getPaymentMethodString(paymentValue: number): string {
        return PaymentMethod[paymentValue];
    }

}
