import { Status } from "../../enums/status";
import { EntrepriseType } from "../../enums/entreprise-type";
import { ServiceType } from "../../enums/service-type";
import { Address } from "../address/address";
import { Employee } from "../employee/employee";
import { UserDTO } from "../user/UserDTO";
import { PaymentDetails } from "./paymentDetails";
import { CategoryType } from "../../enums/categoryType";

export interface Company {
    id?: string;
    taxCode: string;
    rneCode: string;
    name: string;
    email: string;
    phoneNumber: string;
    countryCode: string;
    clientFeePercentage: string;
    entrepriseType: EntrepriseType;
    serviceType: ServiceType;
    categoryType?: CategoryType;
    picture: string;
    status: Status;
    addresses: Address[];
    employees: Employee[];
    users: UserDTO[];
    paymentDetails: PaymentDetails[];

}
