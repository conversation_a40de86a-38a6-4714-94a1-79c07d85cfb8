import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CashbackListComponent } from './cashback-list/cashback-list.component';
import { RoleGuard } from 'src/app/RoleGuard';
@NgModule({
    imports: [RouterModule.forChild([
        { path: '', component: CashbackListComponent,canActivate:[RoleGuard],data:{role:["shopowner","superadmin"]as string[]} }
    ])],
    exports: [RouterModule]
})
export class CashbackManagementRoutingModule { }
