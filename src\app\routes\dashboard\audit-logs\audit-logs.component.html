<div class="grid">
    <!-- Résumé des statistiques -->
    <div class="col-12" *ngIf="summary$ | async as summary">
        <div class="grid">
            <div class="col-12 md:col-3">
                <div class="card text-center bg-blue-100">
                    <h6 class="text-blue-800">Total des Logs</h6>
                    <span class="text-3xl font-bold text-blue-600">{{ summary.totalLogs | number }}</span>
                </div>
            </div>
            <div class="col-12 md:col-3">
                <div class="card text-center bg-green-100">
                    <h6 class="text-green-800">Actions Réussies</h6>
                    <span class="text-3xl font-bold text-green-600">{{ summary.successfulActions | number }}</span>
                </div>
            </div>
            <div class="col-12 md:col-3">
                <div class="card text-center bg-red-100">
                    <h6 class="text-red-800">Actions Échouées</h6>
                    <span class="text-3xl font-bold text-red-600">{{ summary.failedActions | number }}</span>
                </div>
            </div>
            <div class="col-12 md:col-3">
                <div class="card text-center bg-purple-100">
                    <h6 class="text-purple-800">Utilisateurs Actifs</h6>
                    <span class="text-3xl font-bold text-purple-600">{{ summary.uniqueUsers | number }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres -->
    <div class="col-12">
        <div class="card">
            <h5>Filtres de Recherche</h5>
            <form [formGroup]="filterForm" (ngSubmit)="applyFilters()">
                <div class="grid">
                    <!-- Filtres de base -->
                    <div class="col-12 md:col-3">
                        <label for="action">Action</label>
                        <p-dropdown 
                            id="action"
                            formControlName="action"
                            [options]="actionOptions"
                            placeholder="Toutes les actions"
                            [showClear]="true"
                            styleClass="w-full">
                        </p-dropdown>
                    </div>
                    
                    <div class="col-12 md:col-3">
                        <label for="entityType">Type d'Entité</label>
                        <p-dropdown 
                            id="entityType"
                            formControlName="entityType"
                            [options]="entityTypeOptions"
                            placeholder="Tous les types"
                            [showClear]="true"
                            styleClass="w-full">
                        </p-dropdown>
                    </div>
                    
                    <div class="col-12 md:col-3">
                        <label for="success">Statut</label>
                        <p-dropdown 
                            id="success"
                            formControlName="success"
                            [options]="successOptions"
                            placeholder="Tous"
                            styleClass="w-full">
                        </p-dropdown>
                    </div>
                    
                    <div class="col-12 md:col-3">
                        <label>&nbsp;</label>
                        <div class="flex gap-2">
                            <p-button 
                                type="submit"
                                label="Filtrer" 
                                icon="pi pi-search"
                                styleClass="p-button-primary">
                            </p-button>
                            <p-button 
                                type="button"
                                label="Effacer" 
                                icon="pi pi-times"
                                styleClass="p-button-outlined"
                                (click)="clearFilters()">
                            </p-button>
                        </div>
                    </div>
                </div>

                <!-- Filtres avancés -->
                <div class="mt-3">
                    <p-button 
                        type="button"
                        [label]="showAdvancedFilters ? 'Masquer les filtres avancés' : 'Afficher les filtres avancés'"
                        [icon]="showAdvancedFilters ? 'pi pi-chevron-up' : 'pi pi-chevron-down'"
                        styleClass="p-button-text"
                        (click)="toggleAdvancedFilters()">
                    </p-button>
                </div>

                <div *ngIf="showAdvancedFilters" class="grid mt-3">
                    <div class="col-12 md:col-4">
                        <label for="userId">ID Utilisateur</label>
                        <input 
                            id="userId"
                            type="text" 
                            pInputText 
                            formControlName="userId"
                            placeholder="ID de l'utilisateur"
                            class="w-full">
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <label for="entityId">ID Entité</label>
                        <input 
                            id="entityId"
                            type="text" 
                            pInputText 
                            formControlName="entityId"
                            placeholder="ID de l'entité"
                            class="w-full">
                    </div>
                    
                    <div class="col-12 md:col-4">
                        <label for="ipAddress">Adresse IP</label>
                        <input 
                            id="ipAddress"
                            type="text" 
                            pInputText 
                            formControlName="ipAddress"
                            placeholder="Adresse IP"
                            class="w-full">
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <label for="dateFrom">Date de début</label>
                        <p-calendar 
                            id="dateFrom"
                            formControlName="dateFrom"
                            [showTime]="true"
                            dateFormat="dd/mm/yy"
                            placeholder="Date de début"
                            styleClass="w-full">
                        </p-calendar>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <label for="dateTo">Date de fin</label>
                        <p-calendar 
                            id="dateTo"
                            formControlName="dateTo"
                            [showTime]="true"
                            dateFormat="dd/mm/yy"
                            placeholder="Date de fin"
                            styleClass="w-full">
                        </p-calendar>
                    </div>
                </div>
            </form>

            <!-- Indicateur de filtres actifs -->
            <div *ngIf="getActiveFiltersCount() > 0" class="mt-3">
                <p-tag 
                    [value]="getActiveFiltersCount() + ' filtre(s) actif(s)'"
                    severity="info"
                    icon="pi pi-filter">
                </p-tag>
            </div>
        </div>
    </div>

    <!-- Table des logs -->
    <div class="col-12">
        <div class="card">
            <div class="flex justify-content-between align-items-center mb-3">
                <h5>Logs d'Audit</h5>
                <div class="flex gap-2">
                    <p-button 
                        icon="pi pi-refresh" 
                        label="Actualiser"
                        styleClass="p-button-outlined"
                        (click)="loadAuditLogs(); refreshSummary()">
                    </p-button>
                    <p-button 
                        icon="pi pi-download" 
                        label="Export CSV"
                        styleClass="p-button-help"
                        (click)="exportLogs('csv')">
                    </p-button>
                    <p-button 
                        icon="pi pi-file-excel" 
                        label="Export Excel"
                        styleClass="p-button-success"
                        (click)="exportLogs('excel')">
                    </p-button>
                    <p-button 
                        icon="pi pi-trash" 
                        label="Nettoyer"
                        styleClass="p-button-danger"
                        (click)="cleanupOldLogs()">
                    </p-button>
                </div>
            </div>

            <p-table 
                #auditTable
                [value]="items"
                dataKey="id"
                [rows]="pageSize"
                [loading]="loading"
                [rowHover]="true"
                styleClass="p-datatable-gridlines"
                [paginator]="true"
                [lazy]="true"
                (onLazyLoad)="onLazyLoad($event)"
                [totalRecords]="totalRecords"
                [first]="first"
                responsiveLayout="scroll">

                <ng-template pTemplate="header">
                    <tr>
                        <th pSortableColumn="timestamp">
                            Date/Heure <p-sortIcon field="timestamp"></p-sortIcon>
                        </th>
                        <th pSortableColumn="userName">
                            Utilisateur <p-sortIcon field="userName"></p-sortIcon>
                        </th>
                        <th pSortableColumn="action">
                            Action <p-sortIcon field="action"></p-sortIcon>
                        </th>
                        <th pSortableColumn="entityType">
                            Entité <p-sortIcon field="entityType"></p-sortIcon>
                        </th>
                        <th pSortableColumn="success">
                            Statut <p-sortIcon field="success"></p-sortIcon>
                        </th>
                        <th>Détails</th>
                    </tr>
                </ng-template>

                <ng-template pTemplate="body" let-log>
                    <tr>
                        <td>{{ formatTimestamp(log.timestamp) }}</td>
                        <td>
                            <div>
                                <strong>{{ log.userName }}</strong>
                                <br>
                                <small class="text-600">{{ log.userRole }}</small>
                            </div>
                        </td>
                        <td>
                            <p-tag 
                                [value]="getActionLabel(log.action)"
                                [severity]="getActionSeverity(log.action)">
                            </p-tag>
                        </td>
                        <td>
                            <div>
                                <strong>{{ log.entityType }}</strong>
                                <br>
                                <small class="text-600">{{ log.entityId }}</small>
                                <small *ngIf="log.entityName" class="block text-500">{{ log.entityName }}</small>
                            </div>
                        </td>
                        <td>
                            <p-tag 
                                [value]="log.success ? 'Succès' : 'Échec'"
                                [severity]="log.success ? 'success' : 'danger'"
                                [icon]="log.success ? 'pi pi-check' : 'pi pi-times'">
                            </p-tag>
                        </td>
                        <td>
                            <div class="flex gap-1">
                                <p-button 
                                    icon="pi pi-eye" 
                                    styleClass="p-button-rounded p-button-text p-button-info"
                                    pTooltip="Voir les détails"
                                    (click)="showLogDetails(log)">
                                </p-button>
                                <p-tag 
                                    *ngIf="hasChanges(log)"
                                    value="Modif"
                                    severity="warning"
                                    icon="pi pi-pencil">
                                </p-tag>
                                <p-tag 
                                    *ngIf="log.errorMessage"
                                    value="Erreur"
                                    severity="danger"
                                    icon="pi pi-exclamation-triangle">
                                </p-tag>
                            </div>
                        </td>
                    </tr>
                </ng-template>

                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="6" class="text-center">
                            <div class="flex flex-column align-items-center justify-content-center py-5">
                                <i class="pi pi-search text-4xl text-400 mb-3"></i>
                                <span class="text-lg text-600">Aucun log d'audit trouvé</span>
                            </div>
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </div>
    </div>
</div>

<!-- Dialog des détails du log -->
<p-dialog 
    [(visible)]="displayLogDetails"
    header="Détails du Log d'Audit"
    [modal]="true"
    [style]="{width: '70vw'}"
    [draggable]="false"
    [resizable]="false">
    
    <div *ngIf="selectedLogDetails" class="grid">
        <div class="col-12 md:col-6">
            <h6>Informations Générales</h6>
            <div class="field">
                <label>ID:</label>
                <span class="ml-2">{{ selectedLogDetails.id }}</span>
            </div>
            <div class="field">
                <label>Utilisateur:</label>
                <span class="ml-2">{{ selectedLogDetails.userName }} ({{ selectedLogDetails.userRole }})</span>
            </div>
            <div class="field">
                <label>Action:</label>
                <span class="ml-2">{{ getActionLabel(selectedLogDetails.action) }}</span>
            </div>
            <div class="field">
                <label>Entité:</label>
                <span class="ml-2">{{ selectedLogDetails.entityType }}:{{ selectedLogDetails.entityId }}</span>
            </div>
            <div class="field">
                <label>Date/Heure:</label>
                <span class="ml-2">{{ formatTimestamp(selectedLogDetails.timestamp) }}</span>
            </div>
        </div>
        
        <div class="col-12 md:col-6">
            <h6>Contexte Technique</h6>
            <div class="field">
                <label>Adresse IP:</label>
                <span class="ml-2">{{ selectedLogDetails.ipAddress }}</span>
            </div>
            <div class="field">
                <label>Session ID:</label>
                <span class="ml-2">{{ selectedLogDetails.sessionId }}</span>
            </div>
            <div class="field">
                <label>Correlation ID:</label>
                <span class="ml-2">{{ selectedLogDetails.correlationId }}</span>
            </div>
            <div class="field">
                <label>Statut:</label>
                <p-tag 
                    [value]="selectedLogDetails.success ? 'Succès' : 'Échec'"
                    [severity]="selectedLogDetails.success ? 'success' : 'danger'">
                </p-tag>
            </div>
        </div>
        
        <div class="col-12" *ngIf="selectedLogDetails.errorMessage">
            <h6>Message d'Erreur</h6>
            <div class="p-3 bg-red-50 border-round">
                <span class="text-red-800">{{ selectedLogDetails.errorMessage }}</span>
            </div>
        </div>
        
        <div class="col-12" *ngIf="hasChanges(selectedLogDetails)">
            <h6>Modifications</h6>
            <p-tabView>
                <p-tabPanel header="Anciennes Valeurs" *ngIf="selectedLogDetails.oldValues">
                    <pre class="bg-gray-50 p-3 border-round">{{ selectedLogDetails.oldValues | json }}</pre>
                </p-tabPanel>
                <p-tabPanel header="Nouvelles Valeurs" *ngIf="selectedLogDetails.newValues">
                    <pre class="bg-gray-50 p-3 border-round">{{ selectedLogDetails.newValues | json }}</pre>
                </p-tabPanel>
            </p-tabView>
        </div>
        
        <div class="col-12" *ngIf="selectedLogDetails.additionalData">
            <h6>Données Additionnelles</h6>
            <pre class="bg-gray-50 p-3 border-round">{{ selectedLogDetails.additionalData | json }}</pre>
        </div>
    </div>
    
    <ng-template pTemplate="footer">
        <p-button 
            label="Fermer" 
            icon="pi pi-times"
            styleClass="p-button-text"
            (click)="closeLogDetails()">
        </p-button>
    </ng-template>
</p-dialog>

<!-- Confirmations -->
<p-confirmDialog></p-confirmDialog>

<!-- Messages toast -->
<p-toast></p-toast>
