import { Component, ElementRef, ViewChild } from '@angular/core';
import { ConfirmationService, MessageService } from 'primeng/api';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { UserService } from 'src/app/services/User.service';
import { CashierService } from 'src/app/services/cashier.service';
import { Gender } from 'src/app/shared/enums/Gender';
import { Status } from 'src/app/shared/enums/status';
import { UserDTO } from 'src/app/shared/models/user/UserDTO';

interface expandedRows {
  [key: string]: boolean;
}

@Component({
  selector: 'app-cashier-list',
  templateUrl: './cashier-list.component.html',
  styleUrls: ['./cashier-list.component.scss']
})

export class CashierListComponent {
  users: UserDTO[] = [];
  displayAddEditDialog: boolean = false;
  displayDeleteDialog: boolean = false;
  displayAddDialog: boolean = false;
  selectedUserId!: string ;
  rowGroupMetadata: any;
  selectedUser: UserDTO | null = null;
  expandedRows: expandedRows = {};
  activityValues: number[] = [0, 100];
  isExpanded: boolean = false;
  idFrozen: boolean = false;
  loading: boolean = true;
  Status = Status;
  public isAddButtonDisabled = false;

  pageSize: number = 5;
  pageNumber: number = 0;
  sortBy?: string | string[] | null | undefined;
  sortDirection: number = 1;
  first!: number;
  totalRecords: number = 1;

  @ViewChild('filter') filter!: ElementRef;
  constructor(
      private userService:UserService,
      private cashierService: CashierService,
      private messageService: MessageService) { }

    ngOnInit() {
      // this.loadUsers();
    }


    loadUsers() {
      this.pageNumber = (this.first / this.pageSize) + 1;
      const sortBy = this.sortBy as string;
      const sortDirection = this.sortDirection;
      this.cashierService.getAllCashiers(this.pageSize, this.pageNumber, sortBy, this.sortDirection).subscribe(response => {
          if (response.body && response.body.objectValue) {
            this.users = response.body.objectValue;
            const xPaginationHeader = response.headers.get('x-pagination');
            if (xPaginationHeader) {
                const xPagination = JSON.parse(xPaginationHeader);
                this.totalRecords = xPagination.TotalCount;
                console.log(this.totalRecords);
            } else {
                console.error('x-pagination header not found in the response');
            }
          }
          this.users = this.users;
          this.loading = false;
        },
        (error) => {
        }
      );
    }

    expandAll() {
      if (!this.isExpanded) {


      } else {
        this.expandedRows = {};
      }
      this.isExpanded = !this.isExpanded;
    }

  onGlobalFilter(table: Table, event: Event) {
      table.filterGlobal((event.target as HTMLInputElement).value, 'contains');
    }

    clear(table: Table) {
      table.clear();
      this.filter.nativeElement.value = '';
    }

    showDeleteConfirmation(id: any) {

      if (id !== undefined) {
        this.selectedUserId = id;
        this.displayDeleteDialog = true;
      }
      else{
          console.log('nope');

      }
    }

    showAddConfirmation() {
        this.displayAddDialog = true;
        this.isAddButtonDisabled = true; 
    }

  confirmDelete() {
      if (this.selectedUserId !== null) {
      this.cashierService.deleteCashier(this.selectedUserId).subscribe({
          next: (response) => {
              if (response.statusCode === 200) {
                  this.displayDeleteDialog = false;
                  this.onElementDeleted()
                  this.messageService.add({ key: 'toast', severity: 'success', summary: 'Cashier deleted successfully', detail: response.exceptionMessage });
              } else {
                  this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Delete Cashier', detail: response.exceptionMessage });
              }
          },
          error: (err) => {
              this.displayDeleteDialog = false;
              this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Delete Cashier', detail: err });
          }
      });
            }
            else {
              this.displayDeleteDialog = false;
              this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Delete User', detail: "response.exceptionMessage" });

            }
    }

    onAdd(user:UserDTO | null) {
      this.cashierService.AddCashier().subscribe({
        next: (response) => {
            if (response.statusCode === 201) {
                this.displayAddDialog = false;
                this.loadUsers()
                this.messageService.add({ key: 'toast', severity: 'success', summary: 'Cashier added successfully', detail: response.exceptionMessage });
                this.isAddButtonDisabled = false; 
            } else {
                this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error in adding the Cashier', detail: response.exceptionMessage });
            }
        },
        error: (err) => {
            this.displayAddDialog = false;
            this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error in adding the Cashier', detail: err });
        }
    });       
    }

    closeAddEditDialogEvent(e: Event) {
      this.displayAddDialog = false;
    }

    onUserAdded() {
      this.loadUsers();
      this.displayAddDialog = false;
    }

    async onElementDeleted() {
      await this.loadUsers();
    }

    onCancelDelete(){
      this.displayDeleteDialog=false;
      
    }

    onCancelAdd() {
      this.displayAddDialog = false;
      this.isAddButtonDisabled = false; 
    }

    getStatusString(statusValue: number): string {
      return Status[statusValue];
    }

    getGenderString(gender: number): string {
      return Gender[gender];
    }

    lazyLoadCashiers(event: TableLazyLoadEvent) {
      this.first = event.first || 0;
      this.sortBy = event.sortField ?? '';
      this.sortDirection = event.sortOrder as number;
      this.loadUsers();
    }
}


