import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ListTransactionComponent } from './list-transaction/list-transaction.component';
import { CancelTransactionComponent } from './cancel-transaction/cancel-transaction.component';
import { RoleGuard } from 'src/app/RoleGuard';

const routes: Routes = [
    { path: '', component: ListTransactionComponent ,canActivate:[RoleGuard],data:{role:[ "shopowner"]as string[]}},
    { path: 'refund', component: CancelTransactionComponent ,canActivate:[RoleGuard],data:{role:["shopowner"]as string[]}}
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class ShopownerTransactionRoutingModule { }
