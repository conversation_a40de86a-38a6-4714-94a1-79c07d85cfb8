<p-dialog [header]="mode =='add'? 'Add New Group' : 'Edit Group'" [(visible)]="display" [modal]="true" showEffect="fade"
    [style]="{width: '60vw'}" [breakpoints]="{'960px': '75vw'}" (onHide)="closeAddDialog()">
    <form [formGroup]="groupFormGroup">
        <div class="col-12">
            <div class="card">
                <div class="row">
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 input-name">
                        <h5>Name <span style="color: red;">*</span></h5>
                        <input type="text" class="input-width" pInputText placeholder="Name" formControlName="name">
                        <p-message severity="error" text="Name is required"
                            *ngIf="groupFormGroup.get('name')?.hasError('required') && groupFormGroup.get('name')?.touched">
                        </p-message>
                    </div>

                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 dd-status">
                        <h5>Status</h5>
                        <p-dropdown [options]="statusDropdown" (onChange)="onStatusChange($event)" optionLabel="label"
                            optionValue="value" formControlName="status"></p-dropdown>
                    </div>
                </div>


                <div class="row">
                    <div formArrayName="ticketsArray" class="col-12">
                        <div *ngFor="let ticketGroup of ticketsArray.controls; let i = index;" [formGroupName]="i">

                            <div class="row">
                                <!-- Delete Button -->
                                <div class="col-12">
                                    <p-button pRipple type="button" icon="pi pi-minus" label="" [outlined]="true"
                                        [pTooltip]="'Remove Ticket'" (click)="deleteTicketGroup(i)"
                                        [style]="{'margin-right.px': '10'}" styleClass="p-button-outlined ">
                                        <span class="tooltip"></span></p-button>
                                </div>
                            </div>
                            <div class="row">
                                <!-- Ticket Type -->
                                <div class="col-6 mb-6 lg:col-6 lg:mb-0 dd-ticketType">
                                    <h5 class="left-aligned-label">Ticket Type<span style="color: red;">*</span></h5>
                                    <p-dropdown [options]="ticketTypeDropdown"
                                        (onChange)="onTicketTypeChange($event, i)" optionLabel="label"
                                        optionValue="value" formControlName="ticketType" [style]="{'width': '100%'}">
                                    </p-dropdown>
                                </div>

                                <!-- Ticket -->
                                <div class="col-6 mb-6 lg:col-6 lg:mb-0 dd-ticket">
                                    <h5>Ticket<span style="color: red;">*</span></h5>
                                    <p-autoComplete [suggestions]="ticketSuggestions"
                                        (completeMethod)="searchTickets($event)" field="label" [minLength]="1"
                                        placeholder="Search for a ticket" class="input-width" formControlName="ticket"
                                        (keyup)="onTicketChange($event)"(input)=" ticketGroup.get('ticket')?.markAsTouched()" [style]="{'width': '100%'}">
                                    </p-autoComplete>
                                    <p-message severity="error" text="Ticket is required"
                                    *ngIf="ticketGroup.get('ticket')?.hasError('required')  && ticketGroup.get('ticket')?.touched && !ticketGroup.get('ticket')?.value">
                                  </p-message>
                                  
                                  <p-message severity="error" text="Ticket is not in the suggestions"
                                    *ngIf="!ticketSuggestions.includes(ticketGroup.get('ticket')?.value) && ticketGroup.get('ticket')?.touched && ticketGroup.get('ticket')?.dirty && ticketGroup.get('ticket')?.value">
                                  </p-message>

                                  


       

                                </div>
                            </div>

                        </div>
                        <!-- Add Button -->
                        <div class="col-12">
                            <p-button pRipple type="button" icon="pi pi-plus" label="" [outlined]="true"
                                [pTooltip]="'Add Ticket'" (click)="addTicketGroup()" [style]="{'margin-right.px': '10'}"
                                styleClass="p-button-outlined ">
                                <span class="tooltip"></span></p-button>
                        </div>
                    </div>


                </div>

            </div>
        </div>
    </form>
    <ng-template pTemplate="footer" class="footer">
        <button pButton (click)="closeAddDialog()" label="Cancel" class="p-button-outlined "></button>
        <button pButton (click)="saveGroup()" class="p-button-outlined p-button-success" [disabled]="loading">
            <span *ngIf="!loading">Save</span>
            <p-progressSpinner *ngIf="loading" styleClass="w-3rem h-2rem" strokeWidth="6"></p-progressSpinner>
        </button>
        <p-toast key="toast"></p-toast>
    </ng-template>
</p-dialog>