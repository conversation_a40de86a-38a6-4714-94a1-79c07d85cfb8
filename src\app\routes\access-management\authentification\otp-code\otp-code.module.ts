import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { RippleModule } from 'primeng/ripple';
import { AppConfigModule } from 'src/app/layout/config/app.config.module';
import { InputTextModule } from 'primeng/inputtext';
import {KeyFilterModule} from 'primeng/keyfilter';
import { OtpCodeComponent } from './otp-code.component';
import { AuthentificationRoutingModule } from '../authentification-routing.module';
import { ToastModule } from 'primeng/toast';
import { InputSwitchModule } from 'primeng/inputswitch';
import { ProgressSpinnerModule } from 'primeng/progressspinner';

@NgModule({
    imports: [
        AuthentificationRoutingModule,
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        InputTextModule,
        KeyFilterModule,
        ButtonModule,
        RippleModule,
        AppConfigModule,
        ToastModule,
        InputSwitchModule,
        ProgressSpinnerModule,
    ],
    declarations: [OtpCodeComponent]
})
export class OtpCodeModule { }
