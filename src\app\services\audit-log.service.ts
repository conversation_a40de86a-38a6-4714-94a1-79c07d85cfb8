import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, map, catchError, tap } from 'rxjs';
import { AuditLog, AuditLogFilter, AuditLogSummary, AuditAction, AuditEntityType, AuditLogBuilder } from '../shared/models/audit/audit-log';
import { BaseCrudService } from '../shared/services/base-crud.service';
import { ConfigService } from './config.service';
import { ErrorHandlerService } from './error-handler.service';
import { LocalStoreService } from './local-store.service';
import { PaginationRequest, PaginationResponse } from '../shared/models/pagination/pagination-request';
import { ResponseAPI } from '../shared/models/ResponseAPI';

@Injectable({
  providedIn: 'root'
})
export class AuditLogService extends BaseCrudService<AuditLog> {
  protected readonly endpoint = 'AuditLog';
  
  private currentUserSubject = new BehaviorSubject<any>(null);
  private sessionId: string;

  constructor(
    http: HttpClient,
    config: ConfigService,
    errorHandler: ErrorHandlerService,
    private localStore: LocalStoreService
  ) {
    super(http, config, errorHandler);
    this.sessionId = this.generateSessionId();
    this.initializeCurrentUser();
  }

  /**
   * Log une action utilisateur
   */
  logAction(
    action: AuditAction,
    entityType: AuditEntityType,
    entityId: string,
    entityName?: string,
    oldValues?: any,
    newValues?: any,
    additionalData?: any
  ): Observable<AuditLog> {
    const currentUser = this.getCurrentUser();
    
    const auditLog = AuditLogBuilder.create()
      .user(currentUser.id, currentUser.name, currentUser.role)
      .action(action)
      .entity(entityType, entityId, entityName)
      .changes(oldValues, newValues)
      .context(this.getClientIP(), this.getUserAgent(), this.sessionId)
      .result(true)
      .additionalData(additionalData || {})
      .correlationId(this.generateCorrelationId())
      .build();

    return this.create(auditLog).pipe(
      tap(() => {
        if (this.config.isLoggingEnabled) {
          console.log('🔍 Audit Log:', {
            action: action,
            entity: `${entityType}:${entityId}`,
            user: currentUser.name
          });
        }
      }),
      catchError(error => {
        console.error('Erreur lors de l\'enregistrement de l\'audit log:', error);
        // Ne pas faire échouer l'action principale si l'audit log échoue
        return this.errorHandler.createErrorObservable('Erreur d\'audit log', 'AUDIT_ERROR');
      })
    );
  }

  /**
   * Log une action échouée
   */
  logFailedAction(
    action: AuditAction,
    entityType: AuditEntityType,
    entityId: string,
    errorMessage: string,
    additionalData?: any
  ): Observable<AuditLog> {
    const currentUser = this.getCurrentUser();
    
    const auditLog = AuditLogBuilder.create()
      .user(currentUser.id, currentUser.name, currentUser.role)
      .action(action)
      .entity(entityType, entityId)
      .context(this.getClientIP(), this.getUserAgent(), this.sessionId)
      .result(false, errorMessage)
      .additionalData(additionalData || {})
      .correlationId(this.generateCorrelationId())
      .build();

    return this.create(auditLog);
  }

  /**
   * Récupère les logs avec filtres
   */
  getFilteredLogs(filter: AuditLogFilter, pagination: PaginationRequest): Observable<PaginationResponse<AuditLog>> {
    const requestBody = {
      ...pagination,
      filter: filter
    };

    return this.http.post<ResponseAPI<AuditLog[]>>(
      this.getFullUrl('GetFiltered'),
      requestBody,
      { observe: 'response', responseType: 'json' }
    ).pipe(
      map(response => {
        const data = response.body?.objectValue || [];
        const paginationInfo = this.extractPaginationFromHeaders(response.headers);
        
        return {
          data,
          totalCount: paginationInfo.totalCount || 0,
          pageNumber: paginationInfo.pageNumber || pagination.pageNumber,
          pageSize: paginationInfo.pageSize || pagination.pageSize,
          totalPages: paginationInfo.totalPages || 1,
          hasNextPage: paginationInfo.hasNextPage || false,
          hasPreviousPage: paginationInfo.hasPreviousPage || false
        } as PaginationResponse<AuditLog>;
      }),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Récupère un résumé des logs d'audit
   */
  getAuditSummary(dateFrom?: Date, dateTo?: Date): Observable<AuditLogSummary> {
    const params = this.createHttpParams({
      dateFrom: dateFrom?.toISOString(),
      dateTo: dateTo?.toISOString()
    });

    return this.http.get<ResponseAPI<AuditLogSummary>>(
      this.getFullUrl('GetSummary'),
      { params }
    ).pipe(
      map(response => response.objectValue!),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Exporte les logs d'audit
   */
  exportLogs(filter: AuditLogFilter, format: 'csv' | 'excel' = 'csv'): Observable<Blob> {
    const requestBody = { filter, format };

    return this.http.post(
      this.getFullUrl('Export'),
      requestBody,
      { responseType: 'blob' }
    ).pipe(
      tap(() => {
        this.logAction(
          AuditAction.EXPORT,
          AuditEntityType.SYSTEM,
          'audit-logs',
          'Export des logs d\'audit',
          null,
          null,
          { format, filter }
        ).subscribe();
      }),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Récupère les logs pour un utilisateur spécifique
   */
  getUserLogs(userId: string, pagination: PaginationRequest): Observable<PaginationResponse<AuditLog>> {
    return this.getFilteredLogs({ userId }, pagination);
  }

  /**
   * Récupère les logs pour une entité spécifique
   */
  getEntityLogs(entityType: AuditEntityType, entityId: string, pagination: PaginationRequest): Observable<PaginationResponse<AuditLog>> {
    return this.getFilteredLogs({ entityType, entityId }, pagination);
  }

  /**
   * Récupère les logs d'actions échouées
   */
  getFailedActions(pagination: PaginationRequest): Observable<PaginationResponse<AuditLog>> {
    return this.getFilteredLogs({ success: false }, pagination);
  }

  /**
   * Nettoie les anciens logs (à utiliser avec précaution)
   */
  cleanupOldLogs(olderThanDays: number): Observable<{ deletedCount: number }> {
    const params = this.createHttpParams({ olderThanDays });

    return this.http.delete<ResponseAPI<{ deletedCount: number }>>(
      this.getFullUrl('Cleanup'),
      { params }
    ).pipe(
      map(response => response.objectValue!),
      tap(result => {
        this.logAction(
          AuditAction.BULK_DELETE,
          AuditEntityType.SYSTEM,
          'audit-logs',
          'Nettoyage des anciens logs',
          null,
          null,
          { olderThanDays, deletedCount: result.deletedCount }
        ).subscribe();
      }),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  // Méthodes utilitaires privées
  private initializeCurrentUser(): void {
    const userData = this.localStore.getData('UserData');
    if (userData) {
      try {
        this.currentUserSubject.next(JSON.parse(userData));
      } catch {
        this.currentUserSubject.next(null);
      }
    }
  }

  private getCurrentUser(): any {
    const user = this.currentUserSubject.value;
    return user || {
      id: 'anonymous',
      name: 'Utilisateur anonyme',
      role: 'guest'
    };
  }

  private getClientIP(): string {
    // En production, ceci devrait être récupéré côté serveur
    return 'client-ip';
  }

  private getUserAgent(): string {
    return navigator.userAgent;
  }

  private generateSessionId(): string {
    return 'session_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private generateCorrelationId(): string {
    return 'corr_' + Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private extractPaginationFromHeaders(headers: any): any {
    // Réutiliser la logique du service de base
    const xPaginationHeader = headers.get('x-pagination');
    if (xPaginationHeader) {
      try {
        return JSON.parse(xPaginationHeader);
      } catch {
        return {};
      }
    }
    return {};
  }
}
