// Environment de production
export const environment = {
  production: true,
  environmentName: 'production',

  // Configuration API - À configurer selon votre environnement de production
  API: 'https://api.dyno.production.com/Api',
  NotificationAPI: 'https://notifications.dyno.production.com',
  WebAPI: 'https://web.dyno.production.com',

  // Configuration de stockage
  S3Url: 'https://dynofiles.s3.amazonaws.com/',

  // Configuration de routage
  baseUrl: '',
  useHash: false,

  // Configuration de logging
  enableLogging: false,
  logLevel: 'error',

  // Configuration de cache
  cacheTimeout: 600000, // 10 minutes

  // Configuration de sécurité
  tokenRefreshBuffer: 120000, // 2 minutes avant expiration
  maxRetryAttempts: 2,

  // Configuration de l'interface
  defaultPageSize: 20,
  maxPageSize: 100,

  // Configuration des features
  features: {
    enableAdvancedDashboard: true,
    enableAuditLogs: true,
    enableNotifications: true,
    enableExports: true
  }
};
