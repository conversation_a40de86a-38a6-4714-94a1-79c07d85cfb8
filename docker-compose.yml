version: '3.8'

services:
  # Application Angular en mode développement
  angular-dev:
    build:
      context: .
      target: development
    container_name: dyno-angular-dev
    ports:
      - "4200:4200"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    networks:
      - dyno-network
    restart: unless-stopped

  # Application Angular en mode production
  angular-prod:
    build:
      context: .
      target: production
    container_name: dyno-angular-prod
    ports:
      - "8080:80"
    environment:
      - NODE_ENV=production
    networks:
      - dyno-network
    restart: unless-stopped
    profiles:
      - production

  # Container pour les tests
  angular-test:
    build:
      context: .
      target: testing
    container_name: dyno-angular-test
    volumes:
      - .:/app
      - /app/node_modules
      - ./test-results:/app/test-results
    environment:
      - NODE_ENV=test
      - CI=true
    networks:
      - dyno-network
    profiles:
      - testing

  # Base de données pour les tests (PostgreSQL)
  postgres-test:
    image: postgres:15-alpine
    container_name: dyno-postgres-test
    environment:
      POSTGRES_DB: dyno_test
      POSTGRES_USER: dyno_user
      POSTGRES_PASSWORD: dyno_password
    ports:
      - "5433:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./database/init-scripts:/docker-entrypoint-initdb.d
    networks:
      - dyno-network
    profiles:
      - testing
      - development

  # Redis pour le cache (optionnel)
  redis:
    image: redis:7-alpine
    container_name: dyno-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - dyno-network
    profiles:
      - testing
      - development
      - production

  # Backend API simulé (pour les tests)
  mock-api:
    image: mockserver/mockserver:latest
    container_name: dyno-mock-api
    ports:
      - "1080:1080"
    environment:
      MOCKSERVER_PROPERTY_FILE: /config/mockserver.properties
    volumes:
      - ./mock-api/config:/config
      - ./mock-api/expectations:/expectations
    networks:
      - dyno-network
    profiles:
      - testing
      - development

  # Serveur de notifications (SignalR simulé)
  notification-hub:
    image: node:18-alpine
    container_name: dyno-notification-hub
    working_dir: /app
    ports:
      - "3000:3000"
    volumes:
      - ./notification-server:/app
    command: ["npm", "start"]
    networks:
      - dyno-network
    profiles:
      - testing
      - development

volumes:
  postgres_test_data:
  redis_data:

networks:
  dyno-network:
    driver: bridge
