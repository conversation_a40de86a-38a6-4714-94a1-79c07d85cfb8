import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DashboardRoutingModule } from './dashboard-routing.module';
import { AdminOverviewComponent } from './admin-overview/admin-overview.component';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { FormsModule } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { InputTextModule } from 'primeng/inputtext';
import { ChartModule } from 'primeng/chart';
import { KnobModule } from 'primeng/knob';
import { RatingModule } from 'primeng/rating';
import { RippleModule } from 'primeng/ripple';
import { ProgressBarModule } from 'primeng/progressbar';
import { MultiSelectModule } from 'primeng/multiselect';
import { SliderModule } from 'primeng/slider';
import { SuperadminOverviewComponent } from './superadmin-overview/superadmin-overview.component';
import { NotificationComponent } from './notification/notification.component';
import { ChipModule } from 'primeng/chip';
import { LoggingManagementComponent } from './logging-management/logging-management.component';
import { CardModule } from 'primeng/card';
import { TooltipModule } from 'primeng/tooltip';

@NgModule({
	declarations: [
		AdminOverviewComponent, SuperadminOverviewComponent, NotificationComponent, LoggingManagementComponent
	],
	imports: [
		CommonModule,
		ButtonModule,
		RippleModule,
		DropdownModule,
		FormsModule,
		TableModule,
		InputTextModule,
		InputTextareaModule,
		ChartModule,
		RatingModule,
		KnobModule,
		ProgressBarModule,
		MultiSelectModule,
		SliderModule,
		DashboardRoutingModule,
		ChipModule,
		CardModule,
		TooltipModule
	],
	exports: [
		NotificationComponent, 
	  ]
})
export class DashboardModule { }
