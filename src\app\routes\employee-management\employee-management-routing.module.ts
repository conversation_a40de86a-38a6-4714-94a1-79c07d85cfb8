import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { EmployeeListComponent } from './employee-list/employee-list.component';
import { RoleGuard } from 'src/app/RoleGuard';

const routes: Routes = [];

@NgModule({
  imports: [RouterModule.forChild([
    { path: '', component: EmployeeListComponent ,canActivate:[RoleGuard],data:{role:["company"]as string[]}}
  ])],
  exports: [RouterModule]
})
export class EmployeeManagementRoutingModule { }
