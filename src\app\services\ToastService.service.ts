import { Injectable } from '@angular/core';
import { MessageService } from 'primeng/api';
import { Subject } from 'rxjs';
@Injectable({
  providedIn: 'root',
})
export class ToastService {
  constructor(private messageService: MessageService) {}
  private toastSubject = new Subject<void>();

  private toastMessage: any;

  showToast(severity: string, summary: string, detail: string) {
    this.toastMessage = { severity, summary, detail };
    this.toastSubject.next();
    console.log('Toast message sent:', this.toastMessage);
  }
  getToastMessage() {
    return this.toastMessage;
  }

  getToastSubject() {
    return this.toastSubject.asObservable();
  }
}
