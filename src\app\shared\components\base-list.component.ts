import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { Subject, takeUntil, finalize } from 'rxjs';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { ConfirmationService, MessageService } from 'primeng/api';
import { BaseCrudService } from '../services/base-crud.service';
import { PaginationRequest, PaginationHelper, TableLazyLoadConfig } from '../models/pagination/pagination-request';
import { ConfigService } from 'src/app/services/config.service';

@Component({
  template: ''
})
export abstract class BaseListComponent<T> implements OnInit, OnDestroy {
  @ViewChild('dataTable') dataTable!: Table;

  // Données
  items: T[] = [];
  selectedItems: T[] = [];
  selectedItem: T | null = null;

  // Pagination
  totalRecords: number = 0;
  first: number = 0;
  pageSize: number;
  
  // État
  loading: boolean = false;
  
  // Tri
  sortBy?: string;
  sortDirection: number = 1;

  // Dialogs
  displayAddEditDialog: boolean = false;
  displayDeleteDialog: boolean = false;

  // Destruction du composant
  private destroy$ = new Subject<void>();

  // Services abstraits
  protected abstract service: BaseCrudService<T>;
  protected abstract getItemId(item: T): string;
  protected abstract getItemDisplayName(item: T): string;

  constructor(
    protected configService: ConfigService,
    protected confirmationService: ConfirmationService,
    protected messageService: MessageService
  ) {
    this.pageSize = this.configService.defaultPageSize;
  }

  ngOnInit(): void {
    this.loadItems();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Gestion du lazy loading de PrimeNG
   */
  onLazyLoad(event: TableLazyLoadEvent): void {
    this.first = event.first || 0;
    this.sortBy = event.sortField;
    this.sortDirection = event.sortOrder || 1;
    
    const config: TableLazyLoadConfig = {
      first: event.first,
      rows: event.rows,
      sortField: event.sortField,
      sortOrder: event.sortOrder,
      filters: event.filters
    };

    this.loadItemsWithConfig(config);
  }

  /**
   * Charge les éléments avec une configuration spécifique
   */
  loadItemsWithConfig(config: TableLazyLoadConfig): void {
    const request = PaginationHelper.fromTableLazyLoad(config, this.pageSize);
    this.loadItemsWithRequest(request);
  }

  /**
   * Charge les éléments avec une requête de pagination
   */
  loadItemsWithRequest(request: PaginationRequest): void {
    this.loading = true;
    
    this.service.getAllPaged(request)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => this.loading = false)
      )
      .subscribe({
        next: (response) => {
          this.items = response.data;
          this.totalRecords = response.totalCount;
          this.onItemsLoaded(response.data);
        },
        error: (error) => {
          this.onLoadError(error);
        }
      });
  }

  /**
   * Charge les éléments (méthode simple)
   */
  loadItems(): void {
    const request: PaginationRequest = {
      pageSize: this.pageSize,
      pageNumber: Math.floor(this.first / this.pageSize) + 1,
      sortBy: this.sortBy,
      sortDirection: this.sortDirection === 1 ? 'asc' : 'desc'
    };

    this.loadItemsWithRequest(request);
  }

  /**
   * Rafraîchit les données
   */
  refresh(): void {
    this.loadItems();
  }

  /**
   * Efface les filtres
   */
  clear(table: Table): void {
    table.clear();
    this.first = 0;
    this.loadItems();
  }

  /**
   * Ouvre le dialog d'ajout
   */
  openAddDialog(): void {
    this.selectedItem = null;
    this.displayAddEditDialog = true;
  }

  /**
   * Ouvre le dialog d'édition
   */
  openEditDialog(item: T): void {
    this.selectedItem = { ...item } as T;
    this.displayAddEditDialog = true;
  }

  /**
   * Ferme le dialog d'ajout/édition
   */
  closeAddEditDialog(): void {
    this.displayAddEditDialog = false;
    this.selectedItem = null;
  }

  /**
   * Confirme la suppression d'un élément
   */
  confirmDelete(item: T): void {
    this.selectedItem = item;
    this.confirmationService.confirm({
      message: `Êtes-vous sûr de vouloir supprimer ${this.getItemDisplayName(item)} ?`,
      header: 'Confirmation de suppression',
      icon: 'pi pi-exclamation-triangle',
      accept: () => this.deleteItem(item),
      reject: () => this.selectedItem = null
    });
  }

  /**
   * Supprime un élément
   */
  deleteItem(item: T): void {
    const itemId = this.getItemId(item);
    
    this.service.delete(itemId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.messageService.add({
            severity: 'success',
            summary: 'Succès',
            detail: `${this.getItemDisplayName(item)} supprimé avec succès`
          });
          this.refresh();
        },
        error: (error) => {
          this.onDeleteError(error, item);
        }
      });
  }

  /**
   * Sauvegarde un élément (création ou mise à jour)
   */
  saveItem(item: T): void {
    const itemId = this.getItemId(item);
    const isUpdate = !!itemId;

    const operation = isUpdate 
      ? this.service.update(item)
      : this.service.create(item);

    operation
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (savedItem) => {
          this.messageService.add({
            severity: 'success',
            summary: 'Succès',
            detail: `${this.getItemDisplayName(savedItem)} ${isUpdate ? 'mis à jour' : 'créé'} avec succès`
          });
          this.closeAddEditDialog();
          this.refresh();
        },
        error: (error) => {
          this.onSaveError(error, item);
        }
      });
  }

  /**
   * Callbacks pour personnalisation
   */
  protected onItemsLoaded(items: T[]): void {
    // À surcharger dans les composants enfants si nécessaire
  }

  protected onLoadError(error: any): void {
    console.error('Erreur lors du chargement des données:', error);
  }

  protected onDeleteError(error: any, item: T): void {
    console.error('Erreur lors de la suppression:', error);
  }

  protected onSaveError(error: any, item: T): void {
    console.error('Erreur lors de la sauvegarde:', error);
  }

  /**
   * Méthodes utilitaires
   */
  isSelected(item: T): boolean {
    return this.selectedItems.some(selected => 
      this.getItemId(selected) === this.getItemId(item)
    );
  }

  getSelectedCount(): number {
    return this.selectedItems.length;
  }

  hasSelection(): boolean {
    return this.selectedItems.length > 0;
  }
}
