<div class="grid">
    <div class="col-12">
      <div class="card">
        <p-table #dt1 [value]="users" dataKey="id" [rows]="pageSize" [loading]="loading" [rowHover]="true"
          styleClass="p-datatable-gridlines" [paginator]="true"
          [globalFilterFields]="['name','id','email','phonenumber']"
          [totalRecords]="totalRecords" [first]="first" 
          responsiveLayout="scroll" [lazy]="true" (onLazyLoad)="lazyLoadCashiers($event)">
          <ng-template pTemplate="caption">
            <div>
              <p-button  pRipple type="button" icon="pi pi-filter-slash" label="Clear" [outlined]="true"
                        [pTooltip]="'Clear Filters'" (click)="clear(dt1)" [style]="{'margin-right.px': '10'}"
                        styleClass="p-button-outlined " >
                        <span class="tooltip"></span></p-button>
                        
              <p-button  pRipple type="button" icon="pi pi-plus" label="Add" [outlined]="true"
                        [pTooltip]="'Add Cashier'" (click)="showAddConfirmation()" [style]="{'margin-right.px': '10'}"
                        styleClass="p-button-outlined " [disabled]="isAddButtonDisabled" >
                        <span class="tooltip"></span></p-button>
            </div>
          </ng-template>

          <ng-template pTemplate="header">
            <tr>
              <th style="min-width: 12rem">
                <div class="flex justify-content-between align-items-center">
                  Badge
                <!--  <p-columnFilter type="text" field="name" display="menu" placeholder="Search by name"></p-columnFilter>-->
                </div>
              </th>
              <th style="min-width: 12rem">
                <div class="flex justify-content-between align-items-center">
                  Name
                 <!-- <p-columnFilter type="text" field="name" display="menu" placeholder="Search by name"></p-columnFilter> -->
                </div>
              </th>
              <th style="min-width: 10rem">
                <div class="flex justify-content-between align-items-center">
                  Email
                <!--  <p-columnFilter type="text" field="email" display="menu" placeholder="Search by email"></p-columnFilter> -->
                </div>
              </th>
              <th style="min-width: 10rem">
                <div class="flex justify-content-between align-items-center">
                  Gender
                </div>
              </th>
              <th style="min-width: 10rem">
                <div class="flex justify-content-between align-items-center">
                  Phone Number
                </div>
              </th>
              <th style="min-width: 10rem">
                <div class="flex justify-content-between align-items-center">
                  Status
                </div>
              </th>
              <th style="min-width: 10rem">
                <div class="flex justify-content-between align-items-center">
                  Actions
                </div>
              </th>

            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-User>
            <tr>
              <td>
                {{User.userName}}
              </td>
              <td>
                {{User.fullName}}
              </td>
              <td>
                {{User.email}}
              </td>
              <td class="image-container">
                <img *ngIf=" getGenderString(User.gender) === 'Male'" src="../../../../assets/male.png"
                  alt="Male">
                <img *ngIf=" getGenderString(User.gender) === 'Female'" src="../../../../assets/female.png"
                  alt="Female">
                <img *ngIf=" getGenderString(User.gender) === 'Other'" src="../../../../assets/other.png"
                  alt="Male">
              </td>
              <td>{{ User.phoneNumber }}</td>
              <td>
                <span [class]="'component-badge status-' + getStatusString(User.status)">
                  {{ getStatusString(User.status) }}
                </span>
              </td>
              <td>

                  <p-button  pRipple type="button" icon="pi pi-trash" label="" [outlined]="true"
                  [pTooltip]="'Delete Cashier'" (click)="showDeleteConfirmation(User.id)" [style]="{'margin-right.px': '10'}"
                  styleClass="p-button-outlined " >
                  <span class="tooltip"></span></p-button>

              </td>

            </tr>
          </ng-template>
          <ng-template pTemplate="emptymessage">
            <tr>
              <td colspan="8">No Cashiers found.</td>
            </tr>
          </ng-template>
          <ng-template pTemplate="loadingbody">
            <tr>
              <td colspan="8">Loading Cashiers data. Please wait.</td>
            </tr>
          </ng-template>
        </p-table>
    
          <app-add-confirmation [display]="displayDeleteDialog" (confirm)="confirmDelete()" (cancelDelete)="onCancelDelete()"
          (elementDeleted)="onElementDeleted()"></app-add-confirmation>

        <!-- Delete Confirmation Component -->
        <app-add-confirmation [display]="displayAddDialog" (confirm)="onAdd(null)" (cancelDelete)="onCancelAdd()"
          (elementDeleted)="onUserAdded()"></app-add-confirmation>
      </div>
    </div>
    <p-toast key="toast"></p-toast>
  </div>

