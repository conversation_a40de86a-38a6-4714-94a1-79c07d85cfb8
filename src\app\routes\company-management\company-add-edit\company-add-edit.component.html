<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<p-dialog [header]="mode =='add'? 'Add New Company' : 'Edit Company'" [(visible)]="display" [modal]="true"
    showEffect="fade" [style]="{width: '60vw'}" [breakpoints]="{'960px': '75vw'}" (onHide)="closeAddDialog()">
    <form [formGroup]="companyFormGroup">
        <div class="col-12">
            <div class="card">
                <div class="row custom-row">
                    <div *ngIf="lastSelectedImage" class="left">
                        <img [src]="lastSelectedImage" alt="Selected Image" style="max-width: 70px; max-height: 70px;">
                    </div>
                    <div class="right">
                        <div *ngIf="selectedImage" class="image-preview">
                            <img [src]="selectedImage" alt="Selected Image" style="max-width: 50px; max-height: 50px;">
                        </div>
                        <div *ngIf="!selectedImage">
                            <label for="file-upload" class="custom-file-upload">
                                <i class="fas fa-cloud-upload-alt"></i> Logo
                            </label>
                            <input id="file-upload" type="file" accept="image/*" (change)="onFileSelected($event)"  />
                        </div>
                        <p-message severity="error" text="Incorrect Type, select another one"
                            *ngIf="imageTypeIncorrect">
                        </p-message>
                        <p-message severity="error" text="The image is too large, select another one"
                            *ngIf="imageSizeIncorrect">
                        </p-message>
                    </div>
                </div>

                <div class="row">
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 input-name">
                        <h5>Name <span style="color: red;">*</span></h5>
                        <input type="text" class="input-width" pInputText placeholder="Name" formControlName="name">
                        <p-message severity="error" text="Name is required"
                            *ngIf="companyFormGroup.get('name')?.hasError('required') && companyFormGroup.get('name')?.touched">
                        </p-message>
                    </div>
                    <div class="col-4 mb-4 lg:col-4 lg:mb-0 ">
                        <h5>Tax Code <span style="color: red;">*</span></h5>
                        <p-inputMask class="input-VAT" mask="999 a a 9999999/a" formControlName="taxcode"
                            (ngModelChange)="onVATChange($event)" placeholder="000 a a 0000000/a"
                            [style]="{ width: '100%' }"></p-inputMask>
                        <p-message severity="error" text="Tax code is required"
                            *ngIf="companyFormGroup.get('taxcode')?.hasError('required') && companyFormGroup.get('taxcode')?.touched">
                        </p-message>
                    </div>
                    <div class="col-2 mb-2 lg:col-2 lg:mb-0">
                        <h5>RNE <span style="color: red;"></span></h5>
                        <input type="text" class="input-width" pInputText placeholder="0000000/a"
                            formControlName="rnecode">
                        <p-message severity="error" text="RNE code is required"
                            *ngIf="companyFormGroup.get('rnecode')?.hasError('required') && companyFormGroup.get('rnecode')?.touched">
                        </p-message>
                    </div>
                </div>


                <div class="row">
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 input-phonenumber">
                        <h5>Phone <span style="color: red;">*</span></h5>
                        <div class="flex items-center">
                            <div class="country-code-dropdown">
                                <p-dropdown [options]="countryCodes" (onChange)="onCountryCodeChange($event)"
                                    [showClear]="true" [filter]="true" formControlName="countrycode" placeholder="Code"
                                    appendTo="body">
                                    <ng-template let-option pTemplate="selectedItem">
                                        <img [src]="option.flagUrl" alt="Flag" class="flag-icon">
                                        {{ option.label }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        <img [src]="option.flagUrl" alt="Flag" class="flag-icon">
                                        {{ option.label }}
                                    </ng-template>
                                </p-dropdown>
                            </div>

                            <!-- Phone Number Input -->
                            <input type="text" class="input-width" pInputText placeholder="Phone"
                                formControlName="phonenumber">
                        </div>
                        <p-message severity="error" text="Phone number is required"
                            *ngIf="companyFormGroup.get('phonenumber')?.hasError('required') && companyFormGroup.get('phonenumber')?.touched">
                        </p-message>
                        <p-message severity="error" text="Phone number must be in correct form"
                            *ngIf="companyFormGroup.get('phonenumber')?.hasError('invalidPhoneNumber') && companyFormGroup.get('phonenumber')?.touched">
                        </p-message>
                        <p-message severity="error" text="Phone number must contain non-digit characters"
                            *ngIf="companyFormGroup.get('phonenumber')?.hasError('invalidPhoneNumber') && isNotANumber(companyFormGroup.get('phonenumber')?.value)">
                        </p-message>
                    </div>
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 input-email">
                        <h5>Email <span style="color: red;">*</span></h5>
                        <input type="email" class="input-width" pInputText placeholder="Email" formControlName="email">
                        <p-message severity="error" text="Email is required"
                            *ngIf="companyFormGroup.get('email')?.hasError('required') && companyFormGroup.get('email')?.touched">
                        </p-message>
                        <p-message severity="error" text="Email must be in a valid format"
                            *ngIf="companyFormGroup.get('email')?.hasError('email') && companyFormGroup.get('email')?.touched">
                        </p-message>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 input-addresses">
                        <h5>Address <span style="color: red;">*</span></h5>
                        <div class="flex items-center">

                            <!-- Address Input -->
                            <input type="text" class="input-width" pInputText placeholder="Address"
                                formControlName="address1">
                        </div>
                        <p-message severity="error" text="Address is required"
                            *ngIf="companyFormGroup.get('address1')?.hasError('required') && companyFormGroup.get('address1')?.touched">
                        </p-message>
                    </div>
                    <div class="col-3 mb-3 lg:col-3 lg:mb-0 input-addresses">
                        <h5>Longitude <span style="color: red;">*</span></h5>
                        <div class="flex items-center">
                            <input type="number" class="input-width" pInputText placeholder="Longitude"
                                formControlName="longitude">
                        </div>
                        <p-message severity="error" text="Longitude is required"
                            *ngIf="companyFormGroup.get('longitude')?.hasError('required') && companyFormGroup.get('longitude')?.touched">
                        </p-message>
                        <p-message severity="error" text="Longitude has to be between -180 & 180"
                            *ngIf="(companyFormGroup.get('longitude')?.hasError('max') ||
                            companyFormGroup.get('longitude')?.hasError('min')) && 
                            companyFormGroup.get('longitude')?.touched">
                        </p-message>
                        
                    </div>
                    <div class="col-3 mb-3 lg:col-3 lg:mb-0 input-addresses">
                        <h5>Latitude <span style="color: red;">*</span></h5>
                        <div class="flex items-center">
                            <input type="number" class="input-width" pInputText placeholder="Latitude"
                                formControlName="latitude">
                        </div>
                        <p-message severity="error" text="Latitude is required"
                            *ngIf="companyFormGroup.get('latitude')?.hasError('required') && companyFormGroup.get('latitude')?.touched">
                        </p-message>
                        <p-message severity="error" text="Latitude has to be between -90 & 90"
                            *ngIf="(companyFormGroup.get('latitude')?.hasError('max') || 
                                companyFormGroup.get('latitude')?.hasError('min')) && 
                                companyFormGroup.get('latitude')?.touched">
                        </p-message>
                        
                    </div>

                </div>
                <div class="row">
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 dd-entreprisetype" >
                        <h5>Enterprise Type <span style="color: red;">*</span></h5>
                        <p-dropdown [options]="entrepriseTypeDropdown" (onChange)="onEntrepriseTypeChange($event)"
                            optionLabel="label" optionValue="value" formControlName="entreprisetype"
                            class="input-width" [disabled]="mode =='edit'"></p-dropdown>
                    </div>
                    <div [hidden]="!isEnterpriseType" class="col-6 mb-6 lg:col-6 lg:mb-0 dd-servicetype">
                        <h5>Service Type <span style="color: red;">*</span></h5>
                        <p-dropdown [options]="serviceTypeDropdown" (onChange)="onServiceTypeChange($event)"
                            optionLabel="label" optionValue="value" formControlName="servicetype"
                            class="input-width"></p-dropdown>
                    </div>
                    <div [hidden]="isEnterpriseType" class="col-6 mb-6 lg:col-6 lg:mb-0 dd-categorytype">
                        <h5>Good & Product Type <span style="color: red;">*</span></h5>
                        <p-dropdown [options]="categoryTypeDropdown" (onChange)="onCategoryTypeChange($event)"
                            optionLabel="label" optionValue="value" formControlName="categorytype"
                            class="input-width"></p-dropdown>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 dd-feepercent" [hidden]="isEnterpriseType">
                        <h5>Client Fee % <span style="color: red;">*</span></h5>
                        <p-inputNumber class="zero-padding" pInputText placeholder="7%"
                            formControlName="clientFeePercentage" mode="decimal" [max]="100"
                            [minFractionDigits]="1"></p-inputNumber>
                        <p-message severity="error" text="Client fee percentage is required"
                            *ngIf="companyFormGroup.get('clientFeePercentage')?.hasError('required') && companyFormGroup.get('ClientFeePercentage')?.touched">
                        </p-message>
                        <p-message severity="error" text="Client fee percentage is a number"
                            *ngIf="companyFormGroup.get('clientFeePercentage')?.hasError('pattern') && companyFormGroup.get('ClientFeePercentage')?.touched">
                        </p-message>

                    </div>

                    <div class="col-6 mb-6 lg:col-6 lg:mb-0 dd-status">
                        <h5>Status <span style="color: red;">*</span></h5>
                        <p-dropdown [options]="statusDropdown" (onChange)="onStatusChange($event)" optionLabel="label"
                            optionValue="value" formControlName="status"></p-dropdown>
                    </div>

                </div>
                <div class="row">

                    <div formArrayName="paymentDetailsArray" class="col-12">
                        <div *ngFor="let paymentDetailsGroup of paymentDetailsArray.controls; let i = index;"
                            [formGroupName]="i">
                            <div class="row">
                                <!-- Delete Button -->
                                <div class="col-12">
                                    <p-button pRipple type="button" icon="pi pi-minus" label="" outlined="true"
                                        [pTooltip]="'Remove Payment Details'" (click)="deletePaymentDetailsGroup(i)"
                                        [style]="{'margin-right.px': '10'}" styleClass="p-button-outlined ">
                                        <span class="tooltip"></span></p-button>
                                </div>
                                <div class="col-6 mb-6 lg:col-6 lg:mb-0">
                                    <h6 class="left-aligned-label">Payment Method <span style="color: red;">*</span>
                                    </h6>
                                    <p-dropdown [options]="PaymentMethodDropdown"
                                        (onChange)="onPaymentMethodChange($event, i)" optionLabel="label"
                                        optionValue="value" formControlName="paymentMethod">
                                    </p-dropdown>
                                </div>
                                <div class="col-6 mb-6 lg:col-6 lg:mb-0">
                                    <h6>RIB <span style="color: red;">*</span></h6>
                                    <input type="text" class="input-width" pInputText placeholder="RIB"
                                        formControlName="rib" (change)="onRIBChange($event)">
                                    <p-message severity="error" text="RIB must be at least 20 digits"
                                        *ngIf="paymentDetailsGroup.get('rib')?.dirty && paymentDetailsGroup.get('rib')?.touched
                                        && paymentDetailsGroup.get('rib')?.value.length <= 20 && paymentDetailsGroup.get('rib')?.value">
                                    </p-message>
                                    <p-message severity="error" text="RIB is required"
                                        *ngIf="paymentDetailsGroup.get('rib')?.hasError('required') && paymentDetailsGroup.get('rib')?.touched">
                                    </p-message>
                                </div>
                                <div class="col-6 mb-6 lg:col-6 lg:mb-0">
                                    <h6>Payment Delay (days)</h6>
                                    <input type="number" class="input-width text-right" pInputText placeholder="30 days"
                                        formControlName="paymentDelay">
                                    <p-message severity="error" text="Payment delay should be a strict positive number"
                                        *ngIf="paymentDetailsGroup.get('paymentDelay')?.hasError('pattern') && paymentDetailsGroup.get('paymentDelay')?.touched">
                                    </p-message>
                                </div>
                                <div class="col-6 mb-6 lg:col-6 lg:mb-0" [hidden]="hideFeePercantage">
                                    <h6>Shop Fee % <span style="color: red;">*</span></h6>
                                    <p-inputNumber type="number" class="zero-padding text-right" pInputText
                                        placeholder="4%" formControlName="shopFeePercentage" [max]="100"
                                        [minFractionDigits]="1">
                                    </p-inputNumber>
                                    <p-message severity="error" text="Payment rate should be a strict positive number"
                                        *ngIf="paymentDetailsGroup.get('shopFeePercentage')?.hasError('pattern') && paymentDetailsGroup.get('shopFeePercentage')?.touched">
                                    </p-message>
                                    <p-message severity="error" text="Payment rate is required"
                                        *ngIf="paymentDetailsGroup.get('shopFeePercentage')?.hasError('required') && paymentDetailsGroup.get('shopFeePercentage')?.touched">
                                    </p-message>
                                </div>
                            </div>
                        </div>


                        <!-- Add Button -->
                        <div class="col-12">
                            <p-button pRipple type="button" icon="pi pi-plus" label="" outlined="true"
                                [pTooltip]="'Add Payment Details'" (click)="addPaymentDetailsGroup()"
                                [style]="{'margin-right.px': '10'}" styleClass="p-button-outlined ">
                                <span class="tooltip"></span></p-button>
                        </div>
                    </div>
                </div>

            </div>
        </div>

    </form>
    <ng-template pTemplate="footer" class="footer">
        <button pButton (click)="closeAddDialog()" label="Cancel" class="p-button-outlined "></button>
        <button pButton (click)="saveCompany()" label="" class="p-button-outlined p-button-success"
            [disabled]="loading">
            <span *ngIf="!loading">Save</span>
            <p-progressSpinner *ngIf="loading" styleClass="w-3rem h-1rem" strokeWidth="6"></p-progressSpinner>
        </button>

        <p-toast key="toast"></p-toast>
    </ng-template>
</p-dialog>