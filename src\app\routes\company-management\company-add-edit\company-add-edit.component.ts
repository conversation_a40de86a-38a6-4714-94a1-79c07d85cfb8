import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, ValidatorFn, Validators } from '@angular/forms';
import { CompanyService } from '../../../services/company.service'; // Adjust the path
import { Status } from 'src/app/shared/enums/status';
import { ActivatedRoute } from '@angular/router';
import { Company } from 'src/app/shared/models/company/company';
import { MessageService, SelectItem } from 'primeng/api';
import { EntrepriseType } from 'src/app/shared/enums/entreprise-type';
import { ServiceType } from 'src/app/shared/enums/service-type';
import { StatusCode } from 'src/app/shared/enums/StatusCode';
import { PaymentMethod } from 'src/app/shared/enums/PaymentMethod';
import { CountryCode, getCountries, getCountryCallingCode } from 'libphonenumber-js';
import { PhoneNumberValidator } from 'src/app/shared/validators/phoneNumberValidator';
import { Countries } from 'src/app/shared/enums/Countries';
import { CategoryType } from 'src/app/shared/enums/categoryType';



@Component({
    selector: 'app-company-add-edit',
    templateUrl: './company-add-edit.component.html',
    styleUrls: ['./company-add-edit.component.scss'],

})
export class CompanyAddEditComponent implements OnChanges {
    @Output() closeAddDialogEvent = new EventEmitter();
    @Output() companyAdded = new EventEmitter<void>();
    @Input() companyId: string = "";
    @Input() selectedCompany: Company | null = null;
    @Input() display: boolean = false;
    isvalid: boolean = true;
    statusDropdown: SelectItem[] = [];
    entrepriseTypeDropdown: SelectItem[] = [];
    serviceTypeDropdown: SelectItem[] = [];
    categoryTypeDropdown: SelectItem[] = [];
    selectedStatus: Status = Status.Active;
    selectedEntrepriseType: EntrepriseType = EntrepriseType.Enterprise;
    selectedServiceType: ServiceType = ServiceType.Ticket;
    selectedCategoryType: CategoryType = CategoryType.Restaurant;
    companyFormGroup: FormGroup;
    fileName: string = '';
    selectedImage: string | ArrayBuffer | null = null;
    lastSelectedImage: string | ArrayBuffer | null = null;
    mode: string = "";
    PaymentMethodDropdown: SelectItem[] = [];
    hideFeePercantage: boolean = false;
    countryCodes: any[] = [];
    selectedCountryCode: CountryCode | null = null;
    countryCallingCodes: { country: string, callingCode: string }[] = [];
    flagPath = 'assets/demo/images/flag/';
    includedCountryCodes: any;
    initialValidatorsForClientFeepercent: any;
    initialValidatorsForType: any;
    isEnterpriseType: boolean = true;
    loading = false;
    imageTypeIncorrect : boolean = false;
    imageSizeIncorrect : boolean = false;

    constructor(private companyService: CompanyService,
        private formBuilder: FormBuilder, private route: ActivatedRoute,
        private messageService: MessageService,
        private cdRef: ChangeDetectorRef) {
        this.statusDropdown = this.enumToArray(Status);
        this.entrepriseTypeDropdown = this.enumToArray(EntrepriseType);
        this.serviceTypeDropdown = this.enumToArray(ServiceType);
        this.PaymentMethodDropdown = this.enumToArray(PaymentMethod);
        this.categoryTypeDropdown = this.enumToArray(CategoryType);
        this.includedCountryCodes = ['TN', 'DZ', 'MA', 'LY', 'SS', 'FR', 'MR', 'MT', 'EG', 'BH', 'KE', 'DJ'];
        this.initialValidatorsForClientFeepercent = [
            Validators.required,
            Validators.pattern(/^(?!0+(\.0*)?$)\d+(\.\d{1,3})?$/)
        ];

        this.initialValidatorsForType = [
            Validators.required
        ]

        this.companyFormGroup = this.formBuilder.group({
            id: [""],
            name: ["", Validators.required],
            taxcode: ["", Validators.required],
            rnecode: ["", Validators.required],
            email: ['', [Validators.required, Validators.email]],
            address1: ["", [Validators.required]],
            address2: [''],
            longitude: [0, [Validators.required, Validators.max(180), Validators.min(-180)]],
            latitude: [0, [Validators.required, Validators.max(90), Validators.min(-90)]],
            phonenumber: ['', [Validators.required]],
            countrycode: [""],
            picture: [""],
            clientFeePercentage: ["", [Validators.pattern(/^(?!0+(\.0*)?$)\d+(\.\d{1,3})?$/)]],
            entreprisetype: ["", Validators.required],
            servicetype: ["", [Validators.required]],
            categorytype: [""],
            status: ["", Validators.required],
            paymentDetailsArray: this.formBuilder.array(
                [this.formBuilder.group({
                    paymentMethod: [this.PaymentMethodDropdown[0].value],
                    rib: ["", [Validators.required, Validators.pattern(/^\d{20,}$/)]],
                    paymentDelay: [30, Validators.pattern(/^(?!0+(\.0*)?$)\d+(\.\d{1,3})?$/)],
                    shopFeePercentage: [7, [Validators.required, Validators.pattern(/^(?!0+(\.0*)?$)\d+(\.\d{1,3})?$/)]]
                })
                ]
            ),
        });
        this.companyFormGroup.get('rnecode')?.disable()
        this.companyFormGroup.get('countrycode')?.valueChanges.subscribe((countryCode: string) => {

            const countryC = this.getCountryCode('+' + countryCode) as CountryCode;
            const phoneNumberControl = this.companyFormGroup.get('phonenumber');
            if (phoneNumberControl) {
                phoneNumberControl.clearValidators();
                phoneNumberControl.setValidators([Validators.required, PhoneNumberValidator.phoneValidator(countryC)]);
                phoneNumberControl.updateValueAndValidity();
            }
        });




        this.getCountryCodes();

    }
    isNotANumber(value: any): boolean {
        return isNaN(value);
    }
    //get TN from +216
    getCountryCode(countryValue: string): string | undefined {
        for (const countryCode of Object.values(Countries)) {
            if (countryCode === countryValue) {
                return Object.keys(Countries).find(key => Countries[key as keyof typeof Countries] === countryValue);
            }
        }
        return undefined;
    }

    getCountryValue(countryCode: string): string | undefined {
        const country = Object.entries(Countries).find(([key, value]) => value === countryCode);
        return country ? country[1] : undefined;
    }


    onCountryCodeChange(event: { originalEvent: Event, value: any }): void {
        console.log(this.countryCodes)
        this.companyFormGroup.controls['countrycode'].setValue(event.value);
    }
    getCountryCodes(): void {
        const countries = getCountries();
        this.countryCodes = countries
            .filter(country => this.includedCountryCodes.includes(country))
            .map(country => {
                const countryCode = getCountryCallingCode(country);
                const flagUrl = `${this.flagPath}${country.toUpperCase()}.webp`;
                return { label: `+${countryCode}`, value: countryCode, flagUrl: flagUrl };
            });
    }


    enumToArray(enumerable: any): SelectItem[] {
        return Object.keys(enumerable)
            .filter(key => !isNaN(Number(enumerable[key])))
            .map(key => ({ label: key, value: enumerable[key] }));
    }

    ngOnInit() {
        this.selectedImage = null;
    }

    ngOnChanges() {
        if (this.selectedCompany) {
            this.mode = 'edit';
            this.populateFormFromCompany(this.selectedCompany);
            this.isEnterpriseType = this.selectedCompany.entrepriseType == EntrepriseType.Enterprise;
            if (!this.isEnterpriseType) {
                this.companyFormGroup.get("clientFeePercentage")?.setValidators(this.initialValidatorsForClientFeepercent);
                this.companyFormGroup.get("clientFeePercentage")?.updateValueAndValidity();
                this.hideFeePercantage = false;
            } else {

                this.companyFormGroup.get("clientFeePercentage")?.clearValidators();
                this.companyFormGroup.get("clientFeePercentage")?.updateValueAndValidity();

            }
        } else {
            //this.companyFormGroup.reset();
            this.lastSelectedImage = null;
            this.mode = 'add';
            this.companyFormGroup.reset();
            this.companyFormGroup.get("countryCode")?.setValue('');
            this.isEnterpriseType = true;
            this.hideFeePercantage = true;
            this.paymentDetailsArray.clear();
            this.addPaymentDetailsGroup();
        }

        this.imageSizeIncorrect = false;
        this.imageTypeIncorrect = false;

    }

    get paymentDetailsArray(): FormArray {
        return this.companyFormGroup.get('paymentDetailsArray') as FormArray;
    }
    markPaymentDetailsTouched(): void {
        const ticketsArray: FormArray = this.companyFormGroup.get('paymentDetailsArray') as FormArray;
        for (let index = 0; index < this.paymentDetailsArray.length; index++) {
            const paymentGroup: FormGroup = this.paymentDetailsArray.at(index) as FormGroup;
            const ribControl = paymentGroup.get('rib');
            ribControl?.markAsTouched();
        }
        this.cdRef.detectChanges();
    }
    addPaymentDetailsGroup() {
        const paymentDetailsGroup = this.formBuilder.group({
            paymentMethod: [this.PaymentMethodDropdown[0].value],
            rib: ["", [Validators.required, Validators.pattern(/^\d{20,}$/)]],
            paymentDelay: [30, Validators.pattern(/^(?!0+(\.0*)?$)\d+(\.\d{1,3})?$/)],
            shopFeePercentage: [7, Validators.pattern(/^(?!0+(\.0*)?$)\d+(\.\d{1,3})?$/)]
        });
        this.paymentDetailsArray.push(paymentDetailsGroup);

    }

    deletePaymentDetailsGroup(index: number) {
        this.paymentDetailsArray.removeAt(index);
    }

    populateFormFromCompany(company: Company) {
        if (company.id) {
            const ticketsArray = this.companyFormGroup.get('paymentDetailsArray') as FormArray;
            ticketsArray.clear();
            if (company.paymentDetails && Array.isArray(company.paymentDetails)) {
                company.paymentDetails.forEach((paymentDetails) => {
                    let PaymentDetailsGroup = this.formBuilder.group({
                        paymentMethod: [paymentDetails.paymentMethod],
                        rib: [paymentDetails.rib, [Validators.required, Validators.pattern(/^\d{20,}$/)]],
                        paymentDelay: [paymentDetails.paymentDelay, Validators.pattern(/^(?!0+(\.0*)?$)\d+(\.\d{1,3})?$/)],
                        shopFeePercentage: [paymentDetails.shopFeePercentage, [Validators.pattern(/^(?!0$)\d+$/)]]
                    });
                    ticketsArray.push(PaymentDetailsGroup);
                    this.cdRef.detectChanges();

                })
            }
            const addressesArray = company.addresses;
            // Update the form with the fetched flag URL
            this.companyFormGroup.patchValue({
                id: company.id,
                name: company.name,
                taxcode: company.taxCode,
                rnecode: company.rneCode,
                email: company.email,
                phonenumber: company.phoneNumber,
                countrycode: company.countryCode ? company.countryCode.replace("+", "") : '', // Ensure countryCode is properly set
                clientFeePercentage: company.clientFeePercentage,
                entreprisetype: company.entrepriseType,
                servicetype: company.serviceType,
                categorytype: company.categoryType,
                status: company.status,
                address1: addressesArray.length > 0 ? addressesArray[0].fullAddress : '', // Populate first address
                address2: addressesArray.length > 1 ? addressesArray[1].fullAddress : '', // Populate second address if available
                longitude: addressesArray.length > 0 ? addressesArray[0].longitude : null,
                latitude: addressesArray.length > 0 ? addressesArray[0].latitude : null,
                paymentDetailsArray: ticketsArray.value,
                picture: company.picture
            });

            this.lastSelectedImage = company.picture;
            this.selectedEntrepriseType = company.entrepriseType;
            this.updateValidator();

        } else {
            console.log("no payment details found");
        }
    }
    fetchFlagUrl(countryCode: string) {
        return `${this.flagPath}${countryCode.toUpperCase()}.webp`;
    }


    getCompanybyId(id: string) {
        this.companyService.getCompanyById(id).subscribe(company => {
            this.companyFormGroup.setValue({
                name: company.objectValue.name,
                taxcode: company.objectValue.taxCode,
                rnecode: company.objectValue.rneCode,
                email: company.objectValue.email,
                phoneNnumber: company.objectValue.phoneNumber,
                countrycode: company.objectValue.countryCode,
                addresses: company.objectValue.addresses,
                clientFeePercentage: company.objectValue.clientFeePercentage,
                enterpisetype: company.objectValue.entrepriseType,
                servicetype: company.objectValue.serviceType,
                categorytype: company.objectValue.categoryType,
                Status: company.objectValue.status
            });
        });
    }
    isSaveDisabled(): boolean {
        return this.companyFormGroup.invalid ;
    }
    saveCompany() {
        debugger
        // Mark all form controls as touched
        Object.values(this.companyFormGroup.controls).forEach(control => {
            control.markAsTouched();
        });

        Object.keys(this.companyFormGroup.controls).forEach(field => {
            const control = this.companyFormGroup.get(field);
            if (control && control.invalid) {
                control.markAsTouched();
                const errors = control.errors;
                console.log(`Invalid input in ${field}: `, errors);
            }
        });
        this.markPaymentDetailsTouched();
        if(this.isSaveDisabled())
            {
                this.loading=false;
                return;
            }
            this.loading = true;
        const address1Value = this.companyFormGroup.get('address1')?.value;
        const longitudeValue = this.companyFormGroup.get('longitude')?.value;
        const latitudeValue = this.companyFormGroup.get('latitude')?.value;
        const address2Value = this.companyFormGroup.get('address2')?.value;

        const addressesArray = [];
        if (address1Value) {
            addressesArray.push({ fulladdress: address1Value, longitude: longitudeValue, latitude: latitudeValue });
        }
        if (address2Value) {
            addressesArray.push({ fulladdress: address2Value });
        }
        const companyToSave = {
            ...this.companyFormGroup.value,
            rnecode: this.companyFormGroup.get("rnecode")?.value,
            addresses: addressesArray,
            paymentDetails: this.companyFormGroup.value.paymentDetailsArray
        };

        companyToSave.countrycode = '+' + companyToSave.countrycode;
        !this.isEnterpriseType ? companyToSave.categorytype = this.selectedCategoryType : companyToSave.categorytype = null;
        if (this.mode === 'add') {
            delete companyToSave['id'];
            
            this.companyService.addCompany(companyToSave).subscribe((response) => {
                if (response.statusCode == StatusCode.Created) {
                    this.messageService.add({ key: 'toast', severity: 'success', summary: 'Company added successfully', detail: response.exceptionMessage });
                    this.companyAdded.emit();
                    this.closeAddDialogEvent.emit(false);
                }
                else {
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Add Company', detail: response.exceptionMessage });
                    this.closeAddDialogEvent.emit(false);
                }
                this.loading = false;
            });
        } else if (this.mode === 'edit') {
            const companyId = this.companyId;
            if (this.selectedImage == null) {
                companyToSave.picture = this.lastSelectedImage;
            }
            if(this.companyFormGroup.controls['paymentDetailsArray'].value[0].shopFeePercentage==null )
                {                    
                    this.companyFormGroup.controls['paymentDetailsArray'].value[0].shopFeePercentage=0.0;
                }
            this.companyService.updateCompany(companyToSave).subscribe((response) => {
                if (response.statusCode == StatusCode.Ok) {
                    this.messageService.add({ key: 'toast', severity: 'success', summary: 'Company updated successfully', detail: response.exceptionMessage });
                    this.companyAdded.emit();
                    this.closeAddDialogEvent.emit(false);
                } else {
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Update Company', detail: response.exceptionMessage });
                    this.closeAddDialogEvent.emit(false);
                }
                this.loading = false;
            });

        }

        this.selectedImage = null;
    }
    onVATChange(e: any) {
        if (e != null && e != undefined)
            this.companyFormGroup.get("rnecode")?.setValue(e.split(" ")[3]);
    }
    onStatusChange(event: any) {
        this.selectedStatus = event.value;
        console.log("form", this.companyFormGroup)
    }

    onEntrepriseTypeChange(event: any) {
        this.hideFeePercantage = false;
        this.selectedEntrepriseType = event.value;
        this.updateValidator();
    }
    updateValidator() {
        console.log(this.selectedEntrepriseType)
        if (this.selectedEntrepriseType == EntrepriseType.Enterprise) {
            this.companyFormGroup.get("clientFeePercentage")?.reset()
            const paymentsArray = this.companyFormGroup.get('paymentDetailsArray') as FormArray;
            paymentsArray.controls.forEach((control: any) => {
                console.log(control)
                control.get('shopFeePercentage').reset();
            });
            this.hideFeePercantage = true;
            this.companyFormGroup.get("clientFeePercentage")?.clearValidators();
            this.companyFormGroup.get("clientFeePercentage")?.updateValueAndValidity();

            this.companyFormGroup.get("categorytype")?.clearValidators();
            this.companyFormGroup.get("categorytype")?.updateValueAndValidity();

            this.companyFormGroup.get("servicetype")?.setValidators(this.initialValidatorsForType);
            this.companyFormGroup.get("servicetype")?.updateValueAndValidity();

            this.isEnterpriseType = true;
        } else {
            // Resetting validators for clientFeePercentage to initial validators
            this.companyFormGroup.get("servicetype")?.clearValidators();
            this.companyFormGroup.get("servicetype")?.updateValueAndValidity();

            this.companyFormGroup.get("clientFeePercentage")?.setValidators(this.initialValidatorsForClientFeepercent);
            this.companyFormGroup.get("clientFeePercentage")?.updateValueAndValidity();

            this.companyFormGroup.get("categorytype")?.setValidators(this.initialValidatorsForType);
            this.companyFormGroup.get("categorytype")?.updateValueAndValidity();
            this.isEnterpriseType = false;

        }
    }

    onServiceTypeChange(event: any) {
        this.selectedServiceType = event.value;
    }

    onCategoryTypeChange(event: any) {
        this.selectedCategoryType = event.value;
    }

    onPaymentMethodChange(event: any, groupIndex: number) {
    }

    onRIBChange(event: any) {
    }

    closeAddDialog() {
        this.closeAddDialogEvent.emit(false);
        this.selectedImage = null;
    }

    onFileSelected(event: any) {
        const file: File = event.target.files[0];
        const fileSizeInMB: number = file.size / (1024 * 1024);
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp']; 
        if (file) {
            if (!file.type || !allowedTypes.includes(file.type)) {
                console.log("Invalid file type. Please select a valid image file.");
                event.target.value = null;
                this.imageTypeIncorrect = true;
                return; 
            }
        
            if (fileSizeInMB > 0.5) {
                console.log("File size exceeds 500KB. Please select a smaller file.");
                event.target.value = null;
                this.imageSizeIncorrect = true;
                return; // Stop further processing
            }

            this.imageTypeIncorrect = false;
            this.imageSizeIncorrect = false;
            const reader = new FileReader();
            reader.readAsDataURL(file);

            const img = new Image();
            img.src = window.URL.createObjectURL( file );

            reader.onload = () => {
                setTimeout(() => {
                    const width = img.naturalWidth;
                    const height = img.naturalHeight;

                    window.URL.revokeObjectURL( img.src );
                    if( width > 300 || height > 300 ) {
                        this.imageSizeIncorrect = true;
                        return;
                    }
                    this.imageSizeIncorrect = false;
                    this.selectedImage = reader.result;
                })
                
                
            };
            this.fileName = file.name;

            // Convert the file to base64
            this.convertToBase64(file).then((result: string) => {
                // Update the picture field in the form group
                this.companyFormGroup.patchValue({
                    picture: result
                });
            }).catch(error => {
                console.error("Error converting file to base64:", error);
            });
        }
    }

    

    convertToBase64(file: File): Promise<string> {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = error => reject(error);
        });
    }
}



