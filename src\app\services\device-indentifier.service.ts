
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class DeviceIdentifierService {
  private deviceIdentifier: string | null;

  constructor() {
    // Check if the device identifier is already stored in a cookie
    this.deviceIdentifier = this.getDeviceIdentifierFromCookie();

    // If not found, generate a new device identifier
    if (!this.deviceIdentifier) {
      this.deviceIdentifier = this.generateUniqueDeviceIdentifier();
      // Store the device identifier in a cookie
      this.setDeviceIdentifierCookie(this.deviceIdentifier);
    }
  }

  getDeviceIdentifier(): string | null {
    return this.deviceIdentifier;
  }

  private generateUniqueDeviceIdentifier(): string {
    // Generate a unique identifier for the device (e.g., using UUID or other method)
    let uniqueId = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx';
    uniqueId = uniqueId.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
  return uniqueId
  }

  private setDeviceIdentifierCookie(identifier: string): void {
    // Store the device identifier in a cookie
    const expirationDate = new Date();
    expirationDate.setFullYear(expirationDate.getFullYear() + 1);

    // Format the expiration date to a string
    const expires = `expires=${expirationDate.toUTCString()}`;

    // Store the device identifier in a cookie with an expiration date
    document.cookie = `deviceId=${identifier}; ${expires}`;
  }

  private getDeviceIdentifierFromCookie(): string | null {
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [name, value] = cookie.split('=');
      if (name.trim() === 'deviceId') {
        return value;
      }
    }
    return null;
  }
}