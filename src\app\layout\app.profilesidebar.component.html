<p-sidebar [(visible)]="visible" position="right" [transitionOptions]="'.3s cubic-bezier(0, 0, 0.2, 1)'"
    styleClass="layout-profile-sidebar w-full sm:w-25rem">
    <div class="flex flex-column mx-auto md:mx-0">

        <div style="display: flex; align-items: center; justify-content: center;">
            <div>
                    <div style="width: 100px; height: 100px; display: flex; justify-content: center; align-items: center;">
                        <div *ngIf="isLoading">
                            <p-skeleton shape="circle" size="4rem" styleClass="mr-2"></p-skeleton>
                        </div>
                        <div *ngIf="!isLoading" style="padding-left: 100px;">
                            <p-avatar *ngIf="noImage" pBadge value="{{ notificationNumber }}"  icon="pi pi-user" styleClass="mr-2" size="large" [style]="{ 'width.px': 80, 'height.px':80, 'background-color': '#616166', color: '#EFF3F8' }">
                            </p-avatar>
                            <p-avatar *ngIf="!noImage" pBadge value="{{ notificationNumber }}" styleClass="mr-2" [style]="{ 'width.px': 80, 'height.px':80 , 'background-color': '#EFF3F8'}">
                                <img src="{{profilePictureUrl}}" style="width: 75%; height: 75%;">
                            </p-avatar>
                        </div>
                    </div>
                <span class="welcome-message mb-2 font-semibold">Welcome </span>
                <span class="user-name mb-2 font-semibold">{{ profile?.fullName }}</span>
                <br>
                <span class="user-email text-color-secondary font-medium">{{ profile?.email }}</span>
            </div>
        </div>


        <ul class="list-none m-0 p-0">
            <li>
                <a class="cursor-pointer flex surface-border mb-3 p-3 align-items-center border-1 surface-border border-round hover:surface-hover transition-colors transition-duration-150"
                    (click)="toggleEditProfilePopup()">
                    <span>
                        <i class="pi pi-user text-xl text-primary"></i>
                    </span>
                    <div class="ml-3">
                        <span class="mb-2 font-semibold">Profile</span>
                        <p class="text-color-secondary m-0">Edit your profile</p>
                    </div>
                </a>

                <app-edit-profile [display]="displayEditProfileDialog" (closeAddDialogEvent)="closeEditProfileDialog()"
                    (profileModified)="onProfileModified()"
                    (profilePictureChanged)="onProfilePictureChanged($event)"></app-edit-profile>


            </li>

            <li>
                <a class="cursor-pointer flex surface-border mb-3 p-3 align-items-center border-1 surface-border border-round hover:surface-hover transition-colors transition-duration-150"
                    (click)="onSettingButtonClick()">
                    <span>
                        <i class="pi pi-cog text-xl text-primary"></i>
                    </span>
                    <div class="ml-3">
                        <span class="mb-2 font-semibold">Settings</span>
                        <p class="text-color-secondary m-0">Change App settings</p>
                    </div>
                </a>
            </li>
            <li (click)="loading ? null : logout()" [ngClass]="{'loading': loading}">
                <a class="cursor-pointer flex surface-border mb-3 p-3 align-items-center border-1 surface-border border-round hover:surface-hover transition-colors transition-duration-150">
                    <span *ngIf="!loading">
                        <i class="pi pi-power-off text-xl text-primary"></i>
                    </span>
                    <div class="ml-3">
                        <span class="mb-2 font-semibold">Sign Out</span>
                        <p class="text-color-secondary m-0"></p>
                    </div>
                    <p-progressSpinner *ngIf="loading" styleClass="w-3rem h-2rem ml-3" strokeWidth="6"></p-progressSpinner>
                </a>
            </li>


            <li>
                <a
                    class="cursor-pointer flex surface-border mb-3 p-3 align-items-center border-1 surface-border border-round hover:surface-hover transition-colors transition-duration-150">
                    <span>
                        <i class="pi pi-bell text-xl text-primary"></i>
                    </span>
                    <div class="ml-3">
                        <span class="mb-2 font-semibold">notifications</span>
                        <app-notification></app-notification>
                    </div>
                </a>
            </li>
        </ul>
    </div>




</p-sidebar>
