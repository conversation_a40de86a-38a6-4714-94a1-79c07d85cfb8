import { AbstractControl, ValidatorFn } from '@angular/forms';
import { CountryCode, parsePhoneNumberFromString, PhoneNumber } from 'libphonenumber-js';

export class PhoneNumberValidator {
    static phoneValidator(countryCode: CountryCode | null): ValidatorFn {
        return (control: AbstractControl): { [key: string]: any } | null => {
            const phoneNumber: string = control.value;
            // Check if the phone number is empty or null
            if (!phoneNumber || phoneNumber.trim() === '') {
                return null; // No validation error if the phone number is empty
            }

            // Check if the phone number contains only digits
            if (!/^\d+$/.test(phoneNumber)) {
                return { 'invalidPhoneNumber': true }; // Validation error if the phone number contains non-digit characters
            }

            // Check if a country code is selected
            if (!countryCode) {
                return { 'invalidCountryCode': true }; // Validation error if no country code is selected
            }

            // Parse the phone number with the selected country code
            try {
                const parsedPhoneNumber = parsePhoneNumberFromString(phoneNumber, countryCode);
                return parsedPhoneNumber && parsedPhoneNumber.isValid() ? null : { 'invalidPhoneNumber': true };
            } catch (error) {
                console.error('Error parsing phone number:', error);
                return { 'invalidPhoneNumber': true }; // Validation error if an error occurs while parsing the phone number
            }
        };
    }
}