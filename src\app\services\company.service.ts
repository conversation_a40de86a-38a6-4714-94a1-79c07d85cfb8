import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { Company } from '../shared/models/company/company';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ResponseAPI } from '../shared/models/ResponseAPI';
import { environment } from 'src/environments/environment';
import { UserDTO } from '../shared/models/user/UserDTO';
import { Employee } from '../shared/models/employee/employee';
import { HttpResponse } from '@angular/common/http';
import { Ticket } from '../shared/models/ticket/ticket';
import { FilterMetadata } from 'primeng/api';
import { UserType } from '../shared/enums/UserType';
@Injectable({
    providedIn: 'root'
})
export class CompanyService {
    constructor(private http: HttpClient) {

    }
    getAllPaginationCompanies(pageSize: number, pageNumber: number, sortBy: string, sortDirection: number, filters: { [s: string]: FilterMetadata } | undefined): Observable<HttpResponse<ResponseAPI<Company[]>>> {

        let simplifiedFilters: { [s: string]: string } = {};
        if (filters) {
            for (const field in filters) {
                if (Object.prototype.hasOwnProperty.call(filters, field)) {
                    const filterValues = filters[field];

                    if (filterValues !== undefined && Array.isArray(filterValues) && filterValues.length > 0) {
                        const filterValue = filterValues[0]?.value;

                        if (filterValue !== undefined && filterValue !== null) {
                            simplifiedFilters[field] = filterValue.toString();
                        }
                    } else {
                        const filterValue = filterValues?.value;

                        if (filterValue !== undefined && filterValue !== null) {
                            simplifiedFilters[field] = filterValue.toString();
                        }
                    }
                }
            }
        }

        const requestBody = {
            pageSize,
            pageNumber,
            sortBy: sortBy.toString(),
            sortDirection: sortDirection.toString(),
            filters: simplifiedFilters,
        };
        return this.http.post<ResponseAPI<Company[]>>(`${environment.API}/Company/GetAllPaged`,requestBody, { observe: 'response', responseType: 'json' });
    }

    getAllCompanies() {
        return this.http.get<ResponseAPI<Company[]>>(`${environment.API}/Company/GetAll`);
    }

    getAllActiveCompanies(filterType: UserType) {
        return this.http.get<ResponseAPI<Company[]>>(`${environment.API}/Company/GetAllActive/${filterType}`);
    }


    getRecents(pageSize: number) {
        const params = new HttpParams().set('PageSize', pageSize.toString());
        return this.http.get<ResponseAPI<Company[]>>(`${environment.API}/Company/GetRecents`, { params });
    }
    getCompanyById(id?: string) {
        return this.http.get<ResponseAPI<Company>>(`${environment.API}/Company/Get/${id}`);
    }

    addCompany(company: Company) {
        return this.http.post<ResponseAPI<Company>>(`${environment.API}/Company/Create`, company);
    }

    updateCompany(company: Company) {
        return this.http.put<ResponseAPI<Company>>(`${environment.API}/Company/Update`, company);
    }

    deleteCompany(id: string) {
        return this.http.delete<ResponseAPI<void>>(`${environment.API}/Company/Delete/${id}`);
    }

    reactivateCompany(id: string) {
        return this.http.get<ResponseAPI<Company>>(`${environment.API}/Company/Reactivate/${id}`);
    }

    getEmployees(id: string) {
        return this.http.get<ResponseAPI<UserDTO[]>>(`${environment.API}/Company/GetAllEmployees/${id}`);
    }

    AssociateEmployeeToCompany(employee: Employee) {
        return this.http.post<ResponseAPI<UserDTO>>(`${environment.API}/Company/AssociateEmployeeToCompany`, employee);
    }
    UpdateEmployeeAssociation(employee: Employee) {
        return this.http.post<ResponseAPI<UserDTO>>(`${environment.API}/Company/UpdateEmployeeAssociation`, employee);
    }
    getEmployeeById(id: string) {
        return this.http.get<ResponseAPI<Employee>>(`${environment.API}/employees/${id}`);
    }
    deleteEmployee(employee: Employee) {
        return this.http.put<ResponseAPI<Employee>>(`${environment.API}/Company/DeleteEmployee`, employee);
    }
    
}
