import { NgModule } from '@angular/core';
import { ExtraOptions, RouterModule, Routes } from '@angular/router';
import { AppLayoutComponent } from './layout/app.layout.component';
import { ParentAuthGuard } from './ParentAuthGuard';
import { ForbiddenComponent } from './shared/components/forbidden/forbidden.component';

const routerOptions: ExtraOptions = {
    anchorScrolling: 'enabled'
};

const routes: Routes = [
    {
        path: '', component: AppLayoutComponent ,
        children: [
            { path: '', loadChildren: () => import('src/app/routes/secure-routes.module').then(m => m.SecureRoutesModule),canActivate : [ParentAuthGuard] }
        ],
    },

    {
        path: 'auth', component: AppLayoutComponent ,
        children: [
            { path: '', loadChildren: () => import('src/app/routes/routes.module').then(m => m.RoutesModule) }
        ],
    },
    {
        path: 'unauthorized', component: ForbiddenComponent
    }

];

@NgModule({
    imports: [RouterModule.forRoot(routes, routerOptions)],
    exports: [RouterModule]
})
export class AppRoutingModule { }
