import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Status } from 'src/app/shared/enums/status';
import { ActivatedRoute } from '@angular/router';
import { MessageService, SelectItem } from 'primeng/api';
import { v4 as uuidv4 } from 'uuid';
import { StatusCode } from 'src/app/shared/enums/StatusCode';
import { Ticket } from 'src/app/shared/models/ticket/ticket';
import { TicketService } from 'src/app/services/ticket.service';
import { WalletType } from 'src/app/shared/enums/wallet-type';
import { PeriodType } from 'src/app/shared/enums/PeriodType';
import { tick } from '@angular/core/testing';

@Component({
    selector: 'app-ticket-add-edit',
    templateUrl: './ticket-add-edit.component.html',
    styleUrls: ['./ticket-add-edit.component.scss']
})
export class TicketAddEditComponent implements OnChanges {
    @Output() closeAddDialogEvent = new EventEmitter();
    @Output() ticketAdded = new EventEmitter<void>();
    @Input() ticketId: string = "";
    @Input() selectedTicket: Ticket | null = null;
    @Input() display: boolean = false;
    isvalid: boolean = true;
    statusDropdown: SelectItem[] = [];
    ticketTypeDropdown: SelectItem[] = [];
    PeriodDropdown: SelectItem[] = [];
    selectedStatus: Status = Status.Active;
    selectedPeriod: PeriodType = PeriodType.None;
    ticketFormGroup: FormGroup;
    mode: string = "";
    selectedTicketType: any;
    displayPeriod: boolean = false;
    displayDates: boolean = false;
    validForm = false;
    totalAmount: number = 0;
    startDateMin = new Date();
    endDateMin = new Date();
    startDateMax = new Date("01/01/2050");
    loading: boolean = false;

    constructor(private ticketService: TicketService,
        private formBuilder: FormBuilder, private route: ActivatedRoute,
        private messageService: MessageService) {
        this.statusDropdown = this.enumToArray(Status);
        this.ticketTypeDropdown = this.enumToArray(WalletType);
        this.PeriodDropdown = this.enumToArray(PeriodType)
        this.ticketFormGroup = this.formBuilder.group({
            id: [],
            name: ["", Validators.required],
            amount: ["", [Validators.required, Validators.pattern(/^(?!0+(\.0*)?$)\d+(\.\d{1,3})?$/)]],
            quantity: ["", [Validators.required, Validators.pattern(/^(?!0$)\d+$/)]],
            totalAmount: ["", Validators.required],
            ticketType: ["", Validators.required],
            isAutomatic: [false],
            periodType: [PeriodType.None],
            startDate: [],
            endDate: [],
            status: [Status.Active, Validators.required]
        });
        this.ticketFormGroup.get('totalAmount')?.disable();
        if (this.mode == 'add') {
            this.ticketFormGroup.get('status')?.disable();
        }
    }
    enumToArray(enumerable: any): SelectItem[] {
        return Object.keys(enumerable)
            .filter(key => !isNaN(Number(enumerable[key])))
            .map(key => ({ label: key, value: enumerable[key] }));
    }
    valSwitch = true;
    ngOnInit() {

    }
    ngOnChanges() {
        if (this.selectedTicket) {
            console.log("edit");

            this.mode = 'edit';
            if (this.selectedTicket.isAutomatic) {
                this.displayDates = true;
            }
            this.populateFormFromTicket(this.selectedTicket);
        } else {
            console.log("add");
            this.ticketFormGroup.reset();
            this.mode = 'add';
            this.ticketFormGroup.reset();

        }

    }

    populateFormFromTicket(ticket: Ticket) {
        console.log('hi ticket', ticket);
        if (ticket.ticketId != null) {
            this.ticketFormGroup.setValue({
                id: ticket.ticketId,
                name: ticket.name,
                amount: ticket.amount,
                quantity: ticket.quantity,
                totalAmount: ticket.totalAmount,
                ticketType: ticket.type,
                isAutomatic: ticket.isAutomatic,
                periodType: ticket.periodType,
                startDate: new Date(ticket.startDate),
                endDate: new Date(ticket.endDate),
                status: ticket.status,
            });
            console.log('form control', this.ticketFormGroup);
        }
    }
    getTicketbyId(id: string) {
        this.ticketService.getTicketById(id).subscribe(response => {
            var ticket = response.objectValue;
            this.ticketFormGroup.setValue({
                id: ticket.ticketId,
                name: ticket.name,
                amount: ticket.amount,
                quantity: ticket.quantity,
                totalAmount: ticket.totalAmount,
                ticketType: ticket.type,
                isAutomatic: ticket.isAutomatic,
                periodType: ticket.periodType,
                startDate: ticket.startDate,
                endDate: ticket.endDate,
                status: ticket.status,
            });
        });
    }

    saveTicket() {
        this.loading = true;
        // Mark all form controls as touched
        Object.values(this.ticketFormGroup.controls).forEach(control => {
            control.markAsTouched();
        });

        if(!this.validForm )
            {
                this.loading=false;
                return;
            }
            this.loading = true;
        let ticketToSave: Ticket = {
            name: this.ticketFormGroup.get("name")?.value,
            amount: this.ticketFormGroup.get("amount")?.value,
            quantity: this.ticketFormGroup.get("quantity")?.value,
            totalAmount: this.ticketFormGroup.get("totalAmount")?.value,
            type: this.ticketFormGroup.get("ticketType")?.value,
            isAutomatic: false,
            periodType: this.ticketFormGroup.get("periodType")?.value,
            startDate: this.ticketFormGroup.get("startDate")?.value,
            endDate: this.ticketFormGroup.get("endDate")?.value,
            status: this.ticketFormGroup.get("status")?.value
        }

        console.log("ticket to save", ticketToSave);

        if (this.mode === 'add') {
            this.ticketService.addTicket(ticketToSave).subscribe((response) => {
                if (response.statusCode == StatusCode.Created) {
                    this.messageService.add({ key: 'toast', severity: 'success', summary: 'Ticket added successfully', detail: response.exceptionMessage });
                    this.ticketAdded.emit();
                    this.loading = false;
                    this.closeAddDialogEvent.emit(false); // Close the dialog
                }
                else {
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Add Ticket', detail: response.exceptionMessage });
                    this.loading = false;
                }
            });
        } else if (this.mode === 'edit') {
            ticketToSave.ticketId = this.selectedTicket?.ticketId
            this.ticketService.updateTicket(ticketToSave).subscribe((response) => {
                if (response.statusCode == StatusCode.Ok) {
                    this.messageService.add({ key: 'toast', severity: 'success', summary: 'Ticket updated successfully', detail: response.exceptionMessage });
                    this.ticketAdded.emit();
                    this.loading = false;
                    this.closeAddDialogEvent.emit(false); // Close the dialog
                } else {
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Update Ticket', detail: response.exceptionMessage });
                    this.loading = false;
                }

            });
        }
    }
    onNameChange(event: any) {
        this.isFormValid();
    }
    onAmountChange(event: any) {
        let totalAmount = this.ticketFormGroup.get('amount')?.value * this.ticketFormGroup.get('quantity')?.value;
        this.ticketFormGroup.get('totalAmount')?.setValue(totalAmount);
        this.isFormValid();
    }
    onQuantityChange(event: any) {
        let totalAmount = this.ticketFormGroup.get('amount')?.value * this.ticketFormGroup.get('quantity')?.value;
        this.ticketFormGroup.get('totalAmount')?.setValue(totalAmount);
        this.totalAmount = this.ticketFormGroup.get('totalAmount')?.value;
        console.log('totalamount', totalAmount)
        this.isFormValid();
    }
    onStatusChange(event: any) {
        this.selectedStatus = event.value;
        this.isFormValid();
    }
    onTicketTypeChange(event: any) {
        this.selectedTicketType = event.value;
        this.isFormValid();
    }
    onIsAutomaticChange(event: any) {
        console.log(event)
        this.displayDates = event.checked;
        this.ticketFormGroup.get('periodType')?.setValue(PeriodType.None)
        console.log("this.displayPeriod", this.displayPeriod)
        this.isFormValid();
    }
    onPeriodChange(event: any) {
        this.selectedPeriod = event.value;
        this.isFormValid();
    }
    onStartDateChange(event: any) {
        console.log("startdateevent", event)
        this.endDateMin = this.ticketFormGroup.get('startDate')?.value
        this.isFormValid();
    }
    onEndDateChange() {
        this.startDateMax = this.ticketFormGroup.get('endDate')?.value
        this.isFormValid();
    }
    isFormValid() {
        console.log(this.ticketFormGroup)
        if (this.ticketFormGroup.valid) {
            if (this.ticketFormGroup.get('isAutomatic')?.value == true && this.ticketFormGroup.get('startDate')?.value == null) {
                this.validForm = false;
            }
            else {
                this.validForm = true;
            }
        }
        else {
            this.validForm = false;
        }
    }


    closeAddDialog() {
        this.displayDates = false;
        this.closeAddDialogEvent.emit(false);
    }
}

