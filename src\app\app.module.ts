import { NgModule } from '@angular/core';
import { HashLocationStrategy, LocationStrategy } from '@angular/common';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { AppLayoutModule } from './layout/app.layout.module';
import { DialogModule } from 'primeng/dialog';
import { AuthentificationRoutingModule } from './routes/access-management/authentification/authentification-routing.module';
import { Interceptor } from 'src/core/interceptor';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { TermsComponent } from './shared/components/terms/terms.component';
import { PhoneNumberValidator } from './shared/validators/phoneNumberValidator';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import { ForbiddenComponent } from './shared/components/forbidden/forbidden.component';

@NgModule({
    declarations: [
        AppComponent,
        TermsComponent,
        ForbiddenComponent

    ],
    imports: [
        AppRoutingModule,
        AppLayoutModule,
        DialogModule,
        AuthentificationRoutingModule,
        ToastModule,
        TooltipModule,
    ],

    providers: [
        { provide: LocationStrategy, useClass: HashLocationStrategy },
        {
            provide: HTTP_INTERCEPTORS,
            useClass: Interceptor,
            multi: true
        }, PhoneNumberValidator
    ],

    bootstrap: [AppComponent]
})
export class AppModule { }
