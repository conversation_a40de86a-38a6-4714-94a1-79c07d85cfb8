<div class="grid">
    <div class="col-12 md:col-6 xl:col-3">
        <div class="card h-full">
            <span class="font-semibold text-lg">Cashback to validate</span>
            <div class="flex justify-content-between align-items-start mt-3">
                <div class="flex items-center">
                    <span class="text-4xl font-bold text-900 flex items-center">{{shopDemandsToValidate}}</span>
                    <div class="ml-2">
                        <span class="inline-block w-2"></span>
                        <span class="inline-block w-2"></span>
                        <img src="assets/demo/images/dashboard/receive.png" alt="Cashback to be validated"
                            class="h-6 w-6 ml-3">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 md:col-6 xl:col-3">
        <div class="card h-full">
            <span class="font-semibold text-lg">Transfer to validate</span>
            <div class="flex justify-content-between align-items-start mt-3">
                <div class="flex items-center">
                    <span class="text-4xl font-bold text-900 flex items-center">{{companyDemandsToValidate}}</span>
                    <div class="ml-2">
                        <span class="inline-block w-2"></span>
                        <span class="inline-block w-2"></span>
                        <img src="assets/demo/images/dashboard/wallet.png" alt="Cashback to be validated"
                        class="h-6 w-6 ml-2">
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="col-12 md:col-6 xl:col-3">
        <div class="card h-full">
            <span class="font-semibold text-lg">Sales On Companies</span>
            <div class="flex justify-content-between align-items-start mt-3">
                <div class="w-6">
                    <span class="text-4xl font-bold text-900">{{ companiesBalance }}</span>
                    <div [class]="companyRate >= 0 ? 'text-green-500' : 'text-pink-500'">
                        <span class="font-medium">{{ companyRate > 0 ? '+' + companyRate + '%' : companyRate + '%'
                            }}</span>
                        <i
                            [class]="companyRate >= 0 ? 'pi pi-arrow-up text-xs ml-2' : 'pi pi-arrow-down text-xs ml-2'"></i>
                    </div>
                </div>
                <div class="w-6">
                    <svg *ngIf="companyRate < 0" width="100%" viewBox="0 0 115 41" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M1.5 1L2.74444 2.61495C3.98889 4.2299 6.47778 7.4598 8.96667 9.07151C11.4556 10.6832 13.9444 10.6767 16.4333 11.6127C18.9222 12.5487 21.4111 14.4271 23.9 16.6724C26.3889 18.9178 28.8778 21.5301 31.3667 20.1977C33.8556 18.8652 36.3444 13.5878 38.8333 11.3638C41.3222 9.13969 43.8111 9.96891 46.3 11.9894C48.7889 14.0099 51.2778 17.2217 53.7667 16.2045C56.2556 15.1873 58.7444 9.9412 61.2333 11.2783C63.7222 12.6155 66.2111 20.5359 68.7 21.4684C71.1889 22.401 73.6778 16.3458 76.1667 16.0009C78.6556 15.6561 81.1444 21.0217 83.6333 24.2684C86.1222 27.515 88.6111 28.6428 91.1 27.4369C93.5889 26.2311 96.0778 22.6916 98.5667 22.7117C101.056 22.7317 103.544 26.3112 106.033 29.7859C108.522 33.2605 111.011 36.6302 112.256 38.3151L113.5 40"
                            style="stroke-width:1px;stroke:var(--pink-500)" />
                    </svg>
                    <svg *ngIf="companyRate >= 0" width="100%" viewBox="0 0 258 96" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M1 93.9506L4.5641 94.3162C8.12821 94.6817 15.2564 95.4128 22.3846 89.6451C29.5128 83.8774 36.641 71.6109 43.7692 64.4063C50.8974 57.2018 58.0256 55.0592 65.1538 58.9268C72.2821 62.7945 79.4103 72.6725 86.5385 73.5441C93.6667 74.4157 100.795 66.2809 107.923 65.9287C115.051 65.5765 122.179 73.0068 129.308 66.8232C136.436 60.6396 143.564 40.8422 150.692 27.9257C157.821 15.0093 164.949 8.97393 172.077 6.43766C179.205 3.9014 186.333 4.86425 193.462 12.0629C200.59 19.2616 207.718 32.696 214.846 31.0487C221.974 29.4014 229.103 12.6723 236.231 5.64525C243.359 -1.38178 250.487 1.29325 254.051 2.63076L257.615 3.96827"
                            style="stroke-width:2px;stroke:var(--primary-color)" stroke="10" />
                    </svg>
                </div>
            </div>
        </div>
    </div>
    <div class="col-12 md:col-6 xl:col-3">
        <div class="card h-full">
            <span class="font-semibold text-lg">Sales On Shops</span>
            <div class="flex justify-content-between align-items-start mt-3">
                <div class="w-6">
                    <span class="text-4xl font-bold text-900">{{ shopsBalance }}</span>
                    <div [class]="shopRate >= 0 ? 'text-green-500' : 'text-pink-500'">
                        <span class="font-medium">{{ shopRate > 0 ? '+' + shopRate + '%' : shopRate + '%' }}</span>
                        <i
                            [class]="shopRate >= 0 ? 'pi pi-arrow-up text-xs ml-2' : 'pi pi-arrow-down text-xs ml-2'"></i>
                    </div>
                </div>
                <div class="w-6">
                    <svg *ngIf="shopRate < 0" width="100%" viewBox="0 0 115 41" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M1.5 1L2.74444 2.61495C3.98889 4.2299 6.47778 7.4598 8.96667 9.07151C11.4556 10.6832 13.9444 10.6767 16.4333 11.6127C18.9222 12.5487 21.4111 14.4271 23.9 16.6724C26.3889 18.9178 28.8778 21.5301 31.3667 20.1977C33.8556 18.8652 36.3444 13.5878 38.8333 11.3638C41.3222 9.13969 43.8111 9.96891 46.3 11.9894C48.7889 14.0099 51.2778 17.2217 53.7667 16.2045C56.2556 15.1873 58.7444 9.9412 61.2333 11.2783C63.7222 12.6155 66.2111 20.5359 68.7 21.4684C71.1889 22.401 73.6778 16.3458 76.1667 16.0009C78.6556 15.6561 81.1444 21.0217 83.6333 24.2684C86.1222 27.515 88.6111 28.6428 91.1 27.4369C93.5889 26.2311 96.0778 22.6916 98.5667 22.7117C101.056 22.7317 103.544 26.3112 106.033 29.7859C108.522 33.2605 111.011 36.6302 112.256 38.3151L113.5 40"
                            style="stroke-width:1px;stroke:var(--pink-500)" />
                    </svg>
                    <svg *ngIf="shopRate >= 0" width="100%" viewBox="0 0 258 96" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M1 93.9506L4.5641 94.3162C8.12821 94.6817 15.2564 95.4128 22.3846 89.6451C29.5128 83.8774 36.641 71.6109 43.7692 64.4063C50.8974 57.2018 58.0256 55.0592 65.1538 58.9268C72.2821 62.7945 79.4103 72.6725 86.5385 73.5441C93.6667 74.4157 100.795 66.2809 107.923 65.9287C115.051 65.5765 122.179 73.0068 129.308 66.8232C136.436 60.6396 143.564 40.8422 150.692 27.9257C157.821 15.0093 164.949 8.97393 172.077 6.43766C179.205 3.9014 186.333 4.86425 193.462 12.0629C200.59 19.2616 207.718 32.696 214.846 31.0487C221.974 29.4014 229.103 12.6723 236.231 5.64525C243.359 -1.38178 250.487 1.29325 254.051 2.63076L257.615 3.96827"
                            style="stroke-width:2px;stroke:var(--primary-color)" stroke="10" />
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12 xl:col-6">
        <div class="card h-full">
            <div class="text-900 text-xl font-semibold mb-3">Recent Clients</div>

            <p-table [value]="recentCompanies" [paginator]="true" [rows]="6">
                <ng-template pTemplate="header">
                    <tr>
                        <th>Client </th>
                        <th>Type </th>
                        <th>Service </th>
                    </tr>
                </ng-template>

                <ng-template pTemplate="body" let-company>
                    <tr>
                        <td>
                            <div class="flex align-items-center">
                                <!-- <img [src]="'assets/demo/images/banking/amazon.png/'" [alt]="" width="75" class="shadow-2 flex-shrink-0" />-->
                                <div class="flex flex-column ml-1">
                                    <span class="font-medium text-lg mb-1">{{ company.name }}</span>
                                </div>
                            </div>
                        </td>
                        <td class="image-container">
                            <span class="font-medium text-lg mb-1">{{
                                getEntrepriseTypeString(company.entrepriseType)}}</span>
                        </td>
                        <td>
                            <span [class]="'service-badge service-' + getServiceTypeString(company.serviceType)">
                                {{ getServiceTypeString(company.serviceType) }}
                            </span>
                        </td>

                    </tr>
                </ng-template>
            </p-table>
        </div>
    </div>

    <div class="col-12 xl:col-6">
        <div class="card h-full">
            <div class="text-900 text-xl font-semibold mb-3">
                <span class="fas fa-bell fa-m" style="color: #f22c2c;"></span>
                Notifications
            </div>
            <app-notification></app-notification>
        </div>
    </div>

    <div class="col-12 xl:col-12">
        <div class="card h-full">
            <div class="flex align-items-start justify-content-between mb-6">
                <span class="text-900 text-xl font-semibold">Sales & Revenues </span>
            </div>
            <p-chart type="bar" [data]="barData" [options]="barOptions" height="300px"></p-chart>
        </div>
    </div>




    <div class="col-12 lg:col-6">
        <div class="card">
            <div class="flex flex-column md:flex-row md:align-items-start md:justify-content-between mb-3">
                <div class="text-900 text-xl font-semibold mb-3 md:mb-0">Companies Transactions</div>
                <div class="inline-flex align-items-center">

                    <button pButton pRipple icon="pi pi-upload" class="p-button-rounded mx-3"
                        (click)="dt.exportCSV()"></button>
                </div>
            </div>
            <p-table #dt [value]="salesOrders" [columns]="cols" [paginator]="true" [rows]="pageSize" responsiveLayout="scroll" [rowHover]="true"
            styleClass="p-datatable-gridlines" [paginator]="true" [lazy]="true" (onLazyLoad)="lazyCompanyTransactions($event)"
            [totalRecords]="totalRecords" [first]="first"
                [globalFilterFields]="['companyName','paymentMethod','status']" >
                <ng-template pTemplate="header">
                    <tr>
                        <th pSortableColumn="companyName" style="min-width:12rem" class="white-space-nowrap">Company
                            <p-sortIcon field="companyName"></p-sortIcon>
                        </th>
                        <th pSortableColumn="dynoAmount" style="min-width:10rem" class="white-space-nowrap">Dyno Amount
                            <p-sortIcon field="dynoAmount"></p-sortIcon>
                        </th>
                        <th pSortableColumn="totalAmount" style="min-width:10rem" class="white-space-nowrap">Total
                            Amount
                            <p-sortIcon field="totalAmount"></p-sortIcon>
                        </th>
                        <th pSortableColumn="paymentMethod" style="min-width:10rem" class="white-space-nowrap">Payment
                            <p-sortIcon field="paymentMethod"></p-sortIcon>
                        </th>
                        <th pSortableColumn="status" style="min-width:10rem" class="white-space-nowrap">Status
                            <p-sortIcon field="status"></p-sortIcon>
                        </th>
                            </tr>
                </ng-template>
                <ng-template pTemplate="body" let-salesOrder>
                    <tr>
                        <td>{{salesOrder.company.name}}</td>
                        <td>
                            <span class="p-column-title">Dyno Amount</span>
                            {{salesOrder.dynoAmount| currency:'TND'}}
                        </td>
                        <td>
                            <span class="p-column-title">Total Amount</span>
                            {{salesOrder.totalAmount | currency:'TND'}}
                        </td>
                        <td>
                            <span class="p-column-title">Payment</span>
                            {{ getPaymentMethodString(salesOrder.paymentMethod )}}
                        </td>
                        <td>
                            <span [class]="'component-badge status-' + getSalesOrderStatusString(salesOrder.status)">
                                {{ getSalesOrderStatusString(salesOrder.status)}}
                            </span>
                        </td>

                    </tr>
                </ng-template>
            </p-table>
        </div>
    </div>
    <div class="col-12 lg:col-6">
        <div class="card">
            <div class="flex flex-column md:flex-row md:align-items-start md:justify-content-between mb-3">
                <div class="text-900 text-xl font-semibold mb-3 md:mb-0">Shops Transactions</div>
                <div class="inline-flex align-items-center">
                    <button pButton pRipple icon="pi pi-upload" class="p-button-rounded mx-3"
                        (click)="dt.exportCSV()"></button>
                </div>
            </div>
            <p-table #dt [value]="cashBacks" [columns]="cols" [paginator]="true" [rows]="5" responsiveLayout="scroll"
                [globalFilterFields]="['companyName','walletBalance','status']">
                <ng-template pTemplate="header">
                    <tr>
                        <th pSortableColumn="companyName" style="min-width:12rem" class="white-space-nowrap">Company
                            <p-sortIcon field="companyName"></p-sortIcon>
                        </th>
                        <th pSortableColumn="walletBalance" style="min-width:10rem" class="white-space-nowrap">Wallet
                            Balance
                            <p-sortIcon field="walletBalance"></p-sortIcon>
                        </th>
                        <th pSortableColumn="dynoAmount" style="min-width:10rem" class="white-space-nowrap">Dyno Amount
                            <p-sortIcon field="dynoAmount"></p-sortIcon>
                        </th>
                        <th pSortableColumn="totalAmount" style="min-width:10rem" class="white-space-nowrap">Total
                            Amount
                            <p-sortIcon field="totalAmount"></p-sortIcon>
                        </th>
                        <th pSortableColumn="status" style="min-width:10rem" class="white-space-nowrap">Status
                            <p-sortIcon field="status"></p-sortIcon>
                        </th>
                           </tr>
                </ng-template>
                <ng-template pTemplate="body" let-cashback>
                    <tr>
                        <td>{{cashback?.company?.name.toString()}}</td>
                        <td>{{cashback?.walletBallance}}</td>
                        <td>
                            <span class="p-column-title">Dyno Amount</span>
                            {{cashback.dynoAmount| currency:'TND'}}
                        </td>
                        <td>
                            <span class="p-column-title">Total Amount</span>
                            {{cashback.totalAmount | currency:'TND'}}
                        </td>

                        <td>
                            <span [class]="'component-badge status-' + getCashbackStatusString(cashback.status)">
                                {{ getCashbackStatusString(cashback.status)}}
                            </span>
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </div>
    </div>


</div>
