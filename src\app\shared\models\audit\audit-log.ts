export interface AuditLog {
  id?: string;
  userId: string;
  userName: string;
  userRole: string;
  action: AuditAction;
  entityType: string;
  entityId: string;
  entityName?: string;
  oldValues?: any;
  newValues?: any;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  success: boolean;
  errorMessage?: string;
  sessionId?: string;
  correlationId?: string;
  additionalData?: { [key: string]: any };
}

export enum AuditAction {
  CREATE = 'CREATE',
  READ = 'READ',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  EXPORT = 'EXPORT',
  IMPORT = 'IMPORT',
  APPROVE = 'APPROVE',
  REJECT = 'REJECT',
  ACTIVATE = 'ACTIVATE',
  DEACTIVATE = 'DEACTIVATE',
  RESET_PASSWORD = 'RESET_PASSWORD',
  CHANGE_ROLE = 'CHANGE_ROLE',
  BULK_UPDATE = 'BULK_UPDATE',
  BULK_DELETE = 'BULK_DELETE'
}

export enum AuditEntityType {
  USER = 'User',
  COMPANY = 'Company',
  EMPLOYEE = 'Employee',
  ROLE = 'Role',
  TICKET = 'Ticket',
  TRANSACTION = 'Transaction',
  CASHBACK = 'Cashback',
  INVOICE = 'Invoice',
  GROUP = 'Group',
  NOTIFICATION = 'Notification',
  SYSTEM = 'System'
}

export interface AuditLogFilter {
  userId?: string;
  action?: AuditAction;
  entityType?: AuditEntityType;
  entityId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  success?: boolean;
  ipAddress?: string;
}

export interface AuditLogSummary {
  totalLogs: number;
  successfulActions: number;
  failedActions: number;
  uniqueUsers: number;
  mostCommonAction: AuditAction;
  mostActiveUser: string;
  actionDistribution: { [key in AuditAction]?: number };
  entityDistribution: { [key in AuditEntityType]?: number };
  hourlyDistribution: { [hour: number]: number };
  dailyDistribution: { [date: string]: number };
}

export class AuditLogBuilder {
  private auditLog: Partial<AuditLog> = {};

  static create(): AuditLogBuilder {
    return new AuditLogBuilder();
  }

  user(userId: string, userName: string, userRole: string): AuditLogBuilder {
    this.auditLog.userId = userId;
    this.auditLog.userName = userName;
    this.auditLog.userRole = userRole;
    return this;
  }

  action(action: AuditAction): AuditLogBuilder {
    this.auditLog.action = action;
    return this;
  }

  entity(type: AuditEntityType, id: string, name?: string): AuditLogBuilder {
    this.auditLog.entityType = type;
    this.auditLog.entityId = id;
    this.auditLog.entityName = name;
    return this;
  }

  changes(oldValues?: any, newValues?: any): AuditLogBuilder {
    this.auditLog.oldValues = oldValues;
    this.auditLog.newValues = newValues;
    return this;
  }

  context(ipAddress: string, userAgent: string, sessionId?: string): AuditLogBuilder {
    this.auditLog.ipAddress = ipAddress;
    this.auditLog.userAgent = userAgent;
    this.auditLog.sessionId = sessionId;
    return this;
  }

  result(success: boolean, errorMessage?: string): AuditLogBuilder {
    this.auditLog.success = success;
    this.auditLog.errorMessage = errorMessage;
    return this;
  }

  additionalData(data: { [key: string]: any }): AuditLogBuilder {
    this.auditLog.additionalData = data;
    return this;
  }

  correlationId(id: string): AuditLogBuilder {
    this.auditLog.correlationId = id;
    return this;
  }

  build(): AuditLog {
    this.auditLog.timestamp = new Date();
    this.auditLog.id = this.generateId();
    
    // Validation des champs requis
    if (!this.auditLog.userId || !this.auditLog.action || !this.auditLog.entityType) {
      throw new Error('Les champs userId, action et entityType sont requis');
    }

    return this.auditLog as AuditLog;
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}
