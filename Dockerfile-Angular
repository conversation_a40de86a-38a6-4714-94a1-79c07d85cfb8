# ---- Étape 1 : Builder ----
    FROM node:20 AS builder

    # Définir le répertoire de travail
    WORKDIR /app
    
    # Copier uniquement package.json et package-lock.json pour optimiser le cache Docker
    COPY ./package.json ./package-lock.json ./
    
    
    # Installer les dépendances
    RUN npm install -g npm@latest
    RUN npm install --force
    
    # Copier le reste des fichiers de l'application
    COPY . .
    
    # Construire l'application Angular
    RUN npm run build
    
    # ---- Étape 2 : Nginx ----
    FROM nginx:alpine
    
    # Définir le répertoire de travail de Nginx
    WORKDIR /usr/share/nginx/html
    
    # Copier les fichiers générés par Angular depuis la phase de build
    COPY --from=builder /app/dist/apollo-ng /usr/share/nginx/html
    
    # Modifier les permissions pour éviter les erreurs d'accès
    RUN chown -R nginx:nginx /usr/share/nginx/html
    
    # Copier la configuration Nginx
    WORKDIR /etc/nginx
    COPY ./nginx.conf ./conf.d/default.conf

    
    # Exposer le bon port (Nginx tourne sur 80)
    EXPOSE 80
    
    # Lancer Nginx en mode foreground
    CMD ["nginx", "-g", "daemon off;"]
    