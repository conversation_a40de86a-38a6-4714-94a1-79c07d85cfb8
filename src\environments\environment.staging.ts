// Environment de staging/test
export const environment = {
  production: false,
  environmentName: 'staging',
  
  // Configuration API
  API: 'https://api.dyno.staging.com/Api',
  NotificationAPI: 'https://notifications.dyno.staging.com',
  WebAPI: 'https://web.dyno.staging.com',
  
  // Configuration de stockage
  S3Url: 'https://dynofiles-staging.s3.amazonaws.com/',
  
  // Configuration de routage
  baseUrl: '',
  useHash: false,
  
  // Configuration de logging
  enableLogging: true,
  logLevel: 'info',
  
  // Configuration de cache
  cacheTimeout: 180000, // 3 minutes
  
  // Configuration de sécurité
  tokenRefreshBuffer: 60000, // 1 minute avant expiration
  maxRetryAttempts: 3,
  
  // Configuration de l'interface
  defaultPageSize: 15,
  maxPageSize: 100,
  
  // Configuration des features
  features: {
    enableAdvancedDashboard: true,
    enableAuditLogs: true,
    enableNotifications: true,
    enableExports: true
  }
};
