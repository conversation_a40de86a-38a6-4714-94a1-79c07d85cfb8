import { AfterViewInit, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Ticket } from '../../../models/ticket/ticket';
import { TicketService } from 'src/app/services/ticket.service';
import { Employee } from 'src/app/shared/models/employee/employee';

@Component({
    selector: 'app-send-ticket-to-employee-confirmation',
    templateUrl: './send-ticket-to-employee-confirmation.component.html',
    styleUrls: ['./send-ticket-to-employee-confirmation.component.scss']
})
export class SendTicketToEmployeeConfirmationComponent {
    @Input() display: boolean = false;
    @Input() selectedEmployee: Employee | null = null;
    @Output() confirm: EventEmitter<Ticket[]> = new EventEmitter<Ticket[]>();
    @Output() cancelSend = new EventEmitter<void>();
    ticketFormGroup: FormGroup;
    tickets?: Ticket[];
    loading = false;
    constructor(private formBuilder: FormBuilder, private ticketService: TicketService
    ) {
        this.ticketFormGroup = this.formBuilder.group({
            tickets: [[], Validators.required]
        });

    }

    ngOnInit() {
        this.ticketService.getAllTickets().subscribe(tickets => {
            if (tickets.objectValue != null) {
                this.tickets = tickets.objectValue
                console.log("tickets", this.tickets)
            }

        });
        console.log("alltickets", this.tickets)
    }
    confirmSend(tickets: Ticket[]) {
        this.loading = true;
        console.log("selcted tickets", tickets)
        this.confirm.emit(tickets);
        this.ticketFormGroup.reset();
    }
    onCancel() {
        this.ticketFormGroup.reset();
        this.cancelSend.emit();
    }
    onTicketValueChange(event: any) {
        console.log("ser", this.ticketFormGroup.get('tickets')?.value)
        console.log("event,", event)
    }
}
