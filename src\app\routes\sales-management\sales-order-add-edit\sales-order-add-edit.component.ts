import { Component, EventEmitter, Input, OnChanges, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { MessageService, SelectItem } from 'primeng/api';
import { SalesOrderService } from 'src/app/services/sales-order.service';
import { Currency } from 'src/app/shared/enums/Currency';
import { PaymentMethod } from 'src/app/shared/enums/PaymentMethod';
import { SalesOrderStatus } from 'src/app/shared/enums/SalesOrderStatus';
import { ProductType } from 'src/app/shared/enums/ProductType';
import { StatusCode } from 'src/app/shared/enums/StatusCode';
import { SalesOrder } from 'src/app/shared/models/salesOrder/salesOrder';
import { LocalStoreService } from 'src/app/services/local-store.service';
import { CompanyService } from 'src/app/services/company.service';
import jwtDecode from 'jwt-decode';
import { tokenDTO } from 'src/app/shared/models/authentification/token';
import { Company } from 'src/app/shared/models/company/company';

@Component({
    selector: 'app-sales-order-add-edit',
    templateUrl: './sales-order-add-edit.component.html',
    styleUrls: ['./sales-order-add-edit.component.scss']
})
export class SalesOrderAddEditComponent {
    @Output() closeAddDialogEvent = new EventEmitter();
    @Output() salesOrderAdded = new EventEmitter<void>();
    @Input() display: boolean = false;
    isvalid: boolean = true;
    productTypeDropdown: SelectItem[] = [];
    PaymentMethodDropdown: SelectItem[] = [];
    selectedPaymentMethod: PaymentMethod = PaymentMethod.Cash;
    salesOrderFormGroup: FormGroup;
    selectedProductType: any;
    validForm = false;
    totalAmount: number = 0;
    company: Company | null = null;
    netAmount: number = 0;
    vatPercentage: number = 19;
    vatAmount: number = 0;
    CompanyAdress: string = "";
    loading = false;
    constructor(private salesOrderService: SalesOrderService,
        private localStorageService: LocalStoreService,
        private companyService: CompanyService,
        private formBuilder: FormBuilder, private route: ActivatedRoute,
        private messageService: MessageService) {
        this.productTypeDropdown = this.enumToArray(ProductType);
        this.PaymentMethodDropdown = this.enumToArray(PaymentMethod)
        this.salesOrderFormGroup = this.formBuilder.group({
            productType: [this.productTypeDropdown[0].value, Validators.required],
            unitPrice: [, Validators.pattern(/^(?!0+(\.0*)?$)\d+(\.\d{1,3})?$/)],
            quantity: ["", Validators.pattern(/^(?!0$)\d+$/)],
            dynoAmount: ["", [Validators.required, Validators.max(777777), Validators.pattern(/^(?!0+(\.0*)?$)\d+(\.\d{1,3})?$/)]],
            paymentMethod: [this.PaymentMethodDropdown[0].value, Validators.required]
        });
    }
    enumToArray(enumerable: any): SelectItem[] {
        return Object.keys(enumerable)
            .filter(key => !isNaN(Number(enumerable[key])))
            .map(key => ({ label: key, value: enumerable[key] }));
    }
    valSwitch = true;
    ngOnInit() {
        let token = this.localStorageService.getData("Token");
        if (token != undefined) {
            let decodedToken: tokenDTO = jwtDecode(token);
            console.log("decodedToken", decodedToken.Company);
            this.companyService.getCompanyById(decodedToken.Company).subscribe((response) => {
                if (response.statusCode == StatusCode.Ok) {
                    this.company = response.objectValue
                    this.CompanyAdress = this.company?.addresses[0]?.fullAddress
                    console.log("company details", this.company)
                }
                else {
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error getting Company by id =' + decodedToken.Company, detail: response.exceptionMessage });
                }
            });;
        }
    }
    isSaveDisabled(): boolean {
        return this.salesOrderFormGroup.invalid;
    }
    saveSalesOrder() {
        // Mark all form controls as touched
        Object.values(this.salesOrderFormGroup.controls).forEach(control => {
            control.markAsTouched();
        });
        if (this.isSaveDisabled()) {
            this.loading = false;
            return;
        }
        this.loading = true;
        

        const salesOrderToSave = {
            ...this.salesOrderFormGroup.value,
            netAmount: Number(this.netAmount.toFixed(3)),
            vatAmount: Number(this.vatAmount.toFixed(3)),
            totalAmount: Number(this.totalAmount.toFixed(3))
        };
        //   let salesOrderToSave:SalesOrder={
        //       dynoAmount: this.salesOrderFormGroup.get("dynoAmount")?.value,
        //       amount: this.salesOrderFormGroup.get("amount")?.value,
        //       vatAmount: this.salesOrderFormGroup.get("vatAmount")?.value,
        //       totalAmount: this.salesOrderFormGroup.get("totalAmount")?.value,
        //       paymentMethod: this.salesOrderFormGroup.get("paymentMethod")?.value,
        //       type: this.salesOrderFormGroup.get("requestType")?.value
        //   }
        console.log("salesOrder to save", salesOrderToSave);

        this.salesOrderService.addSalesOrder(salesOrderToSave).subscribe((response) => {
            if (response.statusCode == StatusCode.Created) {
                this.messageService.add({ key: 'toast', severity: 'success', summary: 'Sales Order added successfully', detail: response.exceptionMessage });
                this.salesOrderAdded.emit();
                this.loading = false;
                this.closeAddDialogEvent.emit(false); // Close the dialog
            }
            else {
                this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Add Sales Order', detail: response.exceptionMessage });
                this.loading = false;
            }
        });

    }
    onUnitPriceChange(event: any) {
        let netAmount = this.salesOrderFormGroup.get('unitPrice')?.value * this.salesOrderFormGroup.get('quantity')?.value;
        this.salesOrderFormGroup.get('dynoAmount')?.setValue(netAmount);
        this.netAmount = this.salesOrderFormGroup.get('dynoAmount')?.value
        this.vatAmount = this.netAmount * (this.vatPercentage / 100);
        this.totalAmount = this.netAmount * (1 + this.vatPercentage / 100);
        this.validForm = this.salesOrderFormGroup.valid
    }
    onQuantityChange(event: any) {
        let dynoAmount = this.salesOrderFormGroup.get('unitPrice')?.value * this.salesOrderFormGroup.get('quantity')?.value;
        this.salesOrderFormGroup.get('dynoAmount')?.setValue(dynoAmount);
        this.netAmount = this.salesOrderFormGroup.get('dynoAmount')?.value
        this.vatAmount = this.netAmount * (this.vatPercentage / 100);
        this.totalAmount = this.netAmount * (1 + this.vatPercentage / 100);
        this.validForm = this.salesOrderFormGroup.valid
    }
    onDynoAmountChange(event: any) {
        this.netAmount = this.salesOrderFormGroup.get('dynoAmount')?.value
        this.vatAmount = this.netAmount * (this.vatPercentage / 100);
        this.totalAmount = this.netAmount * (1 + this.vatPercentage / 100);
        this.validForm = this.salesOrderFormGroup.valid
    }


    onProductTypeChange(event: any) {
        this.selectedProductType = event.value;
        this.validForm = this.salesOrderFormGroup.valid
    }

    onPaymentMethodChange(event: any) {
        this.selectedPaymentMethod = event.value;
        this.validForm = this.salesOrderFormGroup.valid
    }

    closeAddDialog() {
        this.resetData();
        this.closeAddDialogEvent.emit(false);
    }
    resetData() {
        this.netAmount = 0;
        this.vatAmount = 0;
        this.totalAmount = 0;
        this.salesOrderFormGroup.reset();
    }
}

