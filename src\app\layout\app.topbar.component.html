<div class="layout-topbar">
    <div class="topbar-start">
        <button #menubutton type="button" class="topbar-menubutton p-link p-trigger" (click)="onMenuButtonClick()">
            <i class="pi pi-bars"></i>
        </button>

        <app-breadcrumb class="topbar-breadcrumb"></app-breadcrumb>
    </div>

    <div class="topbar-end">
        <ul class="topbar-menu">
            <li> 
                <div class="card" style="padding: 0.5rem;">
                    <i class="pi pi-wallet" style="font-size: 1.2rem; margin-right: 10px;"></i>
                    <span class="text-l font-bold text-700" style="font-size: 1.2rem;">{{ walletAmount  }} Dynos </span>
                    <p-toast key="toast"></p-toast>
                </div>
            </li>
            <li class="topbar-profile">
                <button type="button" class="p-link" (click)="onProfileButtonClick()" style="padding-right: 50px;">
                    <div *ngIf="isLoading">
                        <p-skeleton shape="circle" size="4rem" styleClass="mr-2"></p-skeleton>
                    </div>
                    <div *ngIf="!isLoading">
                        <p-avatar *ngIf="noImage" pBadge value="{{ notificationNumber }}" icon="pi pi-user" styleClass="mr-2" size="xlarge" [style]="{ 'width.px': 60, 'height.px':60, 'background-color': '#616166', color: '#EFF3F8' }">
                        </p-avatar>
                        <p-avatar *ngIf="!noImage" pBadge value="{{ notificationNumber }}" styleClass="mr-2" [style]="{ 'width.px': 60, 'height.px':60 , 'background-color': '#EFF3F8'}">
                            <img src="{{profilePictureUrl}}" style="width: 60%; height: 60%;">
                        </p-avatar>
                    </div>
                </button>
            </li>
        </ul>
    </div>
</div>