import { Compo<PERSON>, <PERSON>ement<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { UserProfileDTO } from '../shared/models/user/UserProfileDTO';
import { AuthService } from '../services/auth.service';
import { Subscription } from 'rxjs';
import { ProfilePictureService } from '../services/profile-picture.service';
import { WalletService } from '../services/wallet.service';
import { BalanceSignalRService } from '../services/balanceSignalR.service';
import { MessageService } from 'primeng/api';
import { NotificationService } from '../services/notification.service';

@Component({
    selector: 'app-topbar',
    templateUrl: './app.topbar.component.html',
    styleUrls: ['./app.topbar.component.scss']
})
export class AppTopbarComponent implements OnInit, OnDestroy {
    profile: UserProfileDTO | null = null;
    @ViewChild('menubutton') menuButton!: ElementRef;
    profilePictureUrl: string = '';
    walletAmount: number = 0; 
    isLoading: boolean = true;
    noImage: boolean = false;
    private subscription!: Subscription;
    private profilePictureSubscription: Subscription | undefined;
    notificationNumber: number = 0;
    


    constructor(public layoutService: LayoutService, private authService: AuthService, 
        private profilePictureService: ProfilePictureService, private walletService: WalletService, private notificationService: NotificationService,
        private balanceSignalRService : BalanceSignalRService, private ngZone: NgZone, private messageService: MessageService) {
            this.getUserProfile();
            this.getNotifications();
    }


  ngOnInit(): void {
    
    this.profilePictureSubscription = this.profilePictureService.getProfilePictureUrl().subscribe(url => {
        this.profilePictureUrl = url;
        
    });
    this.walletService.GetUserWallets().subscribe(response => {
        if (response.statusCode === 200) {
            if(response.objectValue[0] != null) {
                this.walletAmount = response.objectValue[0].balance;
            }else {
                this.walletAmount = 0;
            }
            
        } else {
            console.error('Failed to fetch user wallets:', response.exceptionMessage);
        }
    });

    this.subscription = this.balanceSignalRService.notificationReceived.subscribe((notification) => {
        this.ngZone.run(() => {
            console.log(notification);
          this.walletAmount = this.walletAmount + notification;
          this.messageService.add({ key: 'toast', severity: 'success', summary: `The transfer amount of money ${notification} has been successfully updated`, detail: '' })
        });
        
      });

    this.getNotifications();
  }

  ngOnDestroy(): void {
    if (this.profilePictureSubscription) {
      this.profilePictureSubscription.unsubscribe();
    }

    this.subscription.unsubscribe();
  }


    onMenuButtonClick() {
        this.layoutService.onMenuToggle();
    }

    onProfileButtonClick() {
        this.layoutService.showProfileSidebar();
    }

    onConfigButtonClick() {
        this.layoutService.showConfigSidebar();
    }

    getUserProfile() {
        this.authService.getUserProfile().subscribe({
            next: (response) => {
                if (response.objectValue) {
                    this.profile = response.objectValue;
                    this.profilePictureUrl = this.profile?.picture;
                }
            },
            error: (err) => {
                console.log(err);
            },
            complete: () => {
                console.info('get profile complete');
                this.isLoading = false;
                if(this.profile?.picture ==  null) {
                    this.noImage = true          
                }    
            }
        });
    }

    onSearchButtonClick(event: Event) {
        const searchInputValue = (document.querySelector('.search-input') as HTMLInputElement).value;
        console.log('Search input value:', searchInputValue);
      }

    getNotifications () {
        this.notificationService.getAll().subscribe(
          (response) => {
              if (response.objectValue) {
                  this.notificationNumber = response.objectValue.length; // Keep only the last 5 notifications
              }
          });
    }
}