import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import jwtDecode from 'jwt-decode';
import { ConfirmationService, FilterMetadata, MessageService, SelectItem } from 'primeng/api';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { InvoicePdfService } from 'src/app/services/invoice-pdf.service';
import { InvoiceService } from 'src/app/services/invoice.service';
import { LocalStoreService } from 'src/app/services/local-store.service';
import { PaymentMethod } from 'src/app/shared/enums/PaymentMethod';
import { ProductType } from 'src/app/shared/enums/ProductType';
import { InvoiceStatus, InvoiceStatusString } from 'src/app/shared/enums/invoice-status';
import { tokenDTO } from 'src/app/shared/models/authentification/token';
import { Invoice } from 'src/app/shared/models/invoice/invoice';
import { environment } from 'src/environments/environment';

@Component({
    selector: 'app-valid-invoices',
    templateUrl: './valid-invoices.component.html',
    styleUrls: ['./valid-invoices.component.scss']
})
export class ValidInvoicesComponent implements OnInit {
    requestType: string = "";
    invoices: Invoice[] = [];
    failedInvoices: Invoice[] = [];
    Status: InvoiceStatus[] = [];
    selectedInvoiceId: string = "";
    rowGroupMetadata: any;
    selectedInvoice: Invoice | null = null;
    activityValues: number[] = [0, 100];
    isExpanded: boolean = false;
    idFrozen: boolean = false;
    loading: boolean = true;
    isUserSuperAdmin: boolean = false;
    invoiceStatusList = this.enumToArray(InvoiceStatus);
    productTypeList = this.enumToArray(ProductType);
    PaymentMethodList = this.enumToArray(PaymentMethod);
    S3URl: string = ""
    pageSize: number = 5;
    pageNumber: number = 0;
    first: number = 0;
    totalRecords: number = 1;
    sortBy?: string | string[] | null | undefined;
    sortDirection: number = 1;
    @ViewChild('filter') filter!: ElementRef;
    tableData: any;

    constructor(private route: ActivatedRoute,
        private invoiceService: InvoiceService,
        private invoicePDFService: InvoicePdfService,
        private localStorageService: LocalStoreService,
        private confirmationService: ConfirmationService,
        private messageService: MessageService) {
        this.S3URl = environment.S3Url;
    }


    ngOnInit(): void {
        // this.route.queryParams.subscribe(params => {
        //     console.log("Query Parameters:", params);
        //     this.requestType = params['requestType'];
        //     console.log("reqType", this.requestType);
        //     this.loadInvoices();
        // });


        let token = this.localStorageService.getData("Token");
        if (token != undefined) {
            let decodedToken: tokenDTO = jwtDecode(token);
            console.log("decodedToken", decodedToken.Role);
            let Roles = decodedToken.Role;
            if (Roles.includes("SuperAdmin")) {
                this.isUserSuperAdmin = true;
            }
        }

    }
    lazyLoadInvoice(event: TableLazyLoadEvent) {
        this.route.queryParams.subscribe(params => {
            console.log("Query Parameters:", params);
            this.requestType = params['requestType'];
            console.log("reqType", this.requestType);
            this.first = event.first || 0;
            this.sortBy = event.sortField ?? '';
            this.sortDirection = event.sortOrder as number;
            this.loadInvoices(event.filters as { [s: string]: FilterMetadata } | undefined);
        });


    }
    openPdf(filePath: string) {
        window.open(filePath, '_blank');
    }

    enumToArray(enumerable: any): SelectItem[] {
        return Object.keys(enumerable)
            .filter(key => !isNaN(Number(enumerable[key])))
            .map(key => ({ label: key, value: enumerable[key] }));
    }
    getStatusString(statusValue: number): string {
        return InvoiceStatusString[statusValue];
    }
    getProductTypeString(productTypeValue: number): string {
        return ProductType[productTypeValue];
    }
    getPaymentMethodString(payentMethodValue: number): string {
        return PaymentMethod[payentMethodValue];
    }

    loadInvoices(filters?: { [s: string]: FilterMetadata } | undefined) {
        if (this.requestType == "Valid") {
            this.pageNumber = (this.first / this.pageSize) + 1
            const sortBy = this.sortBy as string;
            const sortDirection = this.sortDirection;
            filters = filters || {};
            this.Status = [InvoiceStatus.InProgress, InvoiceStatus.TransactionSucceeded, InvoiceStatus.PDFGenerated, InvoiceStatus.PDFFailed, InvoiceStatus.EmailSent, InvoiceStatus.EmailFailed]
            this.invoiceService.getInvoicesByStatus(this.Status,this.pageSize, this.pageNumber, sortBy, sortDirection, filters).subscribe(response => {
                if (response.objectValue) {

                    this.invoices = response.objectValue;
                    this.tableData = this.invoices;
                    this.loading = false;
                }
            },
                (error) => {
                }
            );
        }
        else if (this.requestType == "Failed") {
            this.pageNumber = (this.first / this.pageSize) + 1
            const sortBy = this.sortBy as string;
            const sortDirection = this.sortDirection;
            filters = filters || {};
            this.Status = [InvoiceStatus.TransactionFailed]
            this.invoiceService.getInvoicesByStatus(this.Status,this.pageSize, this.pageNumber, sortBy, sortDirection, filters).subscribe(response => {
                if (response.objectValue) {
                    this.failedInvoices = response.objectValue;
                    this.tableData = this.failedInvoices;
                    this.loading = false;
                }
            },
                (error) => {
                }
            );

        }
        this.loading = false;
    }

    onSort() {
        //this.updateRowGroupMetaData();
    }

    onGlobalFilter(table: Table, event: Event) {
        table.filterGlobal((event.target as HTMLInputElement).value, 'contains');
    }

    clear(table: Table) {
        table.clear();
        this.filter.nativeElement.value = '';
    }

}
