import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';
import { Observable, catchError, switchMap, throwError } from 'rxjs';
import jwt_decode from "jwt-decode";
import { AuthService } from 'src/app/services/auth.service';
import { Router } from "@angular/router";
import { UserTokenDTO } from 'src/app/shared/models/authentification/UserTokenDTO';
import { LocalStoreService } from 'src/app/services/local-store.service';


@Injectable()
export class Interceptor implements HttpInterceptor {
  constructor(private router: Router, private authService: AuthService, private localStore: LocalStoreService) { }
  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    if (req.url.includes('login') || req.url.includes('register') || req.url.includes('ForgetPassword') || req.url.includes('ResetPassword') || req.url.includes('RefreshToken') || req.url.includes('MailConfirmed')) {
      const modifiedReq = req.clone({
        setHeaders: {
          'acceptLanguage': `en`
        }
      });
      return next.handle(modifiedReq);
      //return next.handle(req);

    }

    const token = this.localStore.getData('Token');
    const refreshToken = this.localStore.getData('RefreshToken');
    const expiredDate = this.convertStringToDate(this.localStore.getData('ExpiredDate'))
    const CompanyId = this.localStore.getData('CompanyId');

    const isExpired = token != null ? this.isTokenExpired(token) : true;
    if (isExpired && token != null && refreshToken != null && expiredDate != null) {
      var userToken: UserTokenDTO = {
        token: token,
        refreshToken: refreshToken,
        expiredDate: expiredDate
      };
      // this.authService.RefreshToken(userToken).subscribe({
      //     next: (response) => {
      //         this.localStore.saveData('Token', response.objectValue.token);
      //         this.localStore.saveData('RefreshToken', response.objectValue.refreshToken);
      //         this.localStore.saveData('ExpiredDate', response.objectValue.expiredDate);
      //     },
      //     error: (err) => {
      //       console.log(err)
      //     },
      //     complete: () => console.info('Session Logout, Due to Token expire')
      // });
      return this.authService.RefreshToken(userToken).pipe(
        switchMap((response) => {
          this.localStore.saveData('Token', response.objectValue.token);
          this.localStore.saveData('RefreshToken', response.objectValue.refreshToken);
          this.localStore.saveData('ExpiredDate', response.objectValue.expiredDate);

          // Continue with the original request after refreshing the token
          const tokenModifier = this.localStore.getData('Token');
          const modifiedReq = req.clone({
            setHeaders: {
              'Authorization': `Bearer ${tokenModifier}`,
              'acceptLanguage': "En"
            }
          });

          return next.handle(modifiedReq);
        }),
        catchError((err) => {
          console.log(err);
          // You might want to handle the error appropriately, e.g., redirect to login
          this.localStore.clearData();
          this.router.navigate(["auth/login"]);
          return throwError(err);
        })
      );
    }
    if (expiredDate != null && expiredDate <= new Date()) {
      console.log('Referesh Token expired')
      this.authService.logout().subscribe({
        next: (response) => {
          localStorage.clear();
        //   Avoid redirection here, let AuthGuard handle it
          //this.router.navigate(["auth/login"]);
        },
        error: (err) => {
          console.log(err)
        },
        complete: () => console.info('Session Logout, Due to Token expire')
      });
    }

    const tokenModifier = this.localStore.getData('Token');
    const modifiedReq = req.clone({
      setHeaders: {
        'Authorization': `Bearer ${tokenModifier}`,
        'acceptLanguage': "En"
      }
    });
    return next.handle(modifiedReq);
  }

  private isTokenExpired(token: string): boolean {
    if (token) {
      let decodedToken = jwt_decode<Token>(token);

      if (decodedToken && decodedToken.exp) {
        // Convert the 'exp' claim to a JavaScript Date object
        const expirationDate = new Date(decodedToken.exp * 1000); // Unix timestamp in seconds

        // Compare the expiration date with the current date
        return expirationDate <= new Date();
      }
    }

    return true; // Token is considered expired if no 'exp' claim is found
  }

  private convertStringToDate(value: string | null): Date | null {
    if (value === null) {
      return null; // Handle null case
    }
    const dateObject = new Date(value);

    if (isNaN(dateObject.getTime())) {
      return null; // Handle invalid date string
    }

    return dateObject;
  }
}

class Token {
  exp: number = 0
}
