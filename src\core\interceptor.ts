import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { Observable, catchError, switchMap, throwError, BehaviorSubject } from 'rxjs';
import { filter, take } from 'rxjs/operators';
import jwt_decode from "jwt-decode";
import { AuthService } from 'src/app/services/auth.service';
import { Router } from "@angular/router";
import { UserTokenDTO } from 'src/app/shared/models/authentification/UserTokenDTO';
import { LocalStoreService } from 'src/app/services/local-store.service';

@Injectable()
export class Interceptor implements HttpInterceptor {
  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);

  // URLs qui ne nécessitent pas d'authentification
  private readonly excludedUrls = [
    'login',
    'register',
    'ForgetPassword',
    'ResetPassword',
    'RefreshToken',
    'MailConfirmed'
  ];

  constructor(
    private router: Router,
    private authService: AuthService,
    private localStore: LocalStoreService
  ) { }

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // Vérifier si l'URL est exclue de l'authentification
    if (this.isExcludedUrl(req.url)) {
      return this.handlePublicRequest(req, next);
    }

    // Gérer les requêtes authentifiées
    return this.handleAuthenticatedRequest(req, next);
  }

  private isExcludedUrl(url: string): boolean {
    return this.excludedUrls.some(excludedUrl => url.includes(excludedUrl));
  }

  private handlePublicRequest(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const modifiedReq = req.clone({
      setHeaders: {
        'acceptLanguage': 'en'
      }
    });
    return next.handle(modifiedReq);
  }

  private handleAuthenticatedRequest(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const token = this.localStore.getData('Token');

    if (!token) {
      this.redirectToLogin();
      return throwError('No token available');
    }

    // Vérifier si le token est expiré
    if (this.isTokenExpired(token)) {
      return this.handleTokenRefresh(req, next);
    }

    // Ajouter le token à la requête
    const authenticatedReq = this.addTokenToRequest(req, token);

    return next.handle(authenticatedReq).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 401) {
          return this.handleTokenRefresh(req, next);
        }
        return throwError(error);
      })
    );
  }

  private handleTokenRefresh(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.refreshTokenSubject.next(null);

      const token = this.localStore.getData('Token');
      const refreshToken = this.localStore.getData('RefreshToken');
      const expiredDate = this.convertStringToDate(this.localStore.getData('ExpiredDate'));

      if (!token || !refreshToken) {
        this.redirectToLogin();
        return throwError('No refresh token available');
      }

      const userToken: UserTokenDTO = {
        token,
        refreshToken,
        expiredDate
      };

      return this.authService.RefreshToken(userToken).pipe(
        switchMap((response) => {
          this.isRefreshing = false;
          this.refreshTokenSubject.next(response.objectValue.token);

          // Sauvegarder les nouveaux tokens
          this.localStore.saveData('Token', response.objectValue.token);
          this.localStore.saveData('RefreshToken', response.objectValue.refreshToken);
          this.localStore.saveData('ExpiredDate', response.objectValue.expiredDate);

          // Relancer la requête originale avec le nouveau token
          const authenticatedReq = this.addTokenToRequest(req, response.objectValue.token);
          return next.handle(authenticatedReq);
        }),
        catchError((error) => {
          this.isRefreshing = false;
          this.redirectToLogin();
          return throwError(error);
        })
      );
    } else {
      // Si un refresh est déjà en cours, attendre qu'il se termine
      return this.refreshTokenSubject.pipe(
        filter(token => token != null),
        take(1),
        switchMap(token => {
          const authenticatedReq = this.addTokenToRequest(req, token);
          return next.handle(authenticatedReq);
        })
      );
    }
  }

  private addTokenToRequest(req: HttpRequest<any>, token: string): HttpRequest<any> {
    return req.clone({
      setHeaders: {
        'Authorization': `Bearer ${token}`,
        'acceptLanguage': 'en'
      }
    });
  }

  private redirectToLogin(): void {
    this.localStore.clearData();
    this.router.navigate(['auth/login']);
  }

  private isTokenExpired(token: string): boolean {
    if (token) {
      let decodedToken = jwt_decode<Token>(token);

      if (decodedToken && decodedToken.exp) {
        // Convert the 'exp' claim to a JavaScript Date object
        const expirationDate = new Date(decodedToken.exp * 1000); // Unix timestamp in seconds

        // Compare the expiration date with the current date
        return expirationDate <= new Date();
      }
    }

    return true; // Token is considered expired if no 'exp' claim is found
  }

  private convertStringToDate(value: string | null): Date | null {
    if (value === null) {
      return null; // Handle null case
    }
    const dateObject = new Date(value);

    if (isNaN(dateObject.getTime())) {
      return null; // Handle invalid date string
    }

    return dateObject;
  }
}

class Token {
  exp: number = 0
}
