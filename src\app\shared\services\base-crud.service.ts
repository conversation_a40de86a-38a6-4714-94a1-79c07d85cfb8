import { Injectable } from '@angular/core';
import { HttpClient, HttpResponse, HttpParams } from '@angular/common/http';
import { Observable, map, catchError } from 'rxjs';
import { ConfigService } from 'src/app/services/config.service';
import { ErrorHandlerService } from 'src/app/services/error-handler.service';
import { ResponseAPI } from '../models/ResponseAPI';
import { PaginationRequest, PaginationResponse, PaginationHelper } from '../models/pagination/pagination-request';

@Injectable()
export abstract class BaseCrudService<T, TCreate = T, TUpdate = T> {
  protected abstract readonly endpoint: string;

  constructor(
    protected http: HttpClient,
    protected config: ConfigService,
    protected errorHandler: ErrorHandlerService
  ) {}

  /**
   * Récupère tous les éléments avec pagination
   */
  getAllPaged(request: PaginationRequest): Observable<PaginationResponse<T>> {
    const requestBody = {
      pageSize: request.pageSize,
      pageNumber: request.pageNumber,
      sortBy: request.sortBy || '',
      sortDirection: request.sortDirection?.toString() || 'asc',
      filters: request.filters || {}
    };

    return this.http.post<ResponseAPI<T[]>>(
      this.getFullUrl('GetAllPaged'),
      requestBody,
      { observe: 'response', responseType: 'json' }
    ).pipe(
      map((response: HttpResponse<ResponseAPI<T[]>>) => {
        const data = response.body?.objectValue || [];
        const paginationInfo = PaginationHelper.extractPaginationFromHeaders(response.headers);
        
        return {
          data,
          totalCount: paginationInfo.totalCount || 0,
          pageNumber: paginationInfo.pageNumber || request.pageNumber,
          pageSize: paginationInfo.pageSize || request.pageSize,
          totalPages: paginationInfo.totalPages || 1,
          hasNextPage: paginationInfo.hasNextPage || false,
          hasPreviousPage: paginationInfo.hasPreviousPage || false
        } as PaginationResponse<T>;
      }),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Récupère tous les éléments sans pagination
   */
  getAll(): Observable<T[]> {
    return this.http.get<ResponseAPI<T[]>>(this.getFullUrl('GetAll')).pipe(
      map(response => response.objectValue || []),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Récupère un élément par son ID
   */
  getById(id: string): Observable<T> {
    return this.http.get<ResponseAPI<T>>(this.getFullUrl(`Get/${id}`)).pipe(
      map(response => {
        if (!response.objectValue) {
          throw new Error(`Élément avec l'ID ${id} non trouvé`);
        }
        return response.objectValue;
      }),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Crée un nouvel élément
   */
  create(item: TCreate): Observable<T> {
    return this.http.post<ResponseAPI<T>>(this.getFullUrl('Create'), item).pipe(
      map(response => {
        if (!response.objectValue) {
          throw new Error('Erreur lors de la création');
        }
        return response.objectValue;
      }),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Met à jour un élément existant
   */
  update(item: TUpdate): Observable<T> {
    return this.http.put<ResponseAPI<T>>(this.getFullUrl('Update'), item).pipe(
      map(response => {
        if (!response.objectValue) {
          throw new Error('Erreur lors de la mise à jour');
        }
        return response.objectValue;
      }),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Supprime un élément par son ID
   */
  delete(id: string): Observable<void> {
    return this.http.delete<ResponseAPI<void>>(this.getFullUrl(`Delete/${id}`)).pipe(
      map(() => void 0),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Récupère les éléments récents
   */
  getRecents(pageSize: number = 10): Observable<T[]> {
    const params = new HttpParams().set('PageSize', pageSize.toString());
    return this.http.get<ResponseAPI<T[]>>(this.getFullUrl('GetRecents'), { params }).pipe(
      map(response => response.objectValue || []),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Recherche d'éléments
   */
  search(query: string, pageSize: number = 10): Observable<T[]> {
    const params = new HttpParams()
      .set('query', query)
      .set('pageSize', pageSize.toString());
    
    return this.http.get<ResponseAPI<T[]>>(this.getFullUrl('Search'), { params }).pipe(
      map(response => response.objectValue || []),
      catchError(error => this.errorHandler.handleHttpError(error))
    );
  }

  /**
   * Construit l'URL complète pour un endpoint
   */
  protected getFullUrl(action: string): string {
    return this.config.getFullApiUrl(`${this.endpoint}/${action}`);
  }

  /**
   * Méthode utilitaire pour créer des paramètres HTTP
   */
  protected createHttpParams(params: { [key: string]: any }): HttpParams {
    let httpParams = new HttpParams();
    
    Object.keys(params).forEach(key => {
      const value = params[key];
      if (value !== null && value !== undefined) {
        httpParams = httpParams.set(key, value.toString());
      }
    });

    return httpParams;
  }
}
