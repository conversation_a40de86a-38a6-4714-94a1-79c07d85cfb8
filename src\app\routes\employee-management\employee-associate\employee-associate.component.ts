import { Component, EventEmitter, Input, OnChanges, Output, ChangeDetectorRef } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CompanyService } from '../../../services/company.service'; // Adjust the path
import { Status } from 'src/app/shared/enums/status';
import { ActivatedRoute } from '@angular/router';
import { Company } from 'src/app/shared/models/company/company';
import { MessageService, SelectItem } from 'primeng/api';
import { UserDTO } from 'src/app/shared/models/user/UserDTO';
import { UserService } from 'src/app/services/User.service';
import { Employee } from 'src/app/shared/models/employee/employee';
import { StatusCode } from 'src/app/shared/enums/StatusCode';
import { Group } from 'src/app/shared/models/Group/group';
import { GroupService } from 'src/app/services/group.service';
import { Observable, map } from 'rxjs';
import { DefaultRole } from 'src/app/shared/enums/DefaultRole';
import { CountryCode, getCountries, getCountryCallingCode } from 'libphonenumber-js';
import { PhoneNumberValidator } from 'src/app/shared/validators/phoneNumberValidator';
import { Countries } from 'src/app/shared/enums/Countries';

@Component({
    selector: 'app-employee-associate',
    templateUrl: './employee-associate.component.html',
    styleUrls: ['./employee-associate.component.scss']
})
export class EmployeeAssociateComponent implements OnChanges {
    @Output() closeAddDialogEvent = new EventEmitter();
    @Output() employeeAdded = new EventEmitter<void>();
    @Input() employeeId: number = 0;
    @Input() selectedEmployee: Employee | null = null;
    @Input() selectedCompany: Company | null = null;
    @Input() companyId: string = "";
    @Input() display: boolean = false;
    isvalid: boolean = true;
    statusDropdown: SelectItem[] = [];
    userDropdown: SelectItem[] = [];
    selectedStatus: Status = Status.Active;
    selectedUser: UserDTO | null = null;
    selectedGroup: Group | null = null;
    employee: Employee | null = null;
    associationFormGroup: FormGroup;
    mode: string = "";
    userSuggestions: any[] = [];
    groupSuggestions: any[] = [];
    isEditMode: boolean = false;
    countryCodes: any[] = [];
    selectedCountryCode: CountryCode | null = null;
    countryCallingCodes: { country: string, callingCode: string }[] = [];
    flagPath = 'assets/demo/images/flag/';
    includedCountryCodes: any;
    isValidPhoneNumber: boolean = false;
    userId: string = "";
    loading: boolean = false;

    constructor(private companyService: CompanyService, private userService: UserService,
        private formBuilder: FormBuilder, private route: ActivatedRoute, private cdr: ChangeDetectorRef, private groupService: GroupService,
        private messageService: MessageService) {
        this.statusDropdown = this.enumToArray(Status);
        this.userDropdown;
        this.includedCountryCodes = ['TN', 'DZ', 'MA', 'LY', 'SS', 'FR', 'MR', 'MT', 'EG', 'BH', 'KE', 'DJ'];

        this.associationFormGroup = this.formBuilder.group({
            companyId: [{ value: '' }],
            phonenumber: ["", Validators.required],
            countryCode: [""],
            status: ["", Validators.required],
            group: ["", Validators.required]
        });
        this.associationFormGroup.get('countryCode')?.valueChanges.subscribe((countryCode: string) => {
            const countryC = this.getCountryCode('+' + countryCode) as CountryCode;
            const phoneNumberControl = this.associationFormGroup.get('phonenumber');
            if (phoneNumberControl) {
                phoneNumberControl.clearValidators();
                phoneNumberControl.setValidators([Validators.required, PhoneNumberValidator.phoneValidator(countryC)]);
                phoneNumberControl.updateValueAndValidity();
            }
        });
        this.getCountryCodes();

    }
    isNotANumber(value: any): boolean {
        return isNaN(value);
    }
    //get TN from +216
    getCountryCode(countryValue: string): string | undefined {
        for (const countryCode of Object.values(Countries)) {
            if (countryCode === countryValue) {
                return Object.keys(Countries).find(key => Countries[key as keyof typeof Countries] === countryValue);
            }
        }
        return undefined;
    }

    getCountryValue(countryCode: string): string | undefined {
        const country = Object.entries(Countries).find(([key, value]) => value === countryCode);
        return country ? country[1] : undefined;
    }


    onCountryCodeChange(event: { originalEvent: Event, value: any }): void {

        this.associationFormGroup.controls['countryCode'].setValue(event.value);
    }

    getCountryCodes(): void {
        const countries = getCountries();
        this.countryCodes = countries
            .filter(country => this.includedCountryCodes.includes(country))
            .map(country => {
                const countryCode = getCountryCallingCode(country);
                const flagUrl = `${this.flagPath}${country.toUpperCase()}.webp`;
                return { label: `+${countryCode}`, value: countryCode, country: country, flagUrl: flagUrl }; // Include country code in the object
            });
    }

    enumToArray(enumerable: any): SelectItem[] {
        return Object.keys(enumerable)
            .filter(key => !isNaN(Number(enumerable[key])))
            .map(key => ({ label: key, value: enumerable[key] }));
    }
    ngOnInit() {
        this.searchGroups();
    }
    ngOnChanges() {
        if (this.selectedEmployee) {
            this.mode = 'edit';
            this.associationFormGroup.reset();
            this.populateFormFromEmployee(this.selectedEmployee);

        } else {
            this.mode = 'add';
            this.associationFormGroup.reset();
        }
    }
    getStatusString(statusValue: number): string {
        return Status[statusValue];
    }

    getGroupById(id: string) {
        return this.groupService.getGroupById(id).pipe(
            map(response => {
                if (response.statusCode === StatusCode.Ok) {
                    return response.objectValue;
                } else {
                    return null;
                }
            })
        );
    }

    getCompanyById(id: string) {
        return this.companyService.getCompanyById(id).pipe(
            map(response => {
                if (response.statusCode === StatusCode.Ok) {
                    return response.objectValue;
                } else {
                    return null;
                }
            })
        );
    }

    populateFormFromEmployee(employee: Employee) {

        if (employee.employeeId) {
            const countryCodeWithoutPlus = employee?.user?.countryCode?.substring(1);
            const countryCodeOption = this.countryCodes.find(option => option.value === countryCodeWithoutPlus).value;
            const selectedGroup = this.groupSuggestions.find(group => group.value.groupId === employee.groupId);

            this.associationFormGroup.patchValue({
                companyId: { label: this.selectedCompany?.name, value: this.employee?.companyId },
                phonenumber: employee?.user?.phoneNumber,
                countryCode: countryCodeOption,
                group: selectedGroup,
                status: this.getStatusString(employee.status),
            });
            this.userId = employee.userId;
            console.log("countryCode", this.associationFormGroup.get('countryCode')?.value);
            this.cdr.detectChanges();
        }

    }
    isSaveDisabled(): boolean {
        return this.associationFormGroup.invalid;
    }
    getDefaultRoleString(defaultRole: number): string {
        return DefaultRole[defaultRole];
    }

    searchUsers(event: any) {
        const query = event.query; // Assuming event.target.value contains the input value
        this.userService.getUsersByDefaultRole(this.getDefaultRoleString(DefaultRole.Client)).subscribe((response) => {
            if (response.statusCode == StatusCode.Ok) {
                this.userSuggestions = response.objectValue
                    .filter(user => user.phoneNumber && user.phoneNumber.includes(query))
                    .map(user => user.phoneNumber);
            }
        });

    }

    searchGroups() {
        this.groupService.getAllGroups().subscribe((response) => {
            if (response.statusCode == StatusCode.Ok) {
                this.groupSuggestions = response.objectValue.map(group => ({
                    label: group.name,
                    value: group
                }));
            }
        });
    }

    onUserSelect1(event: any) {

        const selectedPhoneNumber = event;
        this.userService.getUserIdByPhoneNumber(selectedPhoneNumber).subscribe(
            (response) => {
                if (response.statusCode === StatusCode.Ok && response.objectValue) {
                    this.selectedUser = response.objectValue;
                } else {
                    console.error('Failed to fetch user ID.');
                }
            },
            (error) => {
                console.error('Error occurred while fetching user ID:', error);
            }
        );
    }
    validatePhoneNumberInSuggestions(phoneNumber: string): boolean {
        return this.userSuggestions.includes(phoneNumber);
    }

    onUserSelect(event: any) {

        const selectedPhoneNumber = event;
        const phoneNumberControl = this.associationFormGroup.get('phonenumber');
        phoneNumberControl?.setValue(selectedPhoneNumber);

        this.userService.getUserIdByPhoneNumber(selectedPhoneNumber).subscribe(
            (response) => {
                if (response.statusCode === StatusCode.Ok && response.objectValue) {
                    this.selectedUser = response.objectValue;
                } else {
                    console.error('Failed to fetch user ID.');
                }
            },
            (error) => {
                console.error('Error occurred while fetching user ID:', error);
                this.messageService.add({ key: 'toast', severity: 'error', summary: 'Client Not found', detail: 'The phone number didnt match any client' });
                this.closeAddDialogEvent.emit(false);
            }
        );
    }

    onPhoneNumberChange(event: any) {

        if (!this.isValidPhoneNumber) {

            const phoneNumberControl = this.associationFormGroup.get('phonenumber');
            if (phoneNumberControl && !this.userSuggestions.includes(phoneNumberControl.value)) {
                // If phone number is not included in user suggestions
                phoneNumberControl.setErrors({ 'invalidPhoneNumber': true });
            }
        }
    }


    onGroupSelect1(event: any) {

        if (!this.selectedGroup) {
            this.selectedGroup = { groupId: event.value, name: "", employees: [], status: Status.Active, groupTickets: [], ticketNames: [] };
        } else {
            this.selectedGroup.groupId = event.value;
        }
    }

    onGroupSelect(event: any) {

        this.selectedGroup = event.value.value;

    }
    onGroupChange(event: any) {

        this.selectedGroup = event;
    }

    Associate() {
        
        Object.values(this.associationFormGroup.controls).forEach(control => {
            control.markAsTouched();
        });
        const phoneNumberControl = this.associationFormGroup.get('phonenumber');
        if (phoneNumberControl?.invalid) {
            phoneNumberControl.markAsTouched();
            return;
        }
        if(this.isSaveDisabled())
            {
                this.loading=false;
                return;
            }
            this.loading = true;
        const employeeToSave = { ...this.associationFormGroup.value };
        this.employee = {} as Employee;
        this.employee.companyId = this.companyId;



        if (this.selectedGroup) {

            const groupId = this.selectedGroup.groupId ? this.selectedGroup.groupId.toString() : '';
            this.employee.groupId = groupId;
        } else {

            const selectedGroupId = this.associationFormGroup.get('group')?.value.value.groupId;
            this.employee.groupId = selectedGroupId ? selectedGroupId.toString() : '';
        }

        if (this.selectedStatus) {
            this.employee.status = this.selectedStatus;
        } else {
            this.employee.status = this.associationFormGroup.get('status')?.value;
        }

        if (this.selectedEmployee) {
            this.employee.employeeId = this.selectedEmployee.employeeId;
        }

        if (this.mode === 'add') {

            if (this.selectedUser) {

                this.employee.userId = this.selectedUser.id;
            }
            else {

                const phoneNumberControl = this.associationFormGroup.get('phonenumber');
                if (phoneNumberControl && !this.userSuggestions.includes(phoneNumberControl.value)) {
                    // If phone number is not included in user suggestions
                    phoneNumberControl.setErrors({ 'invalidPhoneNumber': true });
                }
            }
            this.companyService.AssociateEmployeeToCompany(this.employee).subscribe(
                (response) => {
                    if (response.statusCode === StatusCode.Created) {
                        this.messageService.add({ key: 'toast', severity: 'success', summary: 'The employee is associated with your company successfully', detail: response.exceptionMessage });
                        this.employeeAdded.emit();
                        this.closeAddDialogEvent.emit(false);
                        this.loading = false;
                    }
                    else if (response.statusCode === StatusCode.Conflict) {
                        this.messageService.add({ key: 'toast', severity: 'error', summary: 'Conflict', detail: 'Employee is already associated with the specified group in the same company.' });
                        this.closeAddDialogEvent.emit(false);
                        this.loading = false;
                    } else {
                        this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Associate Employee to Company', detail: 'An unexpected error occurred while associating employee to company.' });
                        this.loading = false;
                    }
                },
                (error) => {
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error', detail: 'An unexpected error occurred while calling the backend service.' });
                    this.closeAddDialogEvent.emit(false);
                    this.loading = false;
                }
            );
        } else if (this.mode === 'edit') {


            const phoneNumberControl = this.associationFormGroup.get('phonenumber');
            if (phoneNumberControl) {
                phoneNumberControl.clearValidators();
            }
            if (!this.employee.groupId) {
                this.employee.groupId = this.associationFormGroup.get('group')?.value.value;
            }
            if (this.userId) {
                this.employee.userId = this.userId;
            }

            this.companyService.UpdateEmployeeAssociation(this.employee).subscribe(
                (response) => {
                    if (response.statusCode === StatusCode.Ok) {

                        this.messageService.add({ key: 'toast', severity: 'success', summary: 'Employee updated successfully', detail: response.exceptionMessage });
                        this.employeeAdded.emit();
                        this.loading = false;
                        this.closeAddDialogEvent.emit(false);

                    }
                    else if (response.statusCode === StatusCode.Conflict) {
                        this.messageService.add({ key: 'toast', severity: 'error', summary: 'Conflict', detail: 'Employee is already associated with the specified group in the same company.' });
                        this.loading = false;
                        this.closeAddDialogEvent.emit(false);
                    }
                    else {
                        this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error Update Employee', detail: response.exceptionMessage });
                        this.loading = false;
                        this.closeAddDialogEvent.emit(false);
                    }
                },
                (error) => {
                    this.messageService.add({ key: 'toast', severity: 'error', summary: 'Error', detail: 'An unexpected error occurred while calling the backend service.' });
                    this.loading = false;
                }
            );
        }
    }
    onStatusChange(event: any) {
        this.selectedStatus = event.value;
    }
    onUserChange(event: any) {
        this.selectedUser = event;
    }



    closeAddDialog() {
        this.closeAddDialogEvent.emit(false);
    }
}
