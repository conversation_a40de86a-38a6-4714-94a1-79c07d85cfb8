import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { ResponseAPI } from '../shared/models/ResponseAPI';
import { environment } from 'src/environments/environment';
import { Ticket } from '../shared/models/ticket/ticket';
import { WalletType } from '../shared/enums/wallet-type';
import { FilterMetadata } from 'primeng/api';

@Injectable({
  providedIn: 'root'
})
export class TicketService {
  constructor(private http: HttpClient) {

  }
  getAllTickets() {
    return this.http.get<ResponseAPI<Ticket[]>>(`${environment.API}/Ticket/GetAll`);
  }

  getAllPaginationTickets(pageSize: number, pageNumber: number, sortBy: string, sortDirection: number, filters: { [s: string]: FilterMetadata } | undefined) {
    let simplifiedFilters: { [s: string]: string } = {};
        if (filters) {
            for (const field in filters) {
                if (Object.prototype.hasOwnProperty.call(filters, field)) {
                    const filterValues = filters[field];

                    if (filterValues !== undefined && Array.isArray(filterValues) && filterValues.length > 0) {
                        const filterValue = filterValues[0]?.value;

                        if (filterValue !== undefined && filterValue !== null) {
                            simplifiedFilters[field] = filterValue.toString();
                        }
                    } else {
                        const filterValue = filterValues?.value;

                        if (filterValue !== undefined && filterValue !== null) {
                            simplifiedFilters[field] = filterValue.toString();
                        }
                    }
                }
            }
        }

        const requestBody = {
            pageSize,
            pageNumber,
            sortBy: sortBy.toString(),
            sortDirection: sortDirection.toString(),
            filters: simplifiedFilters,
        };
    return this.http.post<ResponseAPI<Ticket[]>>(`${environment.API}/Ticket/GetAllPaged`, requestBody,{  observe: 'response', responseType: 'json' });
  }

  getTicketsByType(type: WalletType) {
    return this.http.get<ResponseAPI<Ticket[]>>(`${environment.API}/Ticket/GetTicketsByType/${type}`);
  }

  getTicketById(id: string) {
    return this.http.get<ResponseAPI<Ticket>>(`${environment.API}/Ticket/Get/${id}`);
  }

  addTicket(ticket: Ticket) {
    return this.http.post<ResponseAPI<Ticket>>(`${environment.API}/Ticket/Create`, ticket);
  }

  updateTicket(ticket: Ticket) {
    return this.http.put<ResponseAPI<Ticket>>(`${environment.API}/Ticket/Update`, ticket);
  }

  deleteTicket(id: string) {
    return this.http.delete<ResponseAPI<void>>(`${environment.API}/Ticket/Delete/${id}`);
  }

}
