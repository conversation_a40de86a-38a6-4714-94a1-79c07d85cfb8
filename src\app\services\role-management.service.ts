import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Role, RoleRequest } from '../shared/models/role/role';
import { Permission } from '../shared/models/role/permission';
import { TreeNode } from 'primeng/api/treenode';
import {map} from 'rxjs'
import { ResponseAPI } from '../shared/models/ResponseAPI';
import { environment } from 'src/environments/environment';
import { FilterMetadata } from 'primeng/api';

@Injectable({
  providedIn: 'root'
})
export class RoleManagementService {

  constructor(private _http: HttpClient) { }

  //--------------------Roles

getAllRoles() {
  
    return this._http.get<ResponseAPI<Role[]>>(`${environment.API}/Role/GetAll`);
  }
getAllPagedRoles(pageSize: number, pageNumber: number, sortBy: string, sortDirection: number, filters: { [s: string]: FilterMetadata } | undefined) {
    let simplifiedFilters: { [s: string]: string } = {};
    if (filters) {
        for (const field in filters) {
            if (Object.prototype.hasOwnProperty.call(filters, field)) {
                const filterValues = filters[field];

                if (filterValues !== undefined && Array.isArray(filterValues) && filterValues.length > 0) {
                    const filterValue = filterValues[0]?.value;

                    if (filterValue !== undefined && filterValue !== null) {
                        simplifiedFilters[field] = filterValue.toString();
                    }
                } else {
                    const filterValue = filterValues?.value;

                    if (filterValue !== undefined && filterValue !== null) {
                        simplifiedFilters[field] = filterValue.toString();
                    }
                }
            }
        }
    }

    const requestBody = {
        pageSize,
        pageNumber,
        sortBy: sortBy.toString(),
        sortDirection: sortDirection.toString(),
        filters: simplifiedFilters,
    };
    return this._http.post<ResponseAPI<Role[]>>(`${environment.API}/Role/GetAllPaged`,requestBody, { observe: 'response', responseType: 'json' });
  }
  getRoleById(id: string) {
    return this._http.get<ResponseAPI<Role>>(`${environment.API}/Role/Get/${id}`);
  }

  addRole(role: RoleRequest){
    return this._http.post<ResponseAPI<Role>>(`${environment.API}/Role/Create`, role);
  }

  updateRole(role: RoleRequest){
    return this._http.patch<ResponseAPI<Role>>(`${environment.API}/Role/Update`, role);
  }

  deleteRole(id: string) {
    return this._http.delete<ResponseAPI<void>>(`${environment.API}/Role/Delete/${id}`);
  }

  //---------------------PermissionsByUser
  getAvailablePermissionsByUser() {
    return this._http.get<ResponseAPI<Permission[]>>(`${environment.API}/Permission/GetAllAvailableUserPermissions`);
  }
}
