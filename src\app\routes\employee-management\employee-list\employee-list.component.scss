.image-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.image-container img {
    max-height: 100px;
    /* Adjust the max-height to control when the text goes next to the image */
}

.image-container span {
    margin-top: 10px;
    /* Add spacing between the image and text when displayed below */
}

td img{
    display: block;
    margin-left: auto;
    margin-right: auto;
    max-height: 50px;   
}