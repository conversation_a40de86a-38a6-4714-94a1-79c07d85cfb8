export interface Environment {
  production: boolean;
  environmentName: string;
  
  // Configuration API
  API: string;
  NotificationAPI: string;
  WebAPI: string;
  
  // Configuration de stockage
  S3Url: string;
  
  // Configuration de routage
  baseUrl: string;
  useHash: boolean;
  
  // Configuration de logging
  enableLogging: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  
  // Configuration de cache
  cacheTimeout: number;
  
  // Configuration de sécurité
  tokenRefreshBuffer: number;
  maxRetryAttempts: number;
  
  // Configuration de l'interface
  defaultPageSize: number;
  maxPageSize: number;
  
  // Configuration des features
  features: {
    enableAdvancedDashboard: boolean;
    enableAuditLogs: boolean;
    enableNotifications: boolean;
    enableExports: boolean;
  };
}
