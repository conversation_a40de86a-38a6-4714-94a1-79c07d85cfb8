export enum InvoiceStatus {
    Valid,
    failed,
    InProgress,
    TransactionSucceeded,
    TransactionFailed,
    PDFGenerated,
    PDFFailed,
    EmailSent,
    EmailFailed
}

export const InvoiceStatusString: { [key: number]: string } = {
    [InvoiceStatus.Valid]: 'Valid',
    [InvoiceStatus.InProgress]: 'In Progress',
    [InvoiceStatus.failed]: 'Failed',
    [InvoiceStatus.TransactionSucceeded]: 'Transaction Succeeded',
    [InvoiceStatus.TransactionFailed]: 'Transaction Failed',
    [InvoiceStatus.PDFGenerated]: 'PDF Generated',
    [InvoiceStatus.PDFFailed]: 'PDF Failed',
    [InvoiceStatus.EmailSent]: 'Email Sent',
    [InvoiceStatus.EmailFailed]: 'Email Failed',
};
