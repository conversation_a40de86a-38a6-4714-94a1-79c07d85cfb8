<p-dialog [(visible)]="display" [modal]="true" (onHide)="onCancel()" class="confirmation-dialog" [closable]="false">
    <ng-template pTemplate="content">
        <div class="flex flex-column align-items-center p-2 surface-overlay border-round">
            <div class="circle-container">
                <div class="circle-icon">
                    <i class="pi pi-question text-5xl"></i>
                </div>
            </div>
            <span class="font-bold text-2xl block mb-2 mt-4">Are you sure?</span>
            <p class="mb-2">Please confirm to proceed sending money to <b>{{selectedEmployee?.user?.fullName}}</b>.</p>

            <form [formGroup]="ticketFormGroup">

            <div>
                <div class="zero-padding col-6 mb-6 lg:col-6 lg:mb-0 ">
                    <h5>Tickets <span style="color: red;">*</span></h5>
                    <p-multiSelect [options]="tickets" (onChange)="onTicketValueChange($event)" formControlName="tickets"
                        placeholder="Select tickets" optionLabel="name" class="multiselect-custom" display="chip"
                        [style]="{ 'width': '100%' }" [showClear]="true">
    
                    </p-multiSelect>
                    <p-message severity="error" text="Ticket is required"
                        *ngIf="ticketFormGroup.get('tickets')?.hasError('required') && ticketFormGroup.get('tickets')?.touched">
                    </p-message>
                </div>    
                <div  class="overflow-x-auto"   >
                    <table class="w-full" style="border-collapse: collapse; table-layout: auto">
                        <thead *ngIf="ticketFormGroup.get('tickets')?.value?.length > 0">
                            <tr >
                                <th class="text-left font-semibold py-3 border-bottom-1 surface-border white-space-nowrap">
                                    Name</th>
                                <th
                                    class="text-right font-semibold py-3 border-bottom-1 surface-border white-space-nowrap px-3">
                                    Quantity</th>
                                <th
                                    class="text-right font-semibold py-3 border-bottom-1 surface-border white-space-nowrap px-3">
                                    Unit Price</th>
                                <th class="text-right font-semibold py-3 border-bottom-1 surface-border white-space-nowrap">
                                    Total Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let ticket of ticketFormGroup.get('tickets')?.value">
                                <td class="text-left py-3 border-bottom-1 surface-border white-space-nowrap">
                                    {{ticket.name}}
                                </td>
                                <td class="text-right py-3 border-bottom-1 surface-border px-3">{{ticket.quantity}}</td>
                                <td class="text-right py-3 border-bottom-1 surface-border px-3">{{ticket.amount}}</td>
                                <td class="text-right py-3 border-bottom-1 surface-border">{{ticket.totalAmount}}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div> 
        </form>
            <div>
                <button pButton type="button" label="Cancel" (click)="onCancel()" class="p-button-outlined cancel-button"></button>
                <button pButton type="button" [disabled]="ticketFormGroup.get('tickets')?.hasError('required')"
                label="Confirm" (click)="confirmSend(ticketFormGroup.get('tickets')?.value)" 
                class="p-button-outlined confirm-button"[ngClass]="{ 'loading': loading }">
                <span *ngIf="!loading">Confirm</span>
                <i *ngIf="loading" class="pi pi-spin pi-spinner loader"></i>
            </button>   
            </div>
        </div>
    </ng-template>
</p-dialog>