import { Injectable } from '@angular/core';
import { ActivatedRoute, ActivatedRouteSnapshot, CanActivate, Router } from '@angular/router';
import { LocalStoreService } from './services/local-store.service';

@Injectable({
    providedIn: 'root'
})
export class ParentAuthGuard implements CanActivate {

    constructor(private router: Router, private route: ActivatedRoute, private localStorageService: LocalStoreService) { }

    canActivate(route: ActivatedRouteSnapshot): boolean {
        const isLoggedIn = !!localStorage.getItem('Token');
        const isDeviceVerified = localStorage.getItem('IsDeviceVerified') === 'true';
        if (isLoggedIn && isDeviceVerified) {
            return true;
        } else if (isLoggedIn && !isDeviceVerified && route.url.map(segment => segment.path).indexOf('verif') === -1) {
            this.router.navigate(['/auth/login/verif'], { queryParams: { returnUrl: route.url } });
            return false;
        } else {
            this.router.navigate(['/auth/login']);
            return false;
        }
    }
}
