import { OnInit } from '@angular/core';
import { Component } from '@angular/core';
import { EntrepriseType } from '../shared/enums/entreprise-type';
import { LocalStoreService } from 'src/app/services/local-store.service';
import { tokenDTO } from '../shared/models/authentification/token';
import jwtDecode from 'jwt-decode';
import { bo } from '@fullcalendar/core/internal-common';
import { UserType } from '../shared/enums/UserType';

@Component({
    selector: 'app-menu',
    templateUrl: './app.menu.component.html'
})
export class AppMenuComponent implements OnInit {

    model: any[] = [];
    entrepriseType: any;
    userType = "";
    constructor(private localStorageService: LocalStoreService) {

    }
    ngOnInit() {
        this.entrepriseType = localStorage.getItem('EntrepriseType');
        let token = this.localStorageService.getData("Token");
        if (token != undefined) {
            let decodedToken: tokenDTO = jwtDecode(token);
            // console.log("decodedToken", decodedToken.Role);
            // let Roles = decodedToken.Role;
            // if (!Array.isArray(Roles)) {
            //     Roles = [Roles]
            // }

            // if (Roles.includes("SuperAdmin")) {
            //     this.userType = UserType[UserType.SuperAdmin].toString().toLowerCase();
            // }
            // else if (Roles.find(x => x == "Admin")) {
            //     this.userType = UserType[UserType.Company].toString().toLowerCase();
            // }
            // else if (Roles.find(x => x == "Shop Admin")) {
            //     this.userType = UserType[UserType.ShopOwner].toString().toLowerCase();
            // }
            this.userType = decodedToken.UserType.toLowerCase();
        }
        console.log("usertype", this.userType)
        this.model = [
            {
                label: 'Dashboards',
                icon: 'pi pi-home',
                items: [
                    {
                        label: 'Overview',
                        icon: 'pi pi-fw pi-home',
                        routerLink: ['Dashboard/OverviewS'],
                        canActivate: ["superadmin"]
                    },
                    {
                        label: 'Overview',
                        icon: 'pi pi-fw pi-image',
                        routerLink: ['Dashboard/Overview'],
                        canActivate: ["company", "shopowner"]
                    },
                    {
                        label: 'Logging Management',
                        icon: 'pi pi-fw pi-wrench',
                        routerLink: ['Dashboard/Logging'],
                        canActivate: ["superadmin"]
                    }
                ]
            },
            {
                label: 'Apps',
                icon: 'pi pi-th-large',
                items: [
                    {
                        label: 'Company Management',
                        icon: 'pi pi-fw pi-building',
                        items: [

                            {
                                label: 'Companies',
                                icon: 'pi pi-fw pi-building',
                                routerLink: ['/apps/companies'],
                                canActivate: ["superadmin"]
                            },
                            {
                                label: 'Groups',
                                icon: 'pi pi-fw pi-users',
                                routerLink: ['/apps/groups'],
                                canActivate: ["company"]
                            },
                            {
                                label: 'Employees',
                                icon: 'pi pi-fw pi-users',
                                routerLink: ['/apps/employees'],
                                canActivate: ["company"]
                            },
                            {
                                label: 'Tickets',
                                icon: 'pi pi-fw pi-users',
                                routerLink: ['/apps/tickets'],
                                canActivate: ["company"]
                            },
                        ]
                    },
                    {
                        label: 'Cashier Management',
                        icon: 'pi pi-fw pi-building',
                        items: [
                            {
                                label: 'Cashiers',
                                icon: 'pi pi-fw pi-users',
                                routerLink: ['/apps/cashiers'],
                                canActivate: ["shopowner"]
                            },
                            {
                                label: 'Transactions',
                                icon: 'pi pi-fw pi-qrcode',
                                routerLink: ['apps/shopownerTransaction'],
                                canActivate: ["shopowner"]
                            },
                            {
                                label: 'Refund',
                                icon: 'pi pi-fw pi-refresh',
                                routerLink: ['apps/shopownerTransaction/refund'],
                                canActivate: ["shopowner"]
                            },
                        ]
                    },
                    {
                        label: 'Resource Management',
                        icon: 'pi pi-fw pi-cog',
                        items: [

                            {
                                label: 'Roles',
                                icon: 'pi pi-fw pi-key',
                                routerLink: ['/apps/roles'],
                                canActivate: ["superadmin", "company", "shopowner"]
                            },
                            {
                                label: 'Users',
                                icon: 'pi pi-fw pi-users',
                                routerLink: ['users'],
                                canActivate: ["superadmin", "company", "shopowner"]
                            }
                        ]
                    },
                    {
                        label: 'Transfer Request',
                        icon: 'pi pi-fw pi-dollar',
                        items: [

                            {
                                label: 'New Request',
                                icon: 'pi pi-fw pi-plus',
                                routerLink: ['/apps/newRequest'],
                                queryParams: { requestType: 'New' },
                                canActivate: ["superadmin", "company"]
                            },
                            {
                                label: 'Valid Request',
                                icon: 'pi pi-fw pi-check',
                                routerLink: ['/apps/validRequest'],
                                queryParams: { requestType: 'Valid' },
                                canActivate: ["superadmin", "company"]
                            },
                            {
                                label: 'Failed Request',
                                icon: 'pi pi-fw pi-times',
                                routerLink: ['/apps/failedRequest'],
                                queryParams: { requestType: 'Failed' },
                                canActivate: ["superadmin", "company"]
                            }
                        ]
                    },
                    {
                        label: 'Cashback Request',
                        icon: 'pi pi-fw pi-dollar',
                        items: [

                            {
                                label: 'New Request',
                                icon: 'pi pi-fw pi-plus',
                                routerLink: ['/apps/newCashbackRequest'],
                                queryParams: { requestType: 'New' },
                                canActivate: ["superadmin", "shopowner"]
                            },
                            {
                                label: 'Valid Request',
                                icon: 'pi pi-fw pi-check',
                                routerLink: ['/apps/validCashbackRequest'],
                                queryParams: { requestType: 'Valid' },
                                canActivate: ["superadmin", "shopowner"]
                            },
                            {
                                label: 'Failed Request',
                                icon: 'pi pi-fw pi-times',
                                routerLink: ['/apps/failedCashbackRequest'],
                                queryParams: { requestType: 'Failed' },
                                canActivate: ["superadmin", "shopowner"]
                            }
                        ]
                    },
                    {
                        label: 'Invoices',
                        icon: 'pi pi-fw pi-money-bill',
                        items: [

                            {
                                label: 'Failed Invoices',
                                icon: 'pi pi-fw pi-times',
                                routerLink: ['/apps/failedInvoices'],
                                queryParams: { requestType: 'Failed' },
                                canActivate: ["superadmin"]
                            },
                            {
                                label: 'Valid Invoices',
                                icon: 'pi pi-fw pi-check',
                                routerLink: ['/apps/validInvoices'],
                                queryParams: { requestType: 'Valid' },
                                canActivate: ["superadmin"]
                            }
                        ]
                    }
                ]
            },
            {
                label: 'Motiva',
                icon: 'pi pi-fw pi-star-fill',
                items: [{
                    label: 'Insurance',
                    icon: 'pi pi-fw pi-shield',
                    routerLink: ['/apps/kanban'],
                    canActivate: ["superadmin", "company"]
                },
                {
                    label: 'Team Building',
                    icon: 'pi pi-fw pi-th-large',
                    routerLink: ['/uikit/formlayout'],
                    canActivate: ["superadmin", "company"]
                },
                {
                    label: 'Transport',
                    icon: 'pi pi-fw pi-car',
                    routerLink: ['/uikit/input'],
                    canActivate: ["superadmin", "company"]
                }
                ]
            }
        ]
        this.model = this.filterMenuItemsByUserType(this.model, this.userType);
        console.log("model", this.model)
        console.log("usertype", this.userType)
    }
    filterMenuItemsByUserType(menuItems: any[], userType: string): any[] {
        return menuItems.reduce((filteredItems, item) => {
            // Check if item has canActivate property and if it includes the user type
            if (item.canActivate && item.canActivate.includes(userType)) {
                // Clone the item to avoid mutation
                const filteredItem = { ...item };

                // Check if the item has sub-items and filter them recursively
                if (item.items) {
                    filteredItem.items = this.filterMenuItemsByUserType(item.items, userType);
                }

                filteredItems.push(filteredItem);
            } else if (!item.canActivate && item.items) {
                // If the parent item doesn't have canActivate but has sub-items, filter them recursively
                const filteredSubItems = this.filterMenuItemsByUserType(item.items, userType);

                // Include the parent item in the result if there are filtered sub-items
                if (filteredSubItems.length > 0) {
                    const filteredItem = { ...item, items: filteredSubItems };
                    filteredItems.push(filteredItem);
                }
            }

            return filteredItems;
        }, []);
    }
}
