import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RoleListComponent } from './role-list/role-list.component';
import { RoleManagementRoutingModule } from './role-managemen-routing.module';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { ToggleButtonModule } from 'primeng/togglebutton';
import { RippleModule } from 'primeng/ripple';
import { MultiSelectModule } from 'primeng/multiselect';
import { DropdownModule } from 'primeng/dropdown';
import { ProgressBarModule } from 'primeng/progressbar';
import { ToastModule } from 'primeng/toast';
import { SliderModule } from 'primeng/slider';
import { RatingModule } from 'primeng/rating';
import { DialogModule } from 'primeng/dialog';
import { TreeModule } from 'primeng/tree';
import { MessageService, ConfirmationService } from 'primeng/api';
import { RoleAddEditComponent } from './role-add-edit/role-add-edit.component';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from 'src/app/shared/Modules/shared-module/shared-module.module';
import { ConfirmationDialogueModule } from "../../shared/components/confirmation-dialog/confirmation-dialogue.module";
import { FormsModule } from '@angular/forms';
import { UserService } from 'src/app/services/User.service';
import { TooltipModule } from 'primeng/tooltip';
import { ProgressSpinnerModule } from 'primeng/progressspinner';

@NgModule({
    declarations: [
        RoleListComponent,
        RoleAddEditComponent
    ],
    providers: [MessageService, ConfirmationService, UserService],
    imports: [
        CommonModule,
        SharedModule,
        ReactiveFormsModule,
        RoleManagementRoutingModule,
        TableModule,
        RatingModule,
        ButtonModule,
        SliderModule,
        InputTextModule,
        ToggleButtonModule,
        RippleModule,
        MultiSelectModule,
        DropdownModule,
        ProgressBarModule,
        ToastModule,
        DialogModule,
        TreeModule,
        OverlayPanelModule,
        ConfirmationDialogueModule,
        FormsModule,
        TooltipModule,
        ProgressSpinnerModule
    ],
    exports: []
})
export class RoleManagementModule { }
