import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CashbackListComponent } from './cashback-list/cashback-list.component';
import { CashbackAddEditComponent } from './cashback-add-edit/cashback-add-edit.component';
import { CashbackManagementRoutingModule } from './cashback-management-routing.module';
import { SharedModule } from 'src/app/shared/Modules/shared-module/shared-module.module';
import { FormsModule } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { ToggleButtonModule } from 'primeng/togglebutton';
import { RippleModule } from 'primeng/ripple';
import { MultiSelectModule } from 'primeng/multiselect';
import { DropdownModule } from 'primeng/dropdown';
import { ProgressBarModule } from 'primeng/progressbar';
import { ToastModule } from 'primeng/toast';
import { SliderModule } from 'primeng/slider';
import { RatingModule } from 'primeng/rating';
import { ConfirmationDialogueModule } from 'src/app/shared/components/confirmation-dialog/confirmation-dialogue.module';
import { ReactiveFormsModule } from '@angular/forms';
import { DialogModule } from 'primeng/dialog';
import { MessageModule } from 'primeng/message';;
import { InputSwitchModule } from 'primeng/inputswitch';
import { CalendarModule } from "primeng/calendar";
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ProgressSpinnerModule } from 'primeng/progressspinner';import { TooltipModule } from 'primeng/tooltip';


@NgModule({
    declarations: [
        CashbackListComponent,
        CashbackAddEditComponent

    ],
    imports: [
        CommonModule,
        CashbackManagementRoutingModule,
        SharedModule,
        FormsModule,
        TableModule,
        RatingModule,
        ButtonModule,
        SliderModule,
        InputTextModule,
        ToggleButtonModule,
        RippleModule,
        MultiSelectModule,
        DropdownModule,
        ProgressBarModule,
        ToastModule,
        ConfirmationDialogueModule,
        ReactiveFormsModule,
        DialogModule,
        CommonModule,
        ReactiveFormsModule,
        TableModule,
        RatingModule,
        ButtonModule,
        SliderModule,
        InputTextModule,
        ToggleButtonModule,
        RippleModule,
        MultiSelectModule,
        DropdownModule,
        ProgressBarModule,
        DialogModule,
        MessageModule,
        InputSwitchModule,
        CalendarModule,
        OverlayPanelModule,
        ConfirmDialogModule,
        ConfirmPopupModule,
        ProgressSpinnerModule,
        TooltipModule
    ],
    providers: [ConfirmationService, MessageService]
})
export class CashbackManagementModule { }
