import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { LoginComponent } from './login/login.component';
import { OtpCodeComponent } from './otp-code/otp-code.component';
import { ForgetPasswordComponent } from './forget-password/forget-password.component';
import { ResetPasswordComponent } from './reset-password/reset-password.component';
import { MailConfirmedComponent } from './confirm/mail-confirmed/mail-confirmed.component';



@NgModule({
  imports: [RouterModule.forChild([
      {path: 'auth/login', component: LoginComponent },
      {path:'auth/login/verif',component:OtpCodeComponent},
      {path:'auth/forgetpassword',component:ForgetPasswordComponent},
      {path:'auth/resetpassword',component:ResetPasswordComponent},
      {path:'auth/MailConfirmed',component:MailConfirmedComponent}
  ])],
  exports: [RouterModule]
})
export class AuthentificationRoutingModule { }
