<div class="col-12">
    <div class="card">
        <p-toast></p-toast>
        <p-table #rolesTable [value]="roles" dataKey="id" [expandedRowKeys]="expandedRows" responsiveLayout="scroll"
            [rows]="pageSize" [loading]="loading" [rowHover]="true" [paginator]="true" [lazy]="true"
            (onLazyLoad)="lazyLoadRoles($event)" [totalRecords]="totalRecords" [first]="first"
            [globalFilterFields]="['name','creationTime','status']" responsiveLayout="scroll">
            <ng-template pTemplate="caption">
                    <p-button pRipple type="button" [outlined]="true" icon="pi pi-fw {{isExpanded ? 'pi-minus' : 'pi-plus'}}"
                    label="{{isExpanded ? 'Collapse All' : 'Expand All'}}" (click)="expandAll()"[pTooltip]="'Expand Or Collapse Data'"
                    [style]="{'margin-right.px': '10'}" styleClass="p-button-outlined ">
                    <span class="tooltip" ></span>
                  </p-button>

                <p-button  pRipple type="button" [outlined]="true" icon="pi pi-fw pi-plus" label="Add"
                    (click)="displayRoleDialog()" [pTooltip]="'Add New Role'" styleClass="p-button-outlined "
                    [style]="{'margin-right.px': '10'}"></p-button>
                <app-role-add-edit [roleId]="roleId" [display]="displayAddEditDialog"
                    (closeAddDialogEvent)="closeAddDialogEvent($event)"
                    (UpdateOrAddConfirmedDialogEvent)="updateConfirmed($event)"></app-role-add-edit>
                <p-button pRipple [outlined]="true" icon="pi pi-filter-slash" label="Clear"
                (click)="clear(rolesTable)" [pTooltip]="'Clear All Filters'" styleClass="p-button-outlined "
                [style]="{'margin-right.px': '10'}"></p-button>

            </ng-template>
            <ng-template pTemplate="header">
                <tr>
                    <th style="width: 3rem"></th>

                    <th style="min-width: 12rem">
                        <div class="flex justify-content-between align-items-center">
                            <span>Name</span>
                            <div class="flex align-items-center">
                              <p-sortIcon field="name" pTooltip="Sort Data" pTooltipPosition="right" pTooltipStyleClass="custom-tooltip"></p-sortIcon>
                              <p-columnFilter pTooltip="Filter Data" type="text" field="Name" display="menu" placeholder="Search by name"></p-columnFilter>
                          </div>
                      </div>
                    </th>



                    <th style="min-width: 10rem" pSortableColumn="status">
                        <div class="flex justify-content-between align-items-center">
                            userType <!--<p-sortIcon field="UserType"></p-sortIcon>
                            <p-columnFilter field="UserType" matchMode="equals" display="menu">
                                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                    <p-dropdown [ngModel]="value" [options]="UserTypeList"
                                        (onChange)="filter($event.value)" placeholder="Any"
                                        [style]="{'min-width': '12rem'}">
                                        <ng-template let-option pTemplate="item">
                                            <span>{{option.label}}</span>
                                        </ng-template>
                                    </p-dropdown>
                                </ng-template>
                            </p-columnFilter>-->
                        </div>
                    </th>
                    <th pSortableColumn="creationTime ">Creation Date
                        <!-- <p-sortIcon field="creationTime"></p-sortIcon>
                        <p-columnFilter type="date" field="CreationDate" display="menu"
                            placeholder="Search by Creation Date"></p-columnFilter>-->
                    </th>
                    <th style="min-width: 10rem" pSortableColumn="status">
                        <div class="flex justify-content-between align-items-center">
                            Status <!--<p-sortIcon field="Status"></p-sortIcon>
                            <p-columnFilter field="Status" matchMode="equals" display="menu">
                                <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                    <p-dropdown [ngModel]="value" [options]="StatusList"
                                        (onChange)="filter($event.value)" placeholder="Any"
                                        [style]="{'min-width': '12rem'}">
                                        <ng-template let-option pTemplate="item">
                                            <span
                                                [class]="'customer-badge status-' + option.value">{{option.label}}</span>
                                        </ng-template>
                                    </p-dropdown>
                                </ng-template>
                            </p-columnFilter>-->
                        </div>
                    </th>
                    <th>Actions</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-role let-expanded="expanded">
                <tr>
                    <td>
                        <button type="button" pButton pRipple [pRowToggler]="role"
                            class="p-button-text p-button-rounded p-button-plain"
                            [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"></button>
                    </td>
                    <td style="min-width: 12rem;">{{role.name}}</td>
                    <td style="min-width: 12rem;">{{getUserTypeString(role.userType)}}</td>
                    <td style="min-width: 10rem;">{{role.creationTime | dateTimeToDate}}</td>
                    <td><span [class]="'component-badge status-'+ (role.status | enumToString)">{{role.status
                            |enumToString}}</span></td>
                    <td class="container">
                        <!-- <i class="pi pi-pencil btn-edit-padding" style="color: rgb(42, 184, 73);font-size: 1.5rem" (click)="expandAll()"></i>
                        <i class="pi pi-trash btn-delete-padding" style="color: rgb(211, 32, 32);font-size: 1.5rem"></i> -->


                        <p-button pRipple type="button" [outlined]="true" icon="pi pi-pencil" (onClick)="editRole(role.id)"
                        [pTooltip]="'Edit Role'" styleClass="p-button-outlined" [style]="{'margin-right.px': 10}"
                        [disabled]="isClientOrSuperAdminRole(role.name)">
                        <span class="tooltip"></span>
                      </p-button>
                      <p-button pRipple type="button" [outlined]="true" icon="pi pi-trash"
                        (onClick)="showDeleteConfirmation(role.id)"[pTooltip]="'Delete Role'" styleClass="p-button-outlined "
                        [disabled]="isClientOrSuperAdminRole(role.name)">
                        <span class="tooltip"></span>
                      </p-button>

                        <!--add ngif role is not attributed to any user-->
                            <app-add-confirmation [display]="displayDeleteDialog" (confirm)="confirmDelete()" (cancelDelete)="onCancelDelete()"
                            (elementDeleted)="onElementDeleted()"></app-add-confirmation>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="rowexpansion" let-role>
                <tr>
                    <td colspan="7">
                        <div class="p-3">
                            <p-tree layout="horizontal" [value]="permissionsByRole[role.id].permissions"
                                selectionMode="checkbox" [selection]="permissionsByRole[role.id].selectedPermissions">
                                <ng-template pTemplate="empty">
                <tr>
                    <td colspan="6" style="padding-left: 20px;"> There are no permissions for this role yet.</td>
                </tr>
            </ng-template>
            </p-tree>
    </div>
    </td>
    </tr>
    </ng-template>
    </p-table>
    <p-toast key="toast"></p-toast>
</div>
</div>
